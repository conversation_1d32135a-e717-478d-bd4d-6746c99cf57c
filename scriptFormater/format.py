import json
import os
import sys
import glob
from typing import List, Dict, Any

def read_json_file(file_path: str) -> Dict[str, Any]:
    """读取JSON文件"""
    with open(file_path, 'r', encoding='utf-8') as f:
        return json.load(f)

def write_json_file(data: Dict[str, Any], file_path: str):
    """写入JSON文件"""
    with open(file_path, 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=2)

def remove_all_clicks(events: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """移除所有点击事件"""
    return [event for event in events if event.get('a') != 'c']

def remove_clicks_within_30s(events: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """每30秒最多保留一个点击事件"""
    filtered_events = []
    last_click_time = -31000  # 初始化为-31秒，确保第一个点击事件会被保留
    TIME_THRESHOLD = 30000  # 30秒 = 30000毫秒
    
    for event in events:
        if event.get('a') == 'c':
            current_time = event.get('t', 0)
            # 如果当前点击事件与上一个点击事件的时间差小于30秒，则跳过
            if current_time - last_click_time < TIME_THRESHOLD:
                continue
            filtered_events.append(event)
            last_click_time = current_time
        else:
            filtered_events.append(event)
    
    return filtered_events

def process_script(input_file: str, output_file: str, mode: int):
    """处理脚本文件"""
    # 确保输出目录存在
    os.makedirs(os.path.dirname(output_file), exist_ok=True)
    
    # 读取输入文件
    data = read_json_file(input_file)
    
    # 根据模式处理事件
    if mode == 1:
        data['e'] = remove_all_clicks(data['e'])
    elif mode == 2:
        data['e'] = remove_clicks_within_30s(data['e'])
    else:
        raise ValueError("无效的模式选择，请选择1或2")
    
    # 写入输出文件
    write_json_file(data, output_file)

def process_all_files(mode: int):
    """处理script_in目录下的所有JSON文件"""
    # 获取脚本所在目录的绝对路径
    script_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 构建输入和输出目录的绝对路径
    input_dir = os.path.join(script_dir, "script_in")
    output_dir = os.path.join(script_dir, "script_out")
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 获取所有JSON文件
    json_files = glob.glob(os.path.join(input_dir, "*.json"))
    
    if not json_files:
        print(f"在 {input_dir} 目录下没有找到JSON文件")
        return
    
    print(f"找到 {len(json_files)} 个JSON文件")
    
    # 处理每个文件
    for input_file in json_files:
        try:
            # 获取文件名
            file_name = os.path.basename(input_file)
            output_file = os.path.join(output_dir, file_name)
            
            print(f"正在处理: {file_name}")
            process_script(input_file, output_file, mode)
            print(f"处理完成: {file_name}")
            
        except Exception as e:
            print(f"处理文件 {file_name} 时出错: {str(e)}")
            continue

def main():
    if len(sys.argv) != 2 or sys.argv[1] not in ['1', '2']:
        print("使用方法: python format.py <mode>")
        print("mode: 1 - 移除所有点击事件")
        print("      2 - 30秒内最多保留一个点击事件")
        sys.exit(1)
    
    mode = int(sys.argv[1])
    
    try:
        process_all_files(mode)
        print("\n所有文件处理完成！")
    except Exception as e:
        print(f"处理过程中出现错误: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
