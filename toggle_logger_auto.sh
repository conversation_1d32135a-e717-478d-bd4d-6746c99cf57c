#!/bin/bash

# 定义颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# 定义文件路径
SDK_FILE="lib_webview_ad_sdk/src/main/java/ai/ad/webview/sdk/WSKSDK.java"
BACKUP_FILE="${SDK_FILE}.bak"

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO] $1${NC}"
}

log_warn() {
    echo -e "${YELLOW}[WARNING] $1${NC}"
}

log_error() {
    echo -e "${RED}[ERROR] $1${NC}"
}

# 函数：禁用日志
disable_logger() {
    # 检查文件是否存在
    if [ ! -f "$SDK_FILE" ]; then
        log_error "WSKSDK.java 文件不存在: $SDK_FILE"
        return 1
    fi

    # 创建备份
    cp "$SDK_FILE" "$BACKUP_FILE"
    if [ $? -ne 0 ]; then
        log_error "无法创建备份文件"
        return 1
    fi
    log_info "已创建备份文件: $BACKUP_FILE"

    # 注释掉日志代码
    log_info "正在注释掉日志注入代码..."
    sed -i '' 's/WSKLog.enable();/\/\/WSKLog.enable();/g' "$SDK_FILE"
    sed -i '' 's/_instance.injectLogger(logger);/\/\/_instance.injectLogger(logger);/g' "$SDK_FILE"

    # 检查是否成功修改
    if grep -q "//WSKLog.enable();" "$SDK_FILE" && grep -q "//_instance.injectLogger(logger);" "$SDK_FILE"; then
        log_info "日志注入代码已成功注释"
        return 0
    else
        log_error "注释日志注入代码失败"
        # 恢复备份
        cp "$BACKUP_FILE" "$SDK_FILE"
        rm "$BACKUP_FILE"
        return 1
    fi
}

# 函数：启用日志
enable_logger() {
    # 恢复原始代码
    log_info "正在恢复日志注入代码..."
    sed -i '' 's/\/\/WSKLog.enable();/WSKLog.enable();/g' "$SDK_FILE"
    sed -i '' 's/\/\/_instance.injectLogger(logger);/_instance.injectLogger(logger);/g' "$SDK_FILE"

    # 检查是否成功恢复
    if grep -q "WSKLog.enable();" "$SDK_FILE" && grep -q "_instance.injectLogger(logger);" "$SDK_FILE"; then
        log_info "日志注入代码已成功恢复"
        # 删除备份
        rm "$BACKUP_FILE"
        log_info "备份文件已删除"
        return 0
    else
        log_error "恢复日志注入代码失败，使用备份文件恢复"
        # 恢复备份
        cp "$BACKUP_FILE" "$SDK_FILE"
        rm "$BACKUP_FILE"
        return 1
    fi
}

# 主函数
main() {
    if [ "$1" == "disable" ]; then
        disable_logger
        return $?
    elif [ "$1" == "enable" ]; then
        enable_logger
        return $?
    elif [ "$1" == "run" ]; then
        # 禁用日志
        disable_logger
        if [ $? -ne 0 ]; then
            return 1
        fi
        
        # 执行命令
        shift
        log_info "执行命令: $@"
        "$@"
        SCRIPT_RESULT=$?
        
        # 无论脚本执行成功与否，都恢复日志
        enable_logger
        
        # 返回脚本执行结果
        if [ $SCRIPT_RESULT -eq 0 ]; then
            log_info "脚本执行成功"
        else
            log_warn "脚本执行返回代码: $SCRIPT_RESULT"
        fi
        
        return $SCRIPT_RESULT
    else
        log_error "用法: $0 {disable|enable|run 命令}"
        return 1
    fi
}

# 执行主函数
main "$@"
