import os
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from sqlalchemy.exc import SQLAlchemyError
import logging
from sshtunnel import SSHTunnelForwarder
import time

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DatabaseManager:
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(DatabaseManager, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        if self._initialized:
            return
            
        # SSH配置
        self.SSH_CONFIG = {
            'hostname': 'www.h5g.co',
            'username': 'ubuntu',
            'password': 'fF8TC62Yj7A6MZ67rBrD',
            'port': 22
        }
        
        # 数据库连接配置
        self.DB_CONFIG = {
            'host': '127.0.0.1',  # 通过SSH隧道转发
            'port': 3306,
            'user': 'webview_ad',
            'password': 'pvsfFP)4-3YBBjL~',
            'database': 't_event_tracking'  # 修改为正确的数据库名
        }
        
        self.engine = None
        self.Session = None
        self.tunnel = None
        self._initialized = True
        
    def _setup_ssh_tunnel(self):
        """建立SSH隧道"""
        try:
            self.tunnel = SSHTunnelForwarder(
                (self.SSH_CONFIG['hostname'], self.SSH_CONFIG['port']),
                ssh_username=self.SSH_CONFIG['username'],
                ssh_password=self.SSH_CONFIG['password'],
                remote_bind_address=('127.0.0.1', 3306),
                local_bind_address=('127.0.0.1', 3306)
            )
            self.tunnel.start()
            logger.info("[DatabaseManager] -> SSH隧道建立成功")
            return True
        except Exception as e:
            logger.error(f"[DatabaseManager] -> SSH隧道建立失败: {str(e)}")
            return False
        
    def connect(self):
        """建立数据库连接"""
        try:
            # 首先建立SSH隧道
            if not self._setup_ssh_tunnel():
                return False
            # 等待SSH隧道建立
            time.sleep(1)
            connection_string = f"mysql+pymysql://{self.DB_CONFIG['user']}:{self.DB_CONFIG['password']}@{self.DB_CONFIG['host']}:{self.DB_CONFIG['port']}/{self.DB_CONFIG['database']}"
            self.engine = create_engine(connection_string)
            self.Session = sessionmaker(bind=self.engine)
            logger.info("[DatabaseManager] -> 数据库连接成功")
            return True
        except SQLAlchemyError as e:
            logger.error(f"[DatabaseManager] -> 数据库连接失败: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"[DatabaseManager] -> 连接过程发生错误: {str(e)}")
            return False
            
    def execute_query(self, query, params=None):
        """执行SQL查询"""
        try:
            with self.engine.connect() as connection:
                result = connection.execute(text(query), params or {})
                return result.fetchall()
        except SQLAlchemyError as e:
            logger.error(f"[DatabaseManager] -> 查询执行失败: {str(e)}")
            return None
            
    def insert_event(self, table_name, data):
        """插入事件数据"""
        try:
            with self.Session() as session:
                query = f"INSERT INTO {table_name} ({', '.join(data.keys())}) VALUES ({', '.join([':' + k for k in data.keys()])})"
                session.execute(text(query), data)
                session.commit()
                logger.info(f"[DatabaseManager] -> 数据插入成功: {table_name}")
                return True
        except SQLAlchemyError as e:
            logger.error(f"[DatabaseManager] -> 数据插入失败: {str(e)}")
            return False
            
    def get_table_data(self, table_name, conditions=None, limit=100):
        """获取表数据"""
        try:
            query = f"SELECT * FROM {table_name}"
            if conditions:
                query += f" WHERE {conditions}"
            query += f" LIMIT {limit}"
            
            return self.execute_query(query)
        except SQLAlchemyError as e:
            logger.error(f"[DatabaseManager] -> 获取表数据失败: {str(e)}")
            return None

    def get_table_structure(self, table_name):
        """获取表结构"""
        try:
            query = f"DESCRIBE {table_name}"
            return self.execute_query(query)
        except SQLAlchemyError as e:
            logger.error(f"[DatabaseManager] -> 获取表结构失败: {str(e)}")
            return None

    def list_databases(self):
        """列出所有可用的数据库"""
        try:
            query = "SHOW DATABASES"
            return self.execute_query(query)
        except SQLAlchemyError as e:
            logger.error(f"[DatabaseManager] -> 获取数据库列表失败: {str(e)}")
            return None

    def __del__(self):
        if self.tunnel and self.tunnel.is_active:
            self.tunnel.stop()

# 使用示例
if __name__ == "__main__":
    db = DatabaseManager()
    if db.connect():
        # 首先列出所有数据库
        print("\n可用的数据库列表:")
        print("-" * 40)
        databases = db.list_databases()
        if databases:
            for db_name in databases:
                print(db_name[0])
        print("-" * 40)
        
        # 获取表结构
        table_name = "t_event_tracking_20240320"  # 根据实际日期修改
        structure = db.get_table_structure(table_name)
        if structure:
            print(f"\n表 {table_name} 的结构:")
            print("-" * 80)
            print(f"{'字段名':<20} {'类型':<20} {'空':<10} {'键':<10} {'默认值':<15} {'额外信息'}")
            print("-" * 80)
            for row in structure:
                field, type_, null, key, default, extra = row
                print(f"{field:<20} {type_:<20} {null:<10} {key:<10} {str(default):<15} {extra}")
            print("-" * 80)
