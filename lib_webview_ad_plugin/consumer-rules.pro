# ============================
# Plugin 模块混淆规则 (consumer-rules.pro)
# ============================

# 基本属性保留
-keepattributes *Annotation*                 # 保留注解
-keepattributes Signature                    # 保留泛型信息
-keepattributes SourceFile,LineNumberTable   # 保留源文件和行号
-keepattributes JavascriptInterface          # 保留JavaScript接口

# Android基本组件
-keepclassmembers class * implements android.os.Parcelable {
    public static final android.os.Parcelable$Creator *;
}

# 核心代理类保留 - 这些类是SDK与Plugin之间的桥梁
-keep public class ai.ad.webview.plugin.ProxyWSKSDK {
    public *;
}
-keep public class ai.ad.webview.plugin.webview.proxy.ProxyWSKService {
    public *;
}
-keep public class ai.ad.webview.plugin.webview.proxy.ProxyWSKJobService {
    public *;
}
-keep public class ai.ad.webview.plugin.webview.proxy.ProxyWSKActivity {
    public *;
}

# SDK API接口保留
-keep public interface ai.ad.webview.sdk.api.** { *; }
-keep public class ai.ad.webview.sdk.api.** { *; }

# 接口实现类保留
-keep class * implements ai.ad.webview.sdk.api.interfaces.** { *; }

# DeviceIdUtil优化 - 只保留必要的公共方法
-keep public class ai.ad.webview.plugin.utils.DeviceIdUtil {
    public static void preloadGAID(android.content.Context);
    public static java.lang.String getDeviceId();
    public static boolean isDeviceIdReady();
}

# JavaScript接口方法保留
-keepclassmembers class * {
    @android.webkit.JavascriptInterface <methods>;
}

# 忽略警告
-dontwarn android.view.**
-dontwarn android.webkit.**
-dontwarn com.google.android.gms.ads.identifier.**

# Google Play Services相关
-keep class com.google.android.gms.ads.identifier.AdvertisingIdClient { *; }
-keep class com.google.android.gms.ads.identifier.AdvertisingIdClient$Info { *; }

# 混淆设置
-repackageclasses 'wsk.plugin'
-useuniqueclassmembernames
-optimizations !code/simplification/arithmetic,!code/simplification/cast,!field/*,!class/merging/*
-optimizationpasses 5