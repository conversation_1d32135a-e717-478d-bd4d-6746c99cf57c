package ai.ad.webview.plugin.utils;

import android.content.Context;
import android.os.SystemClock;

public class DeviceUtil {

    private DeviceUtil() {
        // 私有构造函数，防止实例化
    }

    /**
     * 获取应用包名
     */
    public static String getPackageName(Context context) {
        return context.getPackageName();
    }

    /**
     * 获取系统启动至今的毫秒时间
     */
    public static long getSystemElapsedTime() {
        return SystemClock.elapsedRealtime();
    }
}
