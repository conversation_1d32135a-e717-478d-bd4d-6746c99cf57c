package ai.ad.webview.plugin.utils;

import android.os.Handler;
import android.os.Looper;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

import ai.ad.webview.plugin.repository.PostBodyExt;
import ai.ad.webview.sdk.logger.WSKLog;

public class HttpUtils {
    private static final int TIMEOUT = 30000;
    private static final Handler mainHandler = new Handler(Looper.getMainLooper());
    private static final String TAG = "HttpUtils";

    private HttpUtils() {
        // 私有构造函数，防止实例化
    }

    public static abstract class Result {
        public static class Success extends Result {
            private final JSONObject data;

            public Success(JSONObject data) {
                this.data = data;
            }

            public JSONObject getData() {
                return data;
            }
        }

        public static class Error extends Result {
            private final int code;
            private final String message;

            public Error(int code, String message) {
                this.code = code;
                this.message = message;
            }

            public int getCode() {
                return code;
            }

            public String getMessage() {
                return message;
            }
        }
    }

    public interface Callback {
        void onResult(Result result);
    }

    public static void get(String url, boolean encrypt, Callback callback) {
        new Thread(new Runnable() {
            @Override
            public void run() {
                long startTime = System.currentTimeMillis();
                String responseText = "";
                int responseCode = -1;

                try {
                    // 准备请求头
                    Map<String, String> headers;
                    if (encrypt) {
                        headers = new HashMap<>();
                        headers.put("Accept", "text/plain");
                    } else {
                        headers = new HashMap<>();
                        headers.put("Accept", "application/json");
                    }

                    // 创建连接
                    HttpURLConnection connection = (HttpURLConnection) new URL(url).openConnection();
                    connection.setRequestMethod("GET");
                    connection.setConnectTimeout(TIMEOUT);
                    connection.setReadTimeout(TIMEOUT);
                    for (Map.Entry<String, String> entry : headers.entrySet()) {
                        connection.setRequestProperty(entry.getKey(), entry.getValue());
                    }

                    // 获取响应码
                    responseCode = connection.getResponseCode();
                    JSONObject decryptedData = new JSONObject();
                    Result response;

                    if (responseCode >= 200 && responseCode < 300) {
                        // 读取成功响应
                        BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                        StringBuilder sb = new StringBuilder();
                        String line;
                        while ((line = reader.readLine()) != null) {
                            sb.append(line);
                        }
                        responseText = sb.toString();
                        reader.close();

                        if (encrypt) {
                            // 解密响应数据
                            try {
                                decryptedData = HttpDecryptUtils.decrypt_with_fixed_key(responseText);
                                response = new Result.Success(decryptedData);
                            } catch (Exception e) {
                                WSKLog.e(TAG, "Decryption failed: " + e.getMessage() + e.getMessage());
                                response = new Result.Error(-1, "Decryption failed: " + e.getMessage());
                            }
                        } else {
                            decryptedData = new JSONObject(responseText);
                            response = new Result.Success(decryptedData);
                        }
                    } else {
                        // 读取错误响应
                        BufferedReader reader = new BufferedReader(new InputStreamReader(
                                connection.getErrorStream() != null ? connection.getErrorStream() : connection.getInputStream()));
                        StringBuilder sb = new StringBuilder();
                        String line;
                        while ((line = reader.readLine()) != null) {
                            sb.append(line);
                        }
                        responseText = sb.toString();
                        reader.close();

                        response = new Result.Error(responseCode, responseText);
                    }

                    // 计算请求耗时
                    long duration = System.currentTimeMillis() - startTime;

                    // 统一日志记录 - 只记录一条日志
                    StringBuilder logMessage = new StringBuilder();
                    logMessage.append("GET ").append(url).append("\n");
                    logMessage.append("Status: ").append(responseCode).append(", Duration: ").append(duration).append("ms\n");
                    logMessage.append("Response: ").append(decryptedData.toString());

                    if (responseCode >= 200 && responseCode < 300) {
                        WSKLog.i(TAG, logMessage.toString());
                    } else {
                        WSKLog.e(TAG, logMessage.toString());
                    }

                    // 切换到主线程执行回调
                    final Result finalResponse = response;
                    mainHandler.post(new Runnable() {
                        @Override
                        public void run() {
                            callback.onResult(finalResponse);
                        }
                    });
                } catch (Exception e) {
                    // 计算请求耗时
                    long duration = System.currentTimeMillis() - startTime;

                    // 统一错误日志记录
                    StringBuilder errorLogMessage = new StringBuilder();
                    errorLogMessage.append("GET ").append(url).append(" - Failed\n");
                    errorLogMessage.append("Duration: ").append(duration).append("ms\n");
                    errorLogMessage.append("Error: ").append(e.getMessage());

                    WSKLog.e(TAG, errorLogMessage.toString() + e.getMessage());

                    mainHandler.post(new Runnable() {
                        @Override
                        public void run() {
                            callback.onResult(new Result.Error(-1, e.getMessage() != null ? e.getMessage() : "Unknown error"));
                        }
                    });
                }
            }
        }).start();
    }

    public static void get(String url, Callback callback) {
        get(url, true, callback);
    }

    public static void post(String url, JSONObject body, boolean encrypt, Callback callback) {
        new Thread(new Runnable() {
            @Override
            public void run() {
                long startTime = System.currentTimeMillis();
                String responseText = "";
                int responseCode = -1;

                PostBodyExt.appendProps(body);
                String bodyString = body.toString();
                String encryptedBodyString = "";

                try {
                    // 准备请求头和请求体
                    Map<String, String> headers;

                    if (encrypt) {
                        // 加密请求体
                        encryptedBodyString = HttpDecryptUtils.encrypt_with_fixed_key(bodyString);

                        headers = new HashMap<>();
                        headers.put("Content-Type", "text/plain");
                    } else {
                        encryptedBodyString = bodyString;
                        headers = new HashMap<>();
                        headers.put("Content-Type", "application/json");
                    }

                    // 创建连接
                    HttpURLConnection connection = (HttpURLConnection) new URL(url).openConnection();
                    connection.setRequestMethod("POST");
                    connection.setConnectTimeout(TIMEOUT);
                    connection.setReadTimeout(TIMEOUT);
                    connection.setDoOutput(true);
                    for (Map.Entry<String, String> entry : headers.entrySet()) {
                        connection.setRequestProperty(entry.getKey(), entry.getValue());
                    }

                    // 写入请求体
                    connection.getOutputStream().write(encryptedBodyString.getBytes(StandardCharsets.UTF_8));

                    // 获取响应码
                    responseCode = connection.getResponseCode();
                    JSONObject decryptedData = new JSONObject();

                    Result response;
                    if (responseCode >= 200 && responseCode < 300) {
                        // 读取成功响应
                        BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                        StringBuilder sb = new StringBuilder();
                        String line;
                        while ((line = reader.readLine()) != null) {
                            sb.append(line);
                        }
                        responseText = sb.toString();
                        reader.close();

                        if (encrypt) {
                            // 解密响应数据
                            try {
                                decryptedData = HttpDecryptUtils.decrypt_with_fixed_key(responseText);
                                response = new Result.Success(decryptedData);
                            } catch (Exception e) {
                                WSKLog.e(TAG, "Decryption failed: " + e.getMessage() + e.getMessage());
                                response = new Result.Error(-1, "Decryption failed: " + e.getMessage());
                            }
                        } else {
                            decryptedData = new JSONObject(responseText);
                            response = new Result.Success(decryptedData);
                        }
                    } else {
                        // 读取错误响应
                        BufferedReader reader = new BufferedReader(new InputStreamReader(
                                connection.getErrorStream() != null ? connection.getErrorStream() : connection.getInputStream()));
                        StringBuilder sb = new StringBuilder();
                        String line;
                        while ((line = reader.readLine()) != null) {
                            sb.append(line);
                        }
                        responseText = sb.toString();
                        reader.close();

                        try {
                            if (encrypt && !responseText.isEmpty()) {
                                decryptedData = HttpDecryptUtils.decrypt_with_fixed_key(responseText);
                            } else {
                                decryptedData = new JSONObject().put("error", responseText);
                            }
                        } catch (Exception e) {
                            try {
                                decryptedData = new JSONObject().put("error", responseText);
                            } catch (JSONException je) {
                                // 处理JSON异常
                            }
                        }

                        response = new Result.Error(responseCode, decryptedData.toString());
                    }

                    // 计算请求耗时
                    long duration = System.currentTimeMillis() - startTime;

                    // 统一日志记录 - 只记录一条日志
                    StringBuilder logMessage = new StringBuilder();
                    logMessage.append("POST ").append(url).append("\n");
                    logMessage.append("Request: ").append(bodyString).append("\n");
                    logMessage.append("Status: ").append(responseCode).append(", Duration: ").append(duration).append("ms\n");
                    logMessage.append("Response: ").append(decryptedData);

                    if (responseCode >= 200 && responseCode < 300) {
                        WSKLog.i(TAG, logMessage.toString());
                    } else {
                        WSKLog.e(TAG, logMessage.toString());
                    }

                    // 切换到主线程执行回调
                    final Result finalResponse = response;
                    mainHandler.post(new Runnable() {
                        @Override
                        public void run() {
                            callback.onResult(finalResponse);
                        }
                    });
                } catch (Exception e) {
                    // 计算请求耗时
                    long duration = System.currentTimeMillis() - startTime;

                    // 统一错误日志记录
                    StringBuilder errorLogMessage = new StringBuilder();
                    errorLogMessage.append("POST ").append(url).append(" - Failed\n");
                    errorLogMessage.append("Request: ").append(bodyString).append("\n");
                    errorLogMessage.append("Duration: ").append(duration).append("ms\n");
                    errorLogMessage.append("Error: ").append(e.getMessage());

                    WSKLog.e(TAG, errorLogMessage.toString() + e.getMessage());

                    mainHandler.post(new Runnable() {
                        @Override
                        public void run() {
                            callback.onResult(new Result.Error(-1, e.getMessage() != null ? e.getMessage() : "Unknown error"));
                        }
                    });
                }
            }
        }).start();
    }

    public static void post(String url, JSONObject body, Callback callback) {
        post(url, body, true, callback);
    }
}
