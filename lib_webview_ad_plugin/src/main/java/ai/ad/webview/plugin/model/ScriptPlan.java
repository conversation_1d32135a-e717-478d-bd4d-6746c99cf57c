package ai.ad.webview.plugin.model;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import ai.ad.webview.sdk.logger.WSKLog
        ;

public class ScriptPlan {
    private static final String TAG = "ScriptPlan";

    private final List<Script> scripts;
    private final MockConfig mockConfig;

    public ScriptPlan() {
        this(Collections.emptyList(), new MockConfig());
    }

    public ScriptPlan(List<Script> scripts, MockConfig mockConfig) {
        this.scripts = scripts;
        this.mockConfig = mockConfig;
    }

    public List<Script> getScripts() {
        return scripts;
    }

    public MockConfig getMockConfig() {
        return mockConfig;
    }

    public static class Script {
        private final String url;
        private final double weight;
        private final int maxActionsIn24h;
        private final String id;

        public Script() {
            this("", 1.0, 1000, "");
        }

        public Script(String url, double weight, int maxActionsIn24h, String id) {
            this.url = url;
            this.weight = weight;
            this.maxActionsIn24h = maxActionsIn24h;
            this.id = id;
        }

        public String getUrl() {
            return url;
        }

        public double getWeight() {
            return weight;
        }

        public int getMaxActionsIn24h() {
            return maxActionsIn24h;
        }

        public String getId() {
            return id;
        }

        @Override
        public String toString() {
            return "Script{" +
                    "id='" + id + '\'' +
                    ", url='" + url + '\'' +
                    ", weight=" + weight +
                    ", maxActionsIn24h=" + maxActionsIn24h +
                    '}';
        }
    }

    public static class MockConfig {
        private final String ua;
        private final int delay;
        private final int interval;
        private final int expire;
        private final Jitter jitter;
        private final boolean debug;

        public MockConfig() {
            this("", 5, 60, 60, new Jitter(), false);
        }

        public MockConfig(String ua, int delay, int interval, int expire, Jitter jitter, boolean debug) {
            this.ua = ua;
            this.delay = delay;
            this.interval = interval;
            this.expire = expire;
            this.jitter = jitter;
            this.debug = debug;
        }

        public String getUa() {
            return ua;
        }

        public int getDelay() {
            return delay;
        }

        public int getInterval() {
            return interval;
        }

        public int getExpire() {
            return expire;
        }

        public Jitter getJitter() {
            return jitter;
        }

        public boolean isDebug() {
            return debug;
        }
    }

    public static class Jitter {
        private final JitterConfig xy;
        private final JitterConfig time;

        public Jitter() {
            this(new JitterConfig(0.1, 1), new JitterConfig(0.5, 20));
        }

        public Jitter(JitterConfig xy, JitterConfig time) {
            this.xy = xy;
            this.time = time;
        }

        public JitterConfig getXy() {
            return xy;
        }

        public JitterConfig getTime() {
            return time;
        }
    }

    public static class JitterConfig {
        private final double percent;
        private final int minPx;

        public JitterConfig(double percent, int minPx) {
            this.percent = percent;
            this.minPx = minPx;
        }

        public double getPercent() {
            return percent;
        }

        public int getMinPx() {
            return minPx;
        }

        // Provide an alias method for time type
        public int getMinMs() {
            return minPx;
        }
    }

    public JSONObject toJSONObject() throws JSONException {
        JSONObject jsonObject = new JSONObject();

        JSONArray scriptsArray = new JSONArray();
        for (Script script : scripts) {
            JSONObject scriptObj = new JSONObject();
            scriptObj.put("url", script.getUrl());
            scriptObj.put("weight", script.getWeight());
            scriptObj.put("max_actions_in_24h", script.getMaxActionsIn24h());
            scriptObj.put("id", script.getId());
            scriptsArray.put(scriptObj);
        }
        jsonObject.put("scripts", scriptsArray);

        JSONObject mockConfigObj = new JSONObject();
        mockConfigObj.put("ua", mockConfig.getUa());
        mockConfigObj.put("delay", mockConfig.getDelay());
        mockConfigObj.put("interval", mockConfig.getInterval());
        mockConfigObj.put("expire", mockConfig.getExpire());

        JSONObject jitterObj = new JSONObject();
        JSONObject xyObj = new JSONObject();
        xyObj.put("percent", mockConfig.getJitter().getXy().getPercent());
        xyObj.put("min_px", mockConfig.getJitter().getXy().getMinPx());
        jitterObj.put("xy", xyObj);

        JSONObject timeObj = new JSONObject();
        timeObj.put("percent", mockConfig.getJitter().getTime().getPercent());
        timeObj.put("min_ms", mockConfig.getJitter().getTime().getMinMs());
        jitterObj.put("time", timeObj);

        mockConfigObj.put("jitter", jitterObj);
        mockConfigObj.put("debug", mockConfig.isDebug());

        jsonObject.put("mock_config", mockConfigObj);

        return jsonObject;
    }

    /**
     * Parse ScriptPlan object from JSONObject
     */
    public static ScriptPlan fromJSONObject(JSONObject jsonObject) {
        try {
            // Check if JSON object is empty
            if (jsonObject.length() == 0) {
                WSKLog.e(TAG, "Parsing failed: JSON object is empty");
                return new ScriptPlan(Collections.emptyList(), new MockConfig());
            }

            // Parse scripts array
            List<Script> scripts = new ArrayList<>();
            try {
                JSONArray scriptsArray = jsonObject.optJSONArray("scripts");
                if (scriptsArray == null) {
                    WSKLog.e(TAG, "Parsing failed: 'scripts' field does not exist or is not an array");
                } else {
                    for (int i = 0; i < scriptsArray.length(); i++) {
                        try {
                            JSONObject scriptObj = scriptsArray.optJSONObject(i);
                            if (scriptObj != null) {
                                scripts.add(
                                        new Script(
                                                scriptObj.optString("url", ""),
                                                scriptObj.optDouble("weight", 1.0),
                                                scriptObj.optInt("max_actions_in_24h", Integer.MAX_VALUE),
                                                scriptObj.optString("id", "")
                                        )
                                );
                            }
                        } catch (Exception e) {
                            WSKLog.e(TAG, "Failed to parse script object: " + e.getMessage());
                        }
                    }
                }
            } catch (Exception e) {
                WSKLog.e(TAG, "Failed to parse scripts array: " + e.getMessage());
            }

            // If no scripts were parsed, log a warning
            if (scripts.isEmpty()) {
                WSKLog.e(TAG, "No valid scripts in parsing result");
            }

            // Parse mock_config object
            JSONObject mockConfigObj = jsonObject.optJSONObject("mock_config");
            if (mockConfigObj == null) {
                mockConfigObj = new JSONObject();
            }

            JSONObject jitterObj = mockConfigObj.optJSONObject("jitter");
            if (jitterObj == null) {
                jitterObj = new JSONObject();
            }

            JSONObject xyObj = jitterObj.optJSONObject("xy");
            if (xyObj == null) {
                xyObj = new JSONObject();
            }

            JSONObject timeObj = jitterObj.optJSONObject("time");
            if (timeObj == null) {
                timeObj = new JSONObject();
            }

            String ua = mockConfigObj.optString("ua");

            MockConfig mockConfig = new MockConfig(
                    ua,
                    mockConfigObj.optInt("delay", 5),
                    mockConfigObj.optInt("interval", 60),
                    mockConfigObj.optInt("expire", 3600),
                    new Jitter(
                            new JitterConfig(
                                    xyObj.optDouble("percent", 0.1),
                                    xyObj.optInt("min_px", 1)
                            ),
                            new JitterConfig(
                                    timeObj.optDouble("percent", 0.5),
                                    timeObj.optInt("min_ms", 20)
                            )
                    ),
                    mockConfigObj.optBoolean("debug", false)
            );

            return new ScriptPlan(scripts, mockConfig);
        } catch (Exception e) {
            WSKLog.e(TAG, "Failed to parse ScriptPlan: " + (e.getMessage() != null ? e.getMessage() : "Unknown error"));
            // Return an empty ScriptPlan object
            return new ScriptPlan(Collections.emptyList(), new MockConfig());
        }
    }

    /**
     * Parse ScriptPlan object from JSON string or JSONObject
     * Used to support the data parameter in HttpUtils.Result.Success
     */
    public static ScriptPlan fromJson(JSONObject data) {
        try {
            return fromJSONObject(data);
        } catch (Exception e) {
            WSKLog.e(TAG, "fromJson parsing failed: " + (e.getMessage() != null ? e.getMessage() : "Unknown error"));
            return new ScriptPlan(Collections.emptyList(), new MockConfig());
        }
    }
}
