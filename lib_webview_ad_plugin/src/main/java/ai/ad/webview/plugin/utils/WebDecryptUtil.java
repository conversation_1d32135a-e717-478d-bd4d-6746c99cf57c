package ai.ad.webview.plugin.utils;

import android.util.Base64;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;

import ai.ad.webview.sdk.logger.WSKLog;

public class WebDecryptUtil {
    private static final String MD5_ALGORITHM = "MD5";
    private static final String RC4_ALGORITHM = "RC4";
    private static final String AES_CBC_PKCS5PADDING = "AES/CBC/PKCS5Padding";
    private static final String SECRET_KEY = "L7trZqD2XPGpMPvG";
    private static final String IV = "0ef7e31120b811e9f10740e018ad22dd";
    private static final String TAG = "WebDecryptUtil";

    private WebDecryptUtil() {
        // 私有构造函数，防止实例化
    }

    // 将十六进制字符串转换为字节数组
    private static byte[] hexStringToByteArray(String s) {
        int len = s.length();
        byte[] data = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte) ((Character.digit(s.charAt(i), 16) << 4) + Character.digit(s.charAt(i + 1), 16));
        }
        return data;
    }

    private static String getKey() {
        byte[] encoded = {84, 115, 113, 57, 101, 50, 78, 123, 82, 99, 66, 50, 69, 78, 118, 82};
        byte[] key = new byte[encoded.length];
        for (int i = 0; i < encoded.length; i++) {
            key[i] = (byte) (encoded[i] ^ 1);
        }
        return new String(key);
    }

    private static String cutString(String str, int startIndex, int length) {
        int start = startIndex;
        int len = length;

        if (start >= 0) {
            if (len < 0) {
                len *= -1;
                if (start - len < 0) {
                    len = start;
                    start = 0;
                } else {
                    start -= len;
                }
            }
            if (start > str.length()) return "";
        } else {
            if (len < 0) return "";
            if (len + start > 0) {
                len += start;
                start = 0;
            } else {
                return "";
            }
        }

        if (str.length() - start < len) {
            len = str.length() - start;
        }

        return str.substring(start, start + len);
    }

    private static String cutString(String str, int startIndex) {
        return cutString(str, startIndex, str.length());
    }

    private static byte[] getKey(byte[] pass, int kLen) {
        byte[] mBox = new byte[kLen];
        for (int i = 0; i < kLen; i++) {
            mBox[i] = (byte) i;
        }
        int j = 0;

        for (int i = 0; i < kLen; i++) {
            j = (j + (mBox[i] & 0xFF) + pass[i % pass.length]) % kLen;
            // Swap values
            byte temp = mBox[i];
            mBox[i] = mBox[j];
            mBox[j] = temp;
        }

        return mBox;
    }

    private static byte[] rc4(byte[] input, String pass) {
        if (input == null || pass == null) return null;

        byte[] output = new byte[input.length];
        byte[] mBox = getKey(pass.getBytes(), 256);
        int i = 0;
        int j = 0;

        for (int offset = 0; offset < input.length; offset++) {
            i = (i + 1) % mBox.length;
            j = (j + (mBox[i] & 0xFF)) % mBox.length;
            // Swap values
            byte temp = mBox[i];
            mBox[i] = mBox[j];
            mBox[j] = temp;

            byte a = input[offset];
            byte b = mBox[((mBox[i] & 0xFF) + (mBox[j] & 0xFF)) % mBox.length];
            output[offset] = (byte) (a ^ (b & 0xFF));
        }

        return output;
    }

    private static String md52(String md5) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] md5Bytes = md.digest(md5.getBytes());
            StringBuilder sb = new StringBuilder();
            for (byte b : md5Bytes) {
                sb.append(String.format("%02x", b & 0xFF));
            }
            return sb.toString();
        } catch (Exception e) {
            return "";
        }
    }

    private static int toInt(byte b) {
        return b & 0xFF;
    }

    public static String decode(String source, String key) {
        try {
            if (source == null || source.isEmpty() || key == null || key.isEmpty()) return "";

            int ckeyLength = 8;
            String keyMd5 = md52(key);
            String keya = md52(cutString(keyMd5, 0, 16));
            String keyb = md52(cutString(keyMd5, 16, 16));
            String keyc = (ckeyLength > 0) ? cutString(source, 0, ckeyLength) : "";

            String cryptkey = keya + md52(keya + keyc);

            // 尝试不同的Base64填充
            String result = tryDecode(cutString(source, ckeyLength), cryptkey, keyb);
            if (result != null) return result;

            result = tryDecode(cutString(source + "=", ckeyLength), cryptkey, keyb);
            if (result != null) return result;

            result = tryDecode(cutString(source + "==", ckeyLength), cryptkey, keyb);
            if (result != null) return result;

            return "";

        } catch (Exception e) {
            WSKLog.e(TAG, "Decryption failed: " + e.getMessage());
            return "";
        }
    }

    private static String tryDecode(String src, String cryptkey, String keyb) {
        try {
            byte[] temp = Base64.decode(src, 0);
            byte[] decrypted = rc4(temp, cryptkey);
            if (decrypted == null) return null;

            String result = new String(decrypted);

            if (cutString(result, 10, 16).equals(cutString(md52(cutString(result, 26) + keyb), 0, 16))) {
                WSKLog.d(TAG, "Decryption successful");
                return cutString(result, 26);
            } else {
                return null;
            }
        } catch (Exception e) {
            return null;
        }
    }

    public static String decryptAES(String encryptedData) {
        try {
            byte[] encryptedBytes = Base64.decode(encryptedData, 0);
            Cipher cipher = Cipher.getInstance(AES_CBC_PKCS5PADDING);
            SecretKeySpec keySpec = new SecretKeySpec(SECRET_KEY.getBytes(StandardCharsets.UTF_8), "AES");
            IvParameterSpec ivSpec = new IvParameterSpec(hexStringToByteArray(IV));

            cipher.init(Cipher.DECRYPT_MODE, keySpec, ivSpec);
            byte[] decryptedBytes = cipher.doFinal(encryptedBytes);
            String result = new String(decryptedBytes, StandardCharsets.UTF_8);
            WSKLog.d(TAG, "AES Decryption successful");
            return result;
        } catch (Exception e) {
            WSKLog.e(TAG, "AES Decryption failed: " + e.getMessage());
            throw new RuntimeException(e);
        }
    }

    public static String encryptAES(String plainText) {
        try {
            Cipher cipher = Cipher.getInstance(AES_CBC_PKCS5PADDING);
            SecretKeySpec keySpec = new SecretKeySpec(SECRET_KEY.getBytes(StandardCharsets.UTF_8), "AES");
            IvParameterSpec ivSpec = new IvParameterSpec(hexStringToByteArray(IV));

            cipher.init(Cipher.ENCRYPT_MODE, keySpec, ivSpec);
            byte[] encryptedBytes = cipher.doFinal(plainText.getBytes(StandardCharsets.UTF_8));
            return Base64.encodeToString(encryptedBytes, Base64.DEFAULT);
        } catch (Exception e) {
            WSKLog.e(TAG, "AES Encryption failed: " + e.getMessage());
            throw new RuntimeException(e);
        }
    }
}
