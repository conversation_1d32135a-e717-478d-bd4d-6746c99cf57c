package ai.ad.webview.plugin.overlay;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.graphics.Color;
import android.graphics.PixelFormat;
import android.os.Build;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.webkit.WebView;
import android.widget.FrameLayout;

import java.lang.ref.WeakReference;

import ai.ad.webview.plugin.model.ScriptData;
import ai.ad.webview.plugin.scheduler.ScriptScheduler;
import ai.ad.webview.sdk.logger.WSKLog
        ;
import ai.ad.webview.plugin.webview.WSKDelegate;
import ai.ad.webview.sdk.WSKSDK;

/**
 * 应用内悬浮WebView
 * 负责创建和管理WebView悬浮窗
 */
public class InAppOverlayView implements ScriptScheduler.ScriptTaskListener {
    private static final String TAG = "InAppOverlayView";

    private Context context;
    private WindowManager windowManager;
    private FrameLayout rootLayout;
    private WebView webView;
    private boolean isShowing = false;

    // ScriptScheduler实例，用于定时执行脚本
    private ScriptScheduler scriptScheduler;

    // WSKDelegate实例，用于执行脚本
    private WSKDelegate webDelegate;

    /**
     * 构造函数
     *
     * @param context 上下文
     */
    public InAppOverlayView(Context context) {
        // 使用ApplicationContext避免内存泄漏和Context更新问题
        this.context = context.getApplicationContext();
        this.windowManager = (WindowManager) this.context.getSystemService(Context.WINDOW_SERVICE);

        try {
            WSKLog.d(TAG, "constructor -> Initializing ScriptScheduler");

            // 获取ScriptScheduler单例
            scriptScheduler = ScriptScheduler.getInstance();

            // 初始化ScriptScheduler
            scriptScheduler.init(this.context);

            // 设置任务监听器
            scriptScheduler.setTaskListener(this);

            WSKLog.d(TAG, "constructor -> ScriptScheduler initialization completed");
        } catch (Exception e) {
            WSKLog.e(TAG, "constructor -> Exception occurred while initializing ScriptScheduler: " + e.getMessage());
        }
    }


    /**
     * 创建悬浮窗
     *
     * @param activity 当前Activity
     * @return 是否创建成功
     */
    @SuppressLint("SetJavaScriptEnabled")
    public boolean create(Activity activity) {
        if (isShowing) {
            WSKLog.d(TAG, "Overlay is already displayed, no need to recreate");
            return true;
        }

        // 检查Activity是否有效
        if (activity == null) {
            WSKLog.e(TAG, "Failed to create in-app overlay: Activity is null");
            return false;
        }

        // 再次检查Activity是否已销毁
        if (activity.isFinishing() || (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1 && activity.isDestroyed())) {
            WSKLog.e(TAG, "Failed to create in-app overlay: Activity is destroyed");
            return false;
        }

        try {
            WSKLog.d(TAG, "Starting to create in-app overlay");

            // 使用ApplicationContext创建视图，避免Context更新问题
            // 不再依赖Activity的WindowToken

            // 创建根布局
            rootLayout = new FrameLayout(context);
            rootLayout.setLayoutParams(new ViewGroup.LayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.MATCH_PARENT
            ));

            // 设置背景透明
            rootLayout.setBackgroundColor(Color.TRANSPARENT);

            // 创建或获取WSKDelegate
            if (webDelegate == null) {
                webDelegate = new WSKDelegate(context);
            }

            // 让WSKDelegate创建WebView
            webView = webDelegate.createWebView();
            WSKLog.d(TAG, "Creating new WebView and WSKDelegate");

            // 将WebView添加到根布局
            if (webView.getParent() == null) {
                rootLayout.addView(webView);
                WSKLog.d(TAG, "Adding WebView to root layout");
            } else {
                WSKLog.e(TAG, "WebView already has parent view, cannot add to root layout");
                return false;
            }

            // 创建WindowManager布局参数
            WindowManager.LayoutParams params = createLayoutParams(activity);

            try {
                // 再次检查Activity是否已销毁
                if (activity.isFinishing() || (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1 && activity.isDestroyed())) {
                    WSKLog.e(TAG, "Failed to add view to window manager: Activity is destroyed");
                    cleanup(false);
                    return false;
                }

                // 添加到窗口
                windowManager.addView(rootLayout, params);
                isShowing = true;

                // 设置脚本完成监听器
                setupScriptCompletionListener();

                WSKLog.d(TAG, "In-app overlay created successfully");
                return true;
            } catch (Exception e) {
                WSKLog.e(TAG, "Failed to add view to window manager: " + e.getMessage());

                // 清理资源，但不销毁WebView和WSKDelegate
                cleanup(false);
                return false;
            }
        } catch (Exception e) {
            WSKLog.e(TAG, "Failed to create in-app overlay: " + e.getMessage());
            cleanup(false);
            return false;
        }
    }

    /**
     * 初始化WSKDelegate
     */
    private void setupScriptCompletionListener() {
        try {
            WSKLog.d(TAG, "Initializing WSKDelegate");

            if (webDelegate == null) {
                WSKLog.e(TAG, "setupScriptCompletionListener -> WSKDelegate is null");
                return;
            }

            WSKLog.d(TAG, "WSKDelegate initialization completed");
        } catch (Exception e) {
            WSKLog.e(TAG, "Failed to initialize WSKDelegate: " + e.getMessage());
        }
    }

    /**
     * 创建WindowManager布局参数
     *
     * @param activity 当前Activity
     * @return WindowManager.LayoutParams
     */
    private WindowManager.LayoutParams createLayoutParams(Activity activity) {
        // 根据Android版本选择合适的窗口类型
        final int type;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            // Android 8.0及以上使用TYPE_APPLICATION_OVERLAY，但需要权限
            // 由于我们没有权限，所以仍然使用TYPE_APPLICATION_PANEL
            type = WindowManager.LayoutParams.TYPE_APPLICATION_PANEL;
        } else {
            // Android 8.0以下使用TYPE_PHONE，无需权限
            type = WindowManager.LayoutParams.TYPE_APPLICATION;
        }

        WindowManager.LayoutParams params = new WindowManager.LayoutParams(
                WindowManager.LayoutParams.MATCH_PARENT,
                WindowManager.LayoutParams.MATCH_PARENT,
                type,
                WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE |
                        WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE |
                        WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN,
                PixelFormat.TRANSLUCENT
        );

        // 设置token，这是必须的，确保窗口与Activity关联
        // 但我们会在Activity切换时更新这个token
        params.token = activity.getWindow().getDecorView().getWindowToken();

        params.gravity = Gravity.TOP | Gravity.START;
        params.x = 0;
        params.y = 0;

        return params;
    }

    /**
     * 显示悬浮窗
     */
    public void show() {
        WSKLog.d(TAG, "show -> Starting to show overlay");

        if (rootLayout != null) {
            // 检查当前可见性状态
            boolean wasVisible = (rootLayout.getVisibility() == View.VISIBLE);

            // 设置为可见
            rootLayout.setVisibility(View.VISIBLE);

            if (!wasVisible) {
                WSKLog.d(TAG, "show -> Overlay changed from invisible to visible");
            } else {
                WSKLog.d(TAG, "show -> Overlay is already in visible state");
            }
        } else {
            WSKLog.e(TAG, "show -> rootLayout is null, cannot show overlay");
        }

        // 无论悬浮窗是否可见，都启动脚本调度器
        if (scriptScheduler != null) {
            WSKLog.d(TAG, "show -> Starting script scheduler");
            scriptScheduler.start();
        } else {
            WSKLog.e(TAG, "show -> scriptScheduler is null, cannot start scheduler");
        }
    }

    /**
     * 隐藏悬浮窗
     */
    public void hide() {
        if (rootLayout != null && rootLayout.getVisibility() == View.VISIBLE) {
            rootLayout.setVisibility(View.GONE);
            WSKLog.d(TAG, "Hiding overlay");

            // 停止脚本调度器
            if (scriptScheduler != null) {
                scriptScheduler.stop();
            }
        }
    }

    /**
     * 暂停脚本执行
     * 应用进入后台时调用
     */
    public void pauseScriptExecution() {
        WSKLog.d(TAG, "pauseScriptExecution -> Pausing script execution");

        // 停止脚本调度器，但不隐藏悬浮窗
        if (scriptScheduler != null) {
            scriptScheduler.stop();
            WSKLog.d(TAG, "pauseScriptExecution -> Script scheduler has been paused");
        }

        // 暂停WSKDelegate的脚本执行
        if (webDelegate != null) {
            webDelegate.pauseScriptExecution();
            WSKLog.d(TAG, "pauseScriptExecution -> WSKDelegate script execution has been paused");
        } else {
            WSKLog.e(TAG, "pauseScriptExecution -> WSKDelegate is null, cannot pause script execution");
        }
    }

    /**
     * 恢复脚本执行
     * 应用回到前台时调用
     */
    public void resumeScriptExecution() {
        WSKLog.d(TAG, "resumeScriptExecution -> Resuming script execution");

        // 恢复WSKDelegate的脚本执行
        if (webDelegate != null) {
            webDelegate.resumeScriptExecution();
            WSKLog.d(TAG, "resumeScriptExecution -> WSKDelegate script execution has been resumed");
        } else {
            WSKLog.e(TAG, "resumeScriptExecution -> WSKDelegate is null, cannot resume script execution");
        }

        // 重新启动脚本调度器
        if (scriptScheduler != null && rootLayout != null && rootLayout.getVisibility() == View.VISIBLE) {
            scriptScheduler.start();
            WSKLog.d(TAG, "resumeScriptExecution -> Script scheduler has been restarted");
        }
    }

    /**
     * 销毁悬浮窗
     *
     * @param preserveWebView 是否保留WebView和WSKDelegate
     */
    public void destroy(boolean preserveWebView) {
        WSKLog.d(TAG, "Destroying overlay, preserveWebView=" + preserveWebView);

        // 如果需要保留WebView和WSKDelegate
        if (preserveWebView && webView != null && webDelegate != null) {
            // 从父视图中移除WebView
            if (webView.getParent() instanceof ViewGroup) {
                ((ViewGroup) webView.getParent()).removeView(webView);
                WSKLog.d(TAG, "WebView removed from parent view, ready for reuse");
            }

            // 不释放WSKDelegate，因为要重用

            // 清理其他资源，但不销毁WebView和WSKDelegate
            cleanup(false);
        } else {
            // 停止脚本调度器
            if (scriptScheduler != null) {
                scriptScheduler.stop();
            }

            // 释放WSKDelegate资源
            if (webDelegate != null) {
                webDelegate.release();
                webDelegate = null;
            }

            // 清理所有资源，包括销毁WebView
            cleanup(true);
        }

        // 确保isShowing标志被重置
        isShowing = false;
    }

    /**
     * 销毁悬浮窗（默认不保留WebView）
     */
    public void destroy() {
        destroy(false);
    }

    /**
     * 当脚本准备好执行时调用
     * 实现ScriptTaskListener接口
     *
     * @param scriptData 脚本数据
     */
    @Override
    public void onScriptReady(ScriptData scriptData) {
        try {
            if (scriptData == null || scriptData.getMetadata() == null) {
                WSKLog.e(TAG, "onScriptReady -> Script data is empty, cannot execute");
                scriptScheduler.notifyScriptCompleted();
                return;
            }

            WSKLog.d(TAG, "onScriptReady -> Script ready for execution, scriptId=" + scriptData.getMetadata().getId() +
                    ",url=" + scriptData.getMetadata().getUrl());

            if (webDelegate == null) {
                WSKLog.e(TAG, "onScriptReady -> WSKDelegate is null, cannot execute script");
                scriptScheduler.notifyScriptCompleted();
                return;
            }

            // 确保WebView已经创建
            if (webView == null) {
                WSKLog.e(TAG, "onScriptReady -> WebView is null, cannot execute script");
                scriptScheduler.notifyScriptCompleted();
                return;
            }

            // 设置脚本完成监听器
            webDelegate.setScriptCompletionListener(new WSKDelegate.ScriptCompletionListener() {
                @Override
                public void onScriptCompleted() {
                    WSKLog.d(TAG, "onScriptCompleted -> Script execution completed");
                    WSKSDK.notifyScriptCompleted();
                    scriptScheduler.notifyScriptCompleted();
                }

                @Override
                public void onScriptFailed(String errorMessage) {
                    WSKLog.e(TAG, "onScriptFailed -> Script execution failed: " + errorMessage);
                    WSKSDK.notifyError("onScriptFailed err msg=" + errorMessage);
                    scriptScheduler.notifyScriptCompleted();
                }
            });

            WSKSDK.notifyWebViewLoaded();
            // 执行脚本
            WSKLog.d(TAG, "onScriptReady -> Starting script execution");
            webDelegate.executeScriptData(scriptData);
            WSKLog.d(TAG, "onScriptReady -> Script execution command has been sent");
        } catch (Exception e) {
            WSKLog.e(TAG, "onScriptReady -> Failed to execute script: " + e.getMessage());
            WSKSDK.notifyError("Failed to execute script: " + e.getMessage());
            scriptScheduler.notifyScriptCompleted();
        }
    }

    /**
     * 当脚本执行超时时调用
     * 实现ScriptTaskListener接口
     */
    @Override
    public void onScriptTimeout() {
        WSKLog.w(TAG, "onScriptTimeout -> Script execution timeout");

        // 释放WSKDelegate资源
        if (webDelegate != null) {
            webDelegate.release();
        }
    }


    /**
     * 清理资源
     *
     * @param destroyWebView 是否销毁WebView和WSKDelegate
     */
    private void cleanup(boolean destroyWebView) {
        try {
            WSKLog.d(TAG, "cleanup -> Starting resource cleanup, destroyWebView=" + destroyWebView);

            // 如果需要销毁WSKDelegate
            if (destroyWebView && webDelegate != null) {
                webDelegate.release();
                webDelegate = null;
                WSKLog.d(TAG, "cleanup -> WSKDelegate has been released");
            }

            // 从窗口移除视图
            if (windowManager != null && rootLayout != null) {
                try {
                    windowManager.removeView(rootLayout);
                    WSKLog.d(TAG, "cleanup -> Successfully removed view from window");
                } catch (Exception e) {
                    // 忽略"View not attached to window manager"异常
                    WSKLog.e(TAG, "cleanup -> Failed to remove view from window: " + e.getMessage());
                }
            }

            // 如果需要销毁WebView
            if (destroyWebView && webView != null) {
                try {
                    webView.stopLoading();
                    webView.clearHistory();
                    webView.clearCache(true);
                    webView.clearFormData();

                    // 从父视图中移除
                    if (webView.getParent() instanceof ViewGroup) {
                        ((ViewGroup) webView.getParent()).removeView(webView);
                    }

                    // 销毁WebView
                    webView.destroy();
                    WSKLog.d(TAG, "cleanup -> WebView has been destroyed");
                } catch (Exception e) {
                    WSKLog.e(TAG, "cleanup -> Error destroying WebView: " + e.getMessage());
                } finally {
                    webView = null;
                }
            }

            rootLayout = null;
            WSKLog.d(TAG, "cleanup -> Resource cleanup completed");
        } catch (Exception e) {
            WSKLog.e(TAG, "cleanup -> Error during resource cleanup: " + e.getMessage());
        } finally {
            // 确保isShowing标志被重置
            isShowing = false;
        }
    }

    /**
     * 清理资源（默认销毁WebView）
     */
    private void cleanup() {
        cleanup(true);
    }

    /**
     * 悬浮窗是否正在显示
     *
     * @return 悬浮窗是否正在显示
     */
    public boolean isShowing() {
        return isShowing && rootLayout != null && rootLayout.getVisibility() == View.VISIBLE;
    }

    /**
     * 获取WebView
     *
     * @return WebView实例
     */
    public WebView getWebView() {
        return webView;
    }

    /**
     * 获取脚本调度器
     *
     * @return ScriptScheduler实例
     */
    public ScriptScheduler getScriptScheduler() {
        return scriptScheduler;
    }

    /**
     * 更新悬浮窗的窗口令牌
     * 在Activity切换时调用，以保持悬浮窗显示
     * 使用detach/reattach操作，而不是简单地更新窗口令牌
     * 包含重试机制，确保更新成功率更高
     *
     * @param activity 新的Activity
     * @return 是否更新成功
     */
    public boolean updateWindowToken(Activity activity) {
        return updateWindowTokenWithRetry(activity, 0);
    }

    /**
     * 更新悬浮窗的窗口令牌（带重试机制）
     *
     * @param activity   新的Activity
     * @param retryCount 当前重试次数
     * @return 是否更新成功
     */
    private boolean updateWindowTokenWithRetry(Activity activity, int retryCount) {
        WSKLog.d(TAG, "updateWindowToken -> Starting window token update, retry count: " + retryCount);

        if (!isShowing || rootLayout == null) {
            WSKLog.e(TAG, "updateWindowToken -> Overlay not displayed or rootLayout is null, cannot update window token");
            return false;
        }

        if (activity == null) {
            WSKLog.e(TAG, "updateWindowToken -> Activity is null, cannot update window token");
            return false;
        }

        // 检查Activity是否已销毁
        if (activity.isFinishing() ||
                (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1 && activity.isDestroyed())) {
            WSKLog.e(TAG, "updateWindowToken -> Activity is destroyed, cannot update window token");
            return false;
        }

        try {
            // 执行detach操作：从窗口移除视图，但不销毁
            boolean needReattach = true;
            try {
                windowManager.removeView(rootLayout);
                WSKLog.d(TAG, "updateWindowToken -> Successfully removed view from window");
            } catch (Exception e) {
                WSKLog.e(TAG, "updateWindowToken -> Failed to remove view from window: " + e.getMessage());
                // 如果是"View not attached to window manager"异常，说明视图已经被移除
                // 这种情况下我们只需要添加，不需要先移除
                if (e.getMessage() != null && e.getMessage().contains("not attached to window manager")) {
                    needReattach = true;
                } else {
                    // 其他异常可能需要重试
                    needReattach = false;
                    throw e; // 重新抛出异常，触发重试机制
                }
            }

            if (needReattach) {
                // 再次检查Activity是否已销毁
                if (activity.isFinishing() ||
                        (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1 && activity.isDestroyed())) {
                    WSKLog.e(TAG, "updateWindowToken -> Activity found destroyed when preparing to re-add view");
                    isShowing = false; // 重置状态，因为视图已经被移除
                    return false;
                }

                // 创建新的WindowManager布局参数，使用新Activity的窗口令牌
                WindowManager.LayoutParams params = createLayoutParams(activity);

                // 执行reattach操作：将视图重新添加到窗口
                windowManager.addView(rootLayout, params);
                WSKLog.d(TAG, "updateWindowToken -> Successfully re-added view to window");

                // 确保悬浮窗可见
                rootLayout.setVisibility(View.VISIBLE);

                WSKLog.d(TAG, "updateWindowToken -> Window token update successful");
                return true;
            }

            return false;
        } catch (Exception e) {
            WSKLog.e(TAG, "updateWindowToken -> Failed to update window token: " + e.getMessage());

            // 如果重试次数未达到最大值，则延迟一段时间后重试
            if (retryCount < 3) { // 最多重试3次
                final int nextRetryCount = retryCount + 1;
                WSKLog.d(TAG, "updateWindowToken -> Will retry " + nextRetryCount + " times after 100ms");

                // 使用弱引用保存Activity，避免在延迟执行时Activity已被销毁
                final WeakReference<Activity> activityRef = new WeakReference<>(activity);

                // 使用Handler延迟执行
                new android.os.Handler(android.os.Looper.getMainLooper()).postDelayed(() -> {
                    // 获取弱引用中的Activity
                    Activity currentAct = activityRef.get();

                    // 检查Activity是否仍然有效
                    if (currentAct == null || currentAct.isFinishing() ||
                            (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1 && currentAct.isDestroyed())) {
                        WSKLog.e(TAG, "updateWindowToken -> Activity found invalid during retry");
                        isShowing = false; // 重置状态，因为视图可能已经被移除但未能重新添加
                        return;
                    }

                    // 重试更新窗口令牌
                    updateWindowTokenWithRetry(currentAct, nextRetryCount);
                }, 100);

                return true; // 返回true，表示正在重试中
            } else {
                isShowing = false; // 重置状态，因为视图可能已经被移除但未能重新添加
                return false;
            }
        }
    }
}
