package ai.ad.webview.plugin.utils;

import android.util.Base64;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.crypto.Cipher;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.SecretKeySpec;

import ai.ad.webview.sdk.logger.WSKLog;

public class HttpDecryptUtils {
    private static final String TAG = "HttpDecryptUtils";
    private static final int GCM_TAG_LENGTH = 128;
    private static final KeyManager mKeyManager = new KeyManager();

    private HttpDecryptUtils() {
        // 私有构造函数，防止实例化
    }

    /**
     * CryptoUtils类，模拟示例代码中的CryptoUtils
     */
    public static class CryptoUtils {
        /**
         * 确保数据可序列化
         */
        public static Object ensure_serializable(Object data) {
            if (data == null) return null;

            if (data instanceof Map) {
                Map<String, Object> result = new HashMap<>();
                for (Object entry : ((Map<?, ?>) data).entrySet()) {
                    Map.Entry<?, ?> mapEntry = (Map.Entry<?, ?>) entry;
                    if (mapEntry.getKey() instanceof String) {
                        result.put((String) mapEntry.getKey(), ensure_serializable(mapEntry.getValue()));
                    }
                }
                return result;
            } else if (data instanceof List) {
                List<?> list = (List<?>) data;
                Object[] result = new Object[list.size()];
                for (int i = 0; i < list.size(); i++) {
                    result[i] = ensure_serializable(list.get(i));
                }
                return result;
            } else if (data instanceof String || data instanceof Number || data instanceof Boolean) {
                return data;
            } else {
                return data.toString();
            }
        }
    }

    /**
     * 使用固定密钥加密数据
     * <p>
     * 严格匹配Python实现:
     * iv + tag + ciphertext
     *
     * @param data 要加密的数据，可以是字符串、字节或字典
     * @return Base64编码的加密数据
     */
    public static String encrypt_with_fixed_key(Object data) {
        try {
            // 处理不同类型的数据
            byte[] plaintext;
            if (data instanceof Map) {
                Object serializable_data = CryptoUtils.ensure_serializable(data);
                plaintext = new JSONObject((Map<?, ?>) serializable_data).toString().getBytes(StandardCharsets.UTF_8);
            } else if (data instanceof String) {
                plaintext = ((String) data).getBytes(StandardCharsets.UTF_8);
            } else if (data instanceof byte[]) {
                plaintext = (byte[]) data;
            } else {
                plaintext = data.toString().getBytes(StandardCharsets.UTF_8);
            }

            // 生成随机IV (12字节，与Python一致)
            byte[] iv = new byte[12];
            new SecureRandom().nextBytes(iv);

            // 获取加密密钥
            String encryptionKey = mKeyManager.getEncryptionKey();
            // 创建GCM规范并初始化加密器
            SecretKeySpec key = new SecretKeySpec(encryptionKey.getBytes(StandardCharsets.UTF_8), "AES");
            Cipher cipher = Cipher.getInstance("AES/GCM/NoPadding");
            GCMParameterSpec gcmSpec = new GCMParameterSpec(GCM_TAG_LENGTH, iv);
            cipher.init(Cipher.ENCRYPT_MODE, key, gcmSpec);

            // 加密数据
            byte[] ciphertext = cipher.doFinal(plaintext);

            // 从AES-GCM获取认证标签(tag) - 16字节
            // 在Java实现中，认证标签附加在密文末尾
            // 我们需要提取它并单独放置，与Python保持一致
            int tagSize = 16; // GCM认证标签为16字节(128位)
            byte[] actualCiphertext = new byte[ciphertext.length - tagSize];
            byte[] tag = new byte[tagSize];
            System.arraycopy(ciphertext, 0, actualCiphertext, 0, ciphertext.length - tagSize);
            System.arraycopy(ciphertext, ciphertext.length - tagSize, tag, 0, tagSize);

            // 组合IV、认证标签和密文，与Python实现保持一致: iv + tag + ciphertext
            byte[] encrypted = new byte[iv.length + tag.length + actualCiphertext.length];
            System.arraycopy(iv, 0, encrypted, 0, iv.length);
            System.arraycopy(tag, 0, encrypted, iv.length, tag.length);
            System.arraycopy(actualCiphertext, 0, encrypted, iv.length + tag.length, actualCiphertext.length);

            // Base64编码
            return Base64.encodeToString(encrypted, Base64.NO_WRAP);

        } catch (Exception e) {
            String errorMsg = "Encryption failed: " + e.getMessage();
            throw new RuntimeException(errorMsg + e.getMessage());
        }
    }

    /**
     * 使用固定密钥解密数据
     * <p>
     * 严格匹配Python实现:
     * iv(12字节) + tag(16字节) + ciphertext
     *
     * @param encrypted_data Base64编码的加密数据
     * @return 解密后的数据
     */
    public static JSONObject decrypt_with_fixed_key(String encrypted_data) {
        try {
            // Base64解码
            byte[] encrypted_bytes = Base64.decode(encrypted_data, Base64.DEFAULT);

            // 提取IV (前12字节)
            byte[] iv = new byte[12];
            System.arraycopy(encrypted_bytes, 0, iv, 0, 12);

            // 提取认证标签 (接下来16字节)
            byte[] tag = new byte[16];
            System.arraycopy(encrypted_bytes, 12, tag, 0, 16);

            // 提取实际密文 (剩余部分)
            byte[] ciphertext = new byte[encrypted_bytes.length - 28];
            System.arraycopy(encrypted_bytes, 28, ciphertext, 0, encrypted_bytes.length - 28);

            // 在Java中，我们需要将tag附加到密文末尾才能使用GCM模式
            byte[] ciphertextWithTag = new byte[ciphertext.length + tag.length];
            System.arraycopy(ciphertext, 0, ciphertextWithTag, 0, ciphertext.length);
            System.arraycopy(tag, 0, ciphertextWithTag, ciphertext.length, tag.length);

            // 获取加密密钥
            String encryptionKey = mKeyManager.getEncryptionKey();

            // 创建解密器
            SecretKeySpec key = new SecretKeySpec(encryptionKey.getBytes(StandardCharsets.UTF_8), "AES");
            Cipher cipher = Cipher.getInstance("AES/GCM/NoPadding");
            GCMParameterSpec gcmSpec = new GCMParameterSpec(GCM_TAG_LENGTH, iv);
            cipher.init(Cipher.DECRYPT_MODE, key, gcmSpec);

            // 解密数据
            byte[] plaintext = cipher.doFinal(ciphertextWithTag);

            // 解析JSON
            try {
                return new JSONObject(new String(plaintext, StandardCharsets.UTF_8));
            } catch (JSONException e) {
                // 如果不是JSON对象，尝试解析为JSON数组
                try {
                    JSONArray jsonArray = new JSONArray(new String(plaintext, StandardCharsets.UTF_8));
                    JSONObject result = new JSONObject();
                    result.put("data", jsonArray);
                    return result;
                } catch (JSONException e2) {
                    // 如果不是JSON，返回原始字符串
                    JSONObject result = new JSONObject();
                    result.put("raw_data", new String(plaintext, StandardCharsets.UTF_8));
                    return result;
                }
            }
        } catch (Exception e) {
            String errorMsg = "Decryption failed: " + e.getMessage();
            WSKLog.e(TAG, errorMsg + e.getMessage());
            throw new RuntimeException(errorMsg + e.getMessage());
        }
    }

    /**
     * 解密请求数据并修改原始请求对象
     *
     * @param request        原始请求
     * @param encrypted_data 加密数据
     * @return 修改后的请求对象，供call_next()使用
     */
    public static Object decrypt_request(Object request, String encrypted_data) {
        try {
            // 记录原始加密数据（用于调试）
            WSKLog.d(TAG, "Original encrypted data: " + encrypted_data);

            // 使用固定密钥解密数据
            JSONObject decrypted_data = decrypt_with_fixed_key(encrypted_data);
            WSKLog.d(TAG, "Decryption successful, data type: " + decrypted_data.getClass().getName());

            return decrypted_data;
        } catch (Exception e) {
            WSKLog.e(TAG, "Decryption failed: " + e);
            throw new RuntimeException("Decryption failed: " + e);
        }
    }

    private static class KeyManager {
        // 密钥材料 - 被分散存储和混淆
        private final int[][] KEY_MATERIALS = {
                // 基础值 - 每个值都经过多层编码和混淆处理
                {58, 122, 61, 125, 50, 69, 66, 72},    // 部分一 (经过变换)
                {45, 76, 48, 81, 101, 84, 104, 90},    // 部分二 (经过变换)
                {116, 91, 114, 94, 110, 55, 119, 60},  // 部分三 (经过变换)
                {124, 61, 127, 40, 70, 41, 73, 44}     // 部分四 (经过变换)
        };

        // 转换密钥 - 用于解码各部分
        private final int[] TRANSFORM_KEYS = {
                0x05, 0x04, 0x05, 0x04, 0x03, 0x03, 0x03, 0x03
        };

        // 混淆索引 - 用于重排序字符
        private final int[] SHUFFLE_INDICES = {
                0, 1, 2, 3, 4, 5, 6, 7,
                8, 9, 10, 11, 12, 13, 14, 15,
                16, 17, 18, 19, 20, 21, 22, 23,
                24, 25, 26, 27, 28, 29, 30, 31
        };

        // 生成盐值的种子
        private final byte[] SALT_SEED = {
                0x19, 0x37, 0x34, 0x62, 0x25, 0x7C, 0x0D, 0x3E,
                0x67, 0x32, 0x1D, 0x14, 0x53, 0x4B, 0x7A, 0x71
        };

        // 完整密钥的缓存
        private volatile String cachedFullKey;

        public String getEncryptionKey() {
            // 如果缓存中已有密钥，直接返回
            if (cachedFullKey != null) {
                return cachedFullKey;
            }

            try {
                // 使用多重算法构建密钥
                String assembledKey = assembleAndVerifyKey();

                // 验证密钥长度
                if (assembledKey.length() != 32) {
                    return "";
                }
                // 缓存并返回密钥
                cachedFullKey = assembledKey.replace("WoWmZk4t9", "ThWmZq4t6");
                return cachedFullKey != null ? cachedFullKey : "";
            } catch (Exception e) {
                // 记录异常但不暴露详细信息
                return "";
            }
        }

        /**
         * 组装并验证密钥
         * 使用多层混淆和动态生成算法
         */
        private String assembleAndVerifyKey() {
            // 1. 反混淆并组装密钥材料
            String rawKey = assembleKeyParts();

            // 2. 重排序字符
            String shuffledKey = shuffleKey(rawKey);

            // 3. 最终检查
            return performFinalCheck(shuffledKey);
        }

        /**
         * 组装密钥部分
         */
        private String assembleKeyParts() {
            StringBuilder sb = new StringBuilder(32);

            // 处理每个部分
            for (int i = 0; i < KEY_MATERIALS.length; i++) {
                int[] material = KEY_MATERIALS[i];

                // 处理当前部分的每个字符
                for (int j = 0; j < material.length; j++) {
                    // 反向变换 - 应用转换密钥进行解码
                    char originalChar = (char) (material[j] - TRANSFORM_KEYS[j % TRANSFORM_KEYS.length]);
                    sb.append(originalChar);
                }
            }

            return sb.toString();
        }

        /**
         * 重排序密钥字符
         */
        private String shuffleKey(String input) {
            char[] result = new char[input.length()];
            char[] chars = input.toCharArray();

            // 根据SHUFFLE_INDICES重排序
            for (int i = 0; i < chars.length; i++) {
                int targetIndex = SHUFFLE_INDICES[i];
                result[i] = chars[targetIndex];
            }

            return new String(result);
        }

        /**
         * 执行最终检查
         * 确保生成的密钥是有效的
         */
        private String performFinalCheck(String key) {
            // 验证密钥长度
            if (key.length() != 32) {
                return "";
            }

            // 验证密钥格式
            String validChars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=?!@#$%^&*()[]{}|;:,.<>";
            for (int i = 0; i < key.length(); i++) {
                char c = key.charAt(i);
                if (validChars.indexOf(c) == -1) {
                    return "";
                }
            }

            return key;
        }
    }
}
