package ai.ad.webview.plugin.webview.proxy;

import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Build;
import android.view.Window;
import android.view.WindowManager;
import android.webkit.WebView;

import ai.ad.webview.sdk.logger.WSKLog
        ;
import ai.ad.webview.plugin.webview.WSKDelegate;
import ai.ad.webview.sdk.api.interfaces.IWSKActivity;

/**
 * WSKActivity的代理实现
 * 包含原WSKActivity的核心逻辑，但不继承Activity
 */
public class ProxyWSKActivity implements IWSKActivity {
    private static final String TAG = "ProxyWSKActivity";

    private Context context;
    private WebView mWebView;
    private WSKDelegate webDelegate;
    private BroadcastReceiver closeReceiver;

    public ProxyWSKActivity(Context context) {
        this.context = context;
        WSKLog.d(TAG, "constructor -> Activity proxy instance created");
    }

    /**
     * 重启WSKActivity
     * 先关闭现有的Activity，然后启动新的Activity
     *
     * @param context 上下文
     */

    @Override
    public void configureWindow(Window window) {
        WSKLog.d(TAG, "configureWindow -> Starting window configuration");
        if (window == null) {
            WSKLog.e(TAG, "configureWindow -> Cannot configure window: window is null");
            return;
        }

        try {
            // 设置窗口背景为透明
            window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
            window.setBackgroundDrawableResource(android.R.color.transparent);

            // 设置窗口布局参数
            WindowManager.LayoutParams params = window.getAttributes();
            params.width = WindowManager.LayoutParams.MATCH_PARENT;
            params.height = WindowManager.LayoutParams.MATCH_PARENT;

            // 设置状态栏和导航栏透明（Android 5.0及以上）
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
                window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
                window.setStatusBarColor(Color.TRANSPARENT); // 设置状态栏透明
                window.setNavigationBarColor(Color.TRANSPARENT); // 设置导航栏透明
            }

            // 设置窗口标志以允许点击穿透
            window.addFlags(WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE);

            WSKLog.d(TAG, "configureWindow -> Window configuration completed");
        } catch (Exception e) {
            WSKLog.e(TAG, "configureWindow -> Error configuring window: " + e.getMessage());
        }
    }

    @Override
    public Context getContext() {
        return context;
    }

    @Override
    public void onCreate(Activity activity) {

    }

    @Override
    public void onDestroy(Activity activity) {

    }

    @Override
    public void onResume(Activity activity) {

    }

    @Override
    public void onPause(Activity activity) {

    }
//
//    @Override
//    public WebView getWebView() {
//        return mWebView;
//    }
//
//    @Override
//    public void finish() {
//        Logger.d(TAG, "finish -> Starting to destroy Activity proxy resources");
//        try {
//            // 注销广播接收器
//            if (closeReceiver != null) {
//                context.unregisterReceiver(closeReceiver);
//                closeReceiver = null;
//            }
//
//            // 销毁 WSKDelegate，取消所有等待执行的事件
//            if (webDelegate != null) {
//                webDelegate.destroy();
//                webDelegate = null;
//            }
//
//            // 清理 WebView 资源
//            if (mWebView != null) {
//                mWebView.stopLoading();
////                mWebView.clearHistory();
////                mWebView.clearCache(true);
////                mWebView.clearFormData();
//
//                // 从父视图中移除
//                if (mWebView.getParent() instanceof ViewGroup) {
//                    ((ViewGroup) mWebView.getParent()).removeView(mWebView);
//                }
//
//                mWebView.destroy();
//                mWebView = null;
//            }
//
//            // 如果context是Activity，则调用finish
//            if (context instanceof android.app.Activity) {
//                ((android.app.Activity) context).finish();
//            }
//        } catch (Exception e) {
//            Logger.e(TAG, "Error destroying resources: " + e.getMessage());
//        }
//    }
}
