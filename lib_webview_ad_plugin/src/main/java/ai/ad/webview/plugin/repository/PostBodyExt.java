package ai.ad.webview.plugin.repository;

import org.json.JSONException;
import org.json.JSONObject;

import ai.ad.webview.plugin.ProxyWSKSDK;
import ai.ad.webview.plugin.utils.DeviceIdUtil;
import ai.ad.webview.sdk.WSKSDK;

public class PostBodyExt {

    private PostBodyExt() {
        // 私有构造函数，防止实例化
    }

    public static JSONObject appendProps(JSONObject jsonObject) {
        try {
            jsonObject.put("is_overlay", String.valueOf(ProxyWSKSDK.isOverLayMode));
            if (!WSKSDK.version().isEmpty()) {
                jsonObject.put("sdk_version", WSKSDK.version());
            }
            String osVersion = android.os.Build.VERSION.RELEASE;
            String brand = android.os.Build.MANUFACTURER;
            String model = android.os.Build.MODEL;
            if (!WSKSDK.getAppId().isEmpty()) {
                jsonObject.put("ad_app_id", WSKSDK.getAppId());
            }
            jsonObject.put("os_version", osVersion);
            jsonObject.put("brand", brand);
            jsonObject.put("model", model);
            jsonObject.put("deviceId", DeviceIdUtil.getDeviceId());
        } catch (JSONException e) {
            // 处理JSON异常
        }
        return jsonObject;
    }
}
