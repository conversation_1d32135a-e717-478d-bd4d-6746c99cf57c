package ai.ad.webview.plugin.scheduler;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.os.SystemClock;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;

import ai.ad.webview.plugin.control.ScriptControl;
import ai.ad.webview.plugin.model.ScriptData;
import ai.ad.webview.plugin.repository.BaseScriptRepoImpl;
import ai.ad.webview.plugin.repository.EventRepo;
import ai.ad.webview.plugin.repository.ScriptRepo;
import ai.ad.webview.plugin.utils.DeviceIdUtil;
import ai.ad.webview.sdk.logger.WSKLog
        ;
import ai.ad.webview.sdk.WSKSDK;

/**
 * 脚本调度器
 * 负责定时拉取脚本计划并执行脚本
 * 单例模式，在plugin内部使用
 */
public class ScriptScheduler {
    private static final String TAG = "ScriptScheduler";

    // 单例实例
    private static volatile ScriptScheduler INSTANCE;

    // 检查间隔时间（毫秒）
    private static final long CHECK_INTERVAL_MS = 60 * 1000; // 10秒

    // 脚本执行超时时间（毫秒）
    private static final long EXECUTION_TIMEOUT_MS = 5 * 60 * 1000; // 5分钟

    // 上下文
    private Context context;

    // 处理器
    private Handler handler;

    // 是否正在执行脚本
    private AtomicBoolean isExecuting = new AtomicBoolean(false);

    // 脚本执行开始时间（系统流逝时间）
    private long executionStartTime = 0;

    // 上次脚本执行完成时间（系统流逝时间）
    private long lastExecutionEndTime = 0;

    // 当前正在执行的脚本数据
    private ScriptData currentScriptData;

    // 脚本执行监听器
    private ScriptTaskListener taskListener;

    // 脚本执行检查任务
    private final Runnable checkTask = new Runnable() {
        @Override
        public void run() {
            checkAndExecuteScript();
            // 继续安排下一次检查
            handler.postDelayed(this, CHECK_INTERVAL_MS);
        }
    };

    /**
     * 私有构造函数
     */
    private ScriptScheduler() {
        handler = new Handler(Looper.getMainLooper());
    }

    /**
     * 获取单例实例
     */
    public static ScriptScheduler getInstance() {
        if (INSTANCE == null) {
            synchronized (ScriptScheduler.class) {
                if (INSTANCE == null) {
                    INSTANCE = new ScriptScheduler();
                }
            }
        }
        return INSTANCE;
    }

    /**
     * 初始化调度器
     *
     * @param context 上下文
     */
    public void init(Context context) {
        this.context = context.getApplicationContext();
        WSKLog.d(TAG, "init -> Script scheduler initialized");
    }

    /**
     * 设置脚本任务监听器
     *
     * @param listener 监听器
     */
    public void setTaskListener(ScriptTaskListener listener) {
        this.taskListener = listener;
        WSKLog.d(TAG, "setTaskListener -> Script task listener has been set");
    }

    /**
     * 启动调度器
     */
    public void start() {
        if (context == null) {
            WSKLog.e(TAG, "start -> Scheduler not initialized, cannot start");
            return;
        }

        WSKLog.d(TAG, "start -> Starting script scheduler");

        // 先停止之前的任务，避免重复启动
        stop();

        try {
            // 立即执行一次脚本检查
            handler.post(new Runnable() {
                @Override
                public void run() {
                    WSKLog.d(TAG, "start -> Executing script check immediately");
                    checkAndExecuteScript();
                }
            });

            // 安排定期检查任务
            handler.postDelayed(checkTask, CHECK_INTERVAL_MS);
            WSKLog.d(TAG, "start -> Scheduled periodic check task, interval=" + CHECK_INTERVAL_MS + "ms");
        } catch (Exception e) {
            WSKLog.e(TAG, "start -> Exception occurred while starting script scheduler: " + e.getMessage());
        }
    }

    /**
     * 停止调度器
     */
    public void stop() {
        WSKLog.d(TAG, "stop -> Stopping script scheduler");
        handler.removeCallbacks(checkTask);
    }

    /**
     * 检查并执行脚本
     */
    private void checkAndExecuteScript() {
        WSKLog.d(TAG, "checkAndExecuteScript -> Checking script execution status");

        try {
            // 检查是否已超时
            checkExecutionTimeout();

            // 如果正在执行，则跳过
            if (isExecuting.get()) {
                WSKLog.d(TAG, "checkAndExecuteScript -> Script is currently executing, skipping this check");
                return;
            }

            // 检查是否满足执行间隔
            if (lastExecutionEndTime > 0) {
                long currentTime = SystemClock.elapsedRealtime();
                long elapsedTime = currentTime - lastExecutionEndTime;

                // 从脚本计划中获取间隔时间（秒）
                long intervalSec = ScriptRepo.getScriptPlan().getMockConfig().getInterval();
                long intervalMs = intervalSec * 1000L;

                if (elapsedTime < intervalMs) {
                    WSKLog.d(TAG, "checkAndExecuteScript -> Less than " + intervalSec + " seconds since last execution completion, waiting... elapsed time=" + (elapsedTime / 1000) + " seconds");
                    return;
                }

                WSKLog.d(TAG, "checkAndExecuteScript -> Execution interval satisfied, can execute next script");
            }

            // 检查任务监听器是否已设置
            if (taskListener == null) {
                WSKLog.e(TAG, "checkAndExecuteScript -> Task listener not set, cannot execute script");
                return;
            }

            WSKLog.d(TAG, "checkAndExecuteScript -> Preparing to fetch and execute script");

            // 拉取并执行脚本
            updateScriptPlanAndExecute();
        } catch (Exception e) {
            WSKLog.e(TAG, "checkAndExecuteScript -> Exception occurred while checking and executing script: " + e.getMessage());
        }
    }

    /**
     * 检查脚本执行是否超时
     */
    private void checkExecutionTimeout() {
        if (isExecuting.get() && executionStartTime > 0) {
            long currentTime = SystemClock.elapsedRealtime();
            long elapsedTime = currentTime - executionStartTime;

            if (elapsedTime > EXECUTION_TIMEOUT_MS) {
                WSKLog.w(TAG, "checkExecutionTimeout -> Script execution timeout, elapsedTime=" + elapsedTime + "ms");

                // 重置执行状态
                isExecuting.set(false);
                executionStartTime = 0;

                // 通知监听器脚本执行超时
                if (taskListener != null) {
                    taskListener.onScriptTimeout();
                }
            }
        }
    }

    /**
     * 更新脚本计划并执行脚本
     */
    private void updateScriptPlanAndExecute() {
        WSKLog.d(TAG, "updateScriptPlanAndExecute -> Starting to update script plan");

        try {
            // 获取设备ID
            String deviceId = DeviceIdUtil.getDeviceId();
            if (deviceId == null || deviceId.isEmpty()) {
                WSKLog.e(TAG, "updateScriptPlanAndExecute -> Cannot get device ID, cannot continue");
                WSKSDK.notifyError("Cannot get device ID");
                return;
            }

            // 获取应用ID
            String appId = WSKSDK.getAppId();
            if (appId == null || appId.isEmpty()) {
                WSKLog.e(TAG, "updateScriptPlanAndExecute -> Application ID is empty, cannot continue");
                WSKSDK.notifyError("Application ID is empty");
                return;
            }

            WSKLog.d(TAG, "updateScriptPlanAndExecute -> Device ID and application ID validation passed, deviceId=" + deviceId + ", appId=" + appId);
        } catch (Exception e) {
            WSKLog.e(TAG, "updateScriptPlanAndExecute -> Exception occurred while getting device ID or application ID: " + e.getMessage());
            return;
        }

        try {
            WSKLog.d(TAG, "updateScriptPlanAndExecute -> Starting to call ScriptRepo.updatePlanAndSelectScriptData");

            // 获取脚本数据
            ScriptRepo.updatePlanAndSelectScriptData(context, new BaseScriptRepoImpl.Callback<ScriptData>() {
                @Override
                public void onResult(ScriptData scriptData) {
                    WSKLog.d(TAG, "updateScriptPlanAndExecute_onResult -> Script data callback received");

                    try {
                        // 不再需要安排下一次执行，由定时检查机制自动处理

                        if (scriptData == null) {
                            WSKLog.e(TAG, "updateScriptPlanAndExecute_onResult -> Script data is null, cannot continue");
                            WSKSDK.notifyError("Script data is null");

                            // 上报脚本获取失败事件
                            Map<String, Object> props = new HashMap<>();
                            props.put(EventRepo.PropKey.STATUS, EventRepo.PropValue.STATUS_FAILURE);
                            props.put(EventRepo.PropKey.MESSAGE, "Script data is null");
                            EventRepo.reportEvent(EventRepo.EventKey.SCRIPT_NO_AVAILABLE, props);

                            return;
                        }

                        if (scriptData.getMetadata() == null) {
                            WSKLog.e(TAG, "updateScriptPlanAndExecute_onResult -> Script metadata is null, cannot continue");
                            WSKSDK.notifyError("Script metadata is null");

                            // 上报脚本获取失败事件
                            Map<String, Object> props = new HashMap<>();
                            props.put(EventRepo.PropKey.STATUS, EventRepo.PropValue.STATUS_FAILURE);
                            props.put(EventRepo.PropKey.MESSAGE, "Script metadata is null");
                            EventRepo.reportEvent(EventRepo.EventKey.SCRIPT_NO_AVAILABLE, props);

                            return;
                        }
                    } catch (Exception e) {
                        WSKLog.e(TAG, "updateScriptPlanAndExecute_onResult -> Exception occurred while processing script data: " + e.getMessage());
                        WSKSDK.notifyError("Exception occurred while processing script data: " + e.getMessage());
                        return;
                    }

                    try {
                        // 记录脚本执行
                        ScriptControl scriptControl = ScriptControl.getInstance(context);
                        int executionCount = scriptControl.recordScriptExecution(scriptData);
                        WSKLog.d(TAG, "updateScriptPlanAndExecute_onResult -> Recording script execution, scriptId=" + scriptData.getMetadata().getId() + ", count=" + executionCount);

                        // 上报脚本执行开始事件
                        Map<String, Object> props = new HashMap<>();
                        props.put(EventRepo.PropKey.SCRIPT_ID, scriptData.getMetadata().getId());
                        props.put(EventRepo.PropKey.EXECUTION_COUNT, executionCount);
                        props.put(EventRepo.PropKey.URL, scriptData.getMetadata().getUrl());
                        EventRepo.reportEvent(EventRepo.EventKey.EXECUTION_START, props);

                        // 设置执行状态
                        isExecuting.set(true);
                        executionStartTime = SystemClock.elapsedRealtime();
                        currentScriptData = scriptData;

                        // 通知监听器开始执行脚本
                        if (taskListener != null) {
                            WSKLog.d(TAG, "updateScriptPlanAndExecute_onResult -> Notifying task listener to start script execution");
                            taskListener.onScriptReady(scriptData);
                        } else {
                            WSKLog.e(TAG, "updateScriptPlanAndExecute_onResult -> Task listener is null, cannot execute script");
                            notifyScriptCompleted();
                        }
                    } catch (Exception e) {
                        WSKLog.e(TAG, "updateScriptPlanAndExecute_onResult -> Exception occurred while executing script: " + e.getMessage());
                        WSKSDK.notifyError("Exception occurred while executing script: " + e.getMessage());
                        notifyScriptCompleted();
                    }
                }
            });
        } catch (Exception e) {
            WSKLog.e(TAG, "updateScriptPlanAndExecute -> Exception occurred while calling ScriptRepo.updatePlanAndSelectScriptData: " + e.getMessage());
            WSKSDK.notifyError("Exception occurred while calling ScriptRepo.updatePlanAndSelectScriptData: " + e.getMessage());
        }
    }

    /**
     * 通知脚本执行完成
     */
    public void notifyScriptCompleted() {
        WSKLog.d(TAG, "notifyScriptCompleted -> Script execution completed");

        // 重置执行状态
        isExecuting.set(false);
        executionStartTime = 0;

        // 记录执行完成时间
        lastExecutionEndTime = SystemClock.elapsedRealtime();
        WSKLog.d(TAG, "notifyScriptCompleted -> Recording execution completion time: " + lastExecutionEndTime);

        currentScriptData = null;
    }


    /**
     * 获取当前脚本数据
     */
    public ScriptData getCurrentScriptData() {
        return currentScriptData;
    }

    /**
     * 脚本任务监听器接口
     */
    public interface ScriptTaskListener {
        /**
         * 当脚本准备好执行时调用
         *
         * @param scriptData 脚本数据
         */
        void onScriptReady(ScriptData scriptData);

        /**
         * 当脚本执行超时时调用
         */
        void onScriptTimeout();
    }
}
