package ai.ad.webview.plugin.repository;

import org.json.JSONObject;

import java.util.HashMap;
import java.util.Map;

import ai.ad.webview.plugin.model.ScriptData;
import ai.ad.webview.plugin.model.ScriptPlan;
import ai.ad.webview.plugin.utils.HttpUtils;
import ai.ad.webview.sdk.logger.WSKLog
        ;

/**
 * ScriptRepo的默认实现
 * <p>
 * https://api.tsykw.com/api/v1/test-cases
 * https://api.tsykw.com/api/v1/test-scripts/{id}
 * https://api.tsykw.com/api/v1/report/execution
 */
public class ScriptRepoImpl extends BaseScriptRepoImpl {
    private final String BASE_URL = ScriptRepo.BASE_URL;  // 请根据实际情况修改

    // 单例实例
    private static final ScriptRepoImpl _instance = new ScriptRepoImpl();

    /**
     * 获取实现类实例
     */
    public static ScriptRepoImpl getInstance() {
        return _instance;
    }

    // 获取测试用例列表
    @Override
    public void getScriptPlan(final Callback<ScriptPlan> callback) {
        // 构建请求体
        JSONObject requestBody = new JSONObject();

        // 记录详细请求信息
        String url = BASE_URL + "/api/v1/test-cases";

        // 发送请求
        HttpUtils.post(url, requestBody, new HttpUtils.Callback() {
            @Override
            public void onResult(HttpUtils.Result result) {
                if (result instanceof HttpUtils.Result.Success) {
                    try {
                        HttpUtils.Result.Success success = (HttpUtils.Result.Success) result;
                        // 数据适配器
                        JSONObject dataObj = success.getData().optJSONObject("data");
                        ScriptPlan scriptPlan = ScriptPlan.fromJson(dataObj);
                        if (scriptPlan != null) {
                            WSKLog.d(TAG, "Script plan parsed successfully, script count: " + scriptPlan.getScripts().size());
                            callback.onResult(scriptPlan);
                        } else {
                            WSKLog.e(TAG, "Script plan parsing result is empty");
                            callback.onResult(null);
                        }
                    } catch (Exception e) {
                        HttpUtils.Result.Success success = (HttpUtils.Result.Success) result;
                        String rawData = success.getData().toString();
                        String truncatedData = rawData.length() > 200 ?
                                rawData.substring(0, 200) + "..." :
                                rawData;

                        WSKLog.e(TAG, "Failed to parse script plan: " + e.getMessage() +
                                "\nRaw response data: " + truncatedData + e.getMessage());

                        // 上报接口错误
                        Map<String, Object> props = new HashMap<>();
                        props.put(EventRepo.PropKey.API_NAME, "get_script_plan");
                        props.put(EventRepo.PropKey.ERROR_CODE, "-1");
                        props.put(EventRepo.PropKey.MESSAGE, "Failed to parse script plan: " + e.getMessage());
                        props.put(EventRepo.PropKey.STATUS, EventRepo.PropValue.STATUS_FAILURE);
                        EventRepo.reportEvent(EventRepo.EventKey.API_ERROR, props);

                        callback.onResult(null);
                    }
                } else if (result instanceof HttpUtils.Result.Error) {
                    HttpUtils.Result.Error error = (HttpUtils.Result.Error) result;
                    // 上报接口错误
                    Map<String, Object> props = new HashMap<>();
                    props.put(EventRepo.PropKey.API_NAME, "get_script_plan");
                    props.put(EventRepo.PropKey.ERROR_CODE, String.valueOf(error.getCode()));
                    props.put(EventRepo.PropKey.MESSAGE, error.getMessage());
                    props.put(EventRepo.PropKey.STATUS, EventRepo.PropValue.STATUS_FAILURE);
                    EventRepo.reportEvent(EventRepo.EventKey.API_ERROR, props);

                    callback.onResult(null);
                }
            }
        });
    }

    // 获取测试脚本
    @Override
    public void getScriptDataById(String scriptId, final Callback<ScriptData> callback) {
        // 构建请求URL
        String url = BASE_URL + "/api/v1/test-scripts/" + scriptId;

        HttpUtils.get(url, new HttpUtils.Callback() {
            @Override
            public void onResult(HttpUtils.Result result) {
                if (result instanceof HttpUtils.Result.Success) {
                    try {
                        HttpUtils.Result.Success success = (HttpUtils.Result.Success) result;
                        // 数据适配器
                        JSONObject dataObj = success.getData().optJSONObject("data");
                        String content = dataObj.optString("content");
                        JSONObject jsonObject = new JSONObject(content);
                        ScriptData scriptData = ScriptData.fromJSONObject(jsonObject);

                        if (scriptData != null && !scriptData.getEvents().isEmpty()) {
                            callback.onResult(scriptData);
                        } else {
                            WSKLog.e(TAG, "Script data parsing result is empty");
                            Map<String, Object> props = new HashMap<>();
                            props.put(EventRepo.PropKey.API_NAME, "get_script_data");
                            props.put(EventRepo.PropKey.ERROR_CODE, "-1");
                            props.put(EventRepo.PropKey.MESSAGE, "Script data parsing result is empty");
                            props.put(EventRepo.PropKey.STATUS, EventRepo.PropValue.STATUS_FAILURE);
                            EventRepo.reportEvent(EventRepo.EventKey.API_ERROR, props);

                            // 使用fromJSONObject的健壮性改进，即使解析失败也返回一个空的ScriptData对象
                            callback.onResult(new ScriptData());
                        }
                    } catch (Exception e) {
                        // 上报接口错误
                        Map<String, Object> props = new HashMap<>();
                        props.put(EventRepo.PropKey.API_NAME, "get_script_data");
                        props.put(EventRepo.PropKey.ERROR_CODE, "-1");
                        props.put(EventRepo.PropKey.MESSAGE, "Failed to parse script data: " + e.getMessage());
                        props.put(EventRepo.PropKey.STATUS, EventRepo.PropValue.STATUS_FAILURE);
                        EventRepo.reportEvent(EventRepo.EventKey.API_ERROR, props);

                        // 返回一个空的ScriptData对象，而不是null
                        callback.onResult(new ScriptData());
                    }
                } else if (result instanceof HttpUtils.Result.Error) {
                    HttpUtils.Result.Error error = (HttpUtils.Result.Error) result;
                    // 上报接口错误
                    Map<String, Object> props = new HashMap<>();
                    props.put(EventRepo.PropKey.API_NAME, "get_script_data");
                    props.put(EventRepo.PropKey.ERROR_CODE, String.valueOf(error.getCode()));
                    props.put(EventRepo.PropKey.MESSAGE, error.getMessage());
                    props.put(EventRepo.PropKey.STATUS, EventRepo.PropValue.STATUS_FAILURE);
                    EventRepo.reportEvent(EventRepo.EventKey.API_ERROR, props);

                    // 返回一个空的ScriptData对象，而不是null
                    callback.onResult(new ScriptData());
                }
            }
        });
    }
}
