package ai.ad.webview.plugin;

import android.app.Activity;
import android.app.Application;
import android.content.Context;

import ai.ad.webview.plugin.dex.WSKResLoader;
import ai.ad.webview.plugin.overlay.InAppOverlayManager;
import ai.ad.webview.plugin.utils.DeviceIdUtil;
import ai.ad.webview.plugin.webview.proxy.ProxyWSKActivity;
import ai.ad.webview.plugin.webview.proxy.ProxyWSKService;
import ai.ad.webview.sdk.api.interfaces.IInAppOverlayManager;
import ai.ad.webview.sdk.api.interfaces.IUpdateCallback;
import ai.ad.webview.sdk.api.interfaces.IWSKActivity;
import ai.ad.webview.sdk.api.interfaces.IWSKCallback;
import ai.ad.webview.sdk.api.interfaces.IWSKSDK;
import ai.ad.webview.sdk.api.interfaces.IWSKService;
import ai.ad.webview.sdk.logger.WSKLog;

public class ProxyWSKSDK implements IWSKSDK {
    private static final String TAG = "ProxyWSKSDK";
    private IWSKCallback callback;
    private boolean _isAttach = false;
    private String appId = ""; // 默认值
    //  base = "1.2.0";
    private static final String mVersion = "1.2.4";
    public static boolean isOverLayMode = false;


    @Override
    public boolean isAttach() {
        return _isAttach;
    }

    @Override
    public void initialize(Application context, String appId, IWSKCallback callback) {
        WSKLog.i(TAG, "initialize -> " + appId);
        this.callback = callback;
        this.appId = appId;
        DeviceIdUtil.preloadGAID(context);

        // 初始化WSKResLoader
        WSKResLoader resLoader = WSKResLoader.getInstance();
        resLoader.init(context, this);
    }

    @Override
    public String getAppId() {
        return appId;
    }

    @Override
    public String version() {
        return mVersion;
    }

    @Override
    public String getDeviceId() {
        return DeviceIdUtil.getDeviceId();
    }

    @Override
    public void attach(Activity hostActivity) {
        _isAttach = true;
    }

    // 是否支持应用外,显示透明的activity方案
    @Override
    public boolean isSupportOutApp() {
        return android.os.Build.VERSION.SDK_INT < 30;
    }

    @Override
    public void detach(Activity hostActivity) {
        _isAttach = false;
    }

    @Override
    public void notifyWebViewLoaded() {
        if (callback != null) {
            callback.onWSKSDKStarted();
        }
    }

    @Override
    public void notifyScriptCompleted() {
        if (callback != null) {
            callback.onWSKSDKCompleted();
        }
    }

    @Override
    public void notifyError(String errorMessage) {
        if (callback != null) {
            callback.onError(errorMessage);
        }
    }

    @Override
    public IWSKActivity createActivityProxy(Context context) {
        return new ProxyWSKActivity(context);
    }

    /**
     * 创建Service代理类实例
     *
     * @param context 上下文
     * @return IWSKService
     */
    @Override
    public IWSKService createServiceProxy(Context context) {
        return new ProxyWSKService();
    }

    @Override
    public IInAppOverlayManager createInAppOverlayManager() {
        return new InAppOverlayManager();
    }

    @Override
    public void checkUpdate(IUpdateCallback callback) {

        // 获取WSKResLoader实例
        WSKResLoader resLoader = WSKResLoader.getInstance();

        // 检查更新频率限制（24小时最多执行一次）
        long lastCheckTime = resLoader.getLastCheckTime();
        long currentTime = System.currentTimeMillis();
        long timeDiff = currentTime - lastCheckTime;
        long twentyFourHoursInMs = 24 * 60 * 60 * 1000; // 24小时

        if (lastCheckTime > 0 && timeDiff < twentyFourHoursInMs) {
            long remainingTime = twentyFourHoursInMs - timeDiff;
            long remainingHours = remainingTime / (60 * 60 * 1000);
            WSKLog.i(TAG, "checkUpdate -> 更新频率限制: 距离上次检查仅 " + (timeDiff / (60 * 60 * 1000)) + " 小时，需等待 " + remainingHours + " 小时后才能再次检查");
            WSKLog.i(TAG, "checkUpdate -> 跳过热更新检查");
            callback.onNoUpdate();
            return;
        }

        // 检查更新
        resLoader.checkUpdate(new IUpdateCallback() {
            @Override
            public void onUpdateAvailable(String latestVersion, String patchUrl, String patchMd5, long patchSize) {
                WSKLog.i(TAG, "checkUpdate -> Update available: " + latestVersion);

                // 自动下载并更新
                resLoader.downloadAndUpdate(latestVersion, patchUrl, patchMd5, appId, new IUpdateCallback() {
                    @Override
                    public void onUpdateAvailable(String latestVersion, String patchUrl, String patchMd5, long patchSize) {
                        // 不会被调用
                    }

                    @Override
                    public void onNoUpdate() {
                        // 不会被调用
                    }

                    @Override
                    public void onUpdateSuccess(String newVersion, String newDexPath) {
                        WSKLog.i(TAG, "checkUpdate -> Download and update successful: " + newVersion);

                        // 清理旧版本文件
                        resLoader.cleanupOldVersions(newVersion);

                        // 回调给外部
                        callback.onUpdateSuccess(newVersion, newDexPath);
                    }

                    @Override
                    public void onUpdateFailed(String error) {
                        WSKLog.e(TAG, "checkUpdate -> Download failed: " + error);
                        callback.onUpdateFailed(error);
                    }
                });

                // 同时回调给外部，告知有更新可用
                callback.onUpdateAvailable(latestVersion, patchUrl, patchMd5, patchSize);
            }

            @Override
            public void onNoUpdate() {
                WSKLog.i(TAG, "checkUpdate -> No update available");
                callback.onNoUpdate();
            }

            @Override
            public void onUpdateSuccess(String newVersion, String newDexPath) {
                // 这个回调在这里不会被调用，因为我们只是检查更新
            }

            @Override
            public void onUpdateFailed(String error) {
                WSKLog.e(TAG, "checkUpdate -> Update check failed: " + error);
                callback.onUpdateFailed(error);
            }
        });
    }

    @Override
    public String getLatestDexPath() {
        WSKResLoader resLoader = WSKResLoader.getInstance();
        return resLoader.getDexPath();
    }
}
