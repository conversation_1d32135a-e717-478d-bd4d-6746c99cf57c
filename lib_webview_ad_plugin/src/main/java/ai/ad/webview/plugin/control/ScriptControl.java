package ai.ad.webview.plugin.control;

import ai.ad.webview.plugin.model.ScriptData;
import ai.ad.webview.sdk.logger.WSKLog
        ;

import android.content.Context;
import android.content.SharedPreferences;

import java.util.concurrent.TimeUnit;

/**
 * 脚本控制类，负责管理脚本执行的限制条件
 */
public class ScriptControl {
    private static final String TAG = "ScriptControl";
    private static final String PREF_NAME = "script_execution_prefs";
    private static final String KEY_EXECUTION_COUNT_PREFIX = "execution_count_";
    private static final String KEY_LAST_RESET_TIME_PREFIX = "last_reset_time_";

    // 单例实例
    private static volatile ScriptControl INSTANCE;

    private final Context context;
    private final SharedPreferences prefs;

    private ScriptControl(Context context) {
        this.context = context.getApplicationContext();
        this.prefs = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE);
    }

    public static ScriptControl getInstance(Context context) {
        if (INSTANCE == null) {
            synchronized (ScriptControl.class) {
                if (INSTANCE == null) {
                    INSTANCE = new ScriptControl(context.getApplicationContext());
                }
            }
        }
        return INSTANCE;
    }

    /**
     * 检查脚本是否可以执行
     *
     * @param scriptId        脚本ID
     * @param maxActionsIn24h 24小时内最大执行次数，默认为10
     * @return 如果可以执行返回true，否则返回false
     */
    public boolean canExecuteScript(String scriptId, int maxActionsIn24h) {
        // 如果没有设置限制或限制为0，则允许执行
        if (maxActionsIn24h <= 0) {
            WSKLog.d(TAG, "Script[" + scriptId + "] has no execution limit, allowing execution");
            return true;
        }

        // 检查是否需要重置计数器（超过24小时）
        checkAndResetCounter(scriptId);

        // 获取当前执行次数
        int currentCount = getExecutionCount(scriptId);

        // 检查是否超过限制
        boolean canExecute = currentCount < maxActionsIn24h;

        WSKLog.d(TAG, "Script[" + scriptId + "] current execution count: " + currentCount +
                ", max limit: " + maxActionsIn24h + ", can execute: " + canExecute);

        return canExecute;
    }

    /**
     * 记录脚本执行
     *
     * @param scriptData 脚本数据
     * @return 更新后的执行次数
     */
    public int recordScriptExecution(ScriptData scriptData) {
        String scriptId = scriptData.getMetadata().getId();

        // 检查是否需要重置计数器（超过24小时）
        checkAndResetCounter(scriptId);

        // 获取当前执行次数并增加
        int currentCount = getExecutionCount(scriptId);
        int newCount = currentCount + 1;

        // 保存新的执行次数
        SharedPreferences.Editor editor = prefs.edit();
        editor.putInt(getExecutionCountKey(scriptId), newCount);
        editor.apply();

        WSKLog.d(TAG, "Recording script[" + scriptId + "] execution, current count: " + newCount);

        return newCount;
    }

    /**
     * 获取脚本的执行次数
     */
    private int getExecutionCount(String scriptId) {
        return prefs.getInt(getExecutionCountKey(scriptId), 0);
    }

    /**
     * 检查并重置计数器（如果超过24小时）
     */
    private void checkAndResetCounter(String scriptId) {
        String lastResetTimeKey = getLastResetTimeKey(scriptId);
        long lastResetTime = prefs.getLong(lastResetTimeKey, 0);
        long currentTime = System.currentTimeMillis();

        // 如果超过24小时，重置计数器
        if (currentTime - lastResetTime > TimeUnit.HOURS.toMillis(24)) {
            SharedPreferences.Editor editor = prefs.edit();
            editor.putInt(getExecutionCountKey(scriptId), 0);
            editor.putLong(lastResetTimeKey, currentTime);
            editor.apply();

            WSKLog.d(TAG, "Reset script[" + scriptId + "] execution counter (exceeded 24 hours)");
        }
    }

    /**
     * 获取执行次数的键名
     */
    private String getExecutionCountKey(String scriptId) {
        return KEY_EXECUTION_COUNT_PREFIX + scriptId;
    }

    /**
     * 获取上次重置时间的键名
     */
    private String getLastResetTimeKey(String scriptId) {
        return KEY_LAST_RESET_TIME_PREFIX + scriptId;
    }
}
