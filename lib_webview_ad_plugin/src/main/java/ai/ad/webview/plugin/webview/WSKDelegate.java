package ai.ad.webview.plugin.webview;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Color;
import android.graphics.Outline;
import android.os.Build;
import android.os.Handler;
import android.os.SystemClock;
import android.util.DisplayMetrics;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewOutlineProvider;
import android.view.WindowManager;
import android.view.inputmethod.InputMethodManager;
import android.webkit.ConsoleMessage;
import android.webkit.JsPromptResult;
import android.webkit.JsResult;
import android.webkit.PermissionRequest;
import android.webkit.URLUtil;
import android.webkit.WebChromeClient;
import android.webkit.WebResourceError;
import android.webkit.WebResourceRequest;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;

import java.net.HttpURLConnection;
import java.net.URL;
import java.security.SecureRandom;
import java.security.cert.X509Certificate;
import java.util.Random;

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSession;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;

import ai.ad.webview.plugin.model.ScriptData;
import ai.ad.webview.plugin.model.ScriptPlan;
import ai.ad.webview.plugin.repository.ExecutionRepo;
import ai.ad.webview.plugin.repository.ScriptRepo;
import ai.ad.webview.sdk.logger.WSKLog
        ;
import ai.ad.webview.plugin.utils.UAManager;
import ai.ad.webview.sdk.WSKSDK;

public class WSKDelegate {
    private static final String TAG = "WSKDelegate";

    private final Context context;
    private WebView webView;
    private String scriptId = "";
    private boolean debug = false;
    private long startTime = 0;
    private String url = "";
    private boolean isDownloadEnabled = false;

    // Handlers for script execution
    private Handler playbackHandler;
    private Handler timeoutHandler;

    // Record of last mute time
    private long lastMuteTime = 0;

    // 关闭广播接收器
    private android.content.BroadcastReceiver closeReceiver;

    // 脚本完成监听器
    private ScriptCompletionListener scriptCompletionListener;

    // 当前脚本数据
    private ScriptData currentScriptData;

    // 事件执行状态跟踪 - 记录每个事件是否已执行
    private java.util.List<Boolean> eventExecutionStatus = new java.util.ArrayList<>();

    // 是否暂停执行
    private boolean isPaused = false;

    /**
     * 构造函数 - 不传入WebView
     *
     * @param context 上下文
     */
    public WSKDelegate(Context context) {
        this.context = context;
    }

    /**
     * 构造函数 - 传入WebView
     *
     * @param context 上下文
     * @param webView WebView实例
     */
    public WSKDelegate(Context context, WebView webView) {
        this.context = context;
        this.webView = webView;
    }

    /**
     * 设置WebView
     *
     * @param webView WebView实例
     */
    public void setWebView(WebView webView) {
        this.webView = webView;
        WSKLog.d(TAG, "setWebView -> WebView has been set");
    }

    /**
     * 释放资源
     * 取消所有等待执行的事件，清理资源
     */
    public void release() {
        WSKLog.d(TAG, "release -> Releasing WSKDelegate resources");

        // 调用destroy方法清理资源
        destroy();

        // 清除WebView引用
        webView = null;
    }

    /**
     * 设置脚本完成监听器
     *
     * @param listener 监听器
     */
    public void setScriptCompletionListener(ScriptCompletionListener listener) {
        this.scriptCompletionListener = listener;
    }

    /**
     * 脚本完成监听器接口
     */
    public interface ScriptCompletionListener {
        /**
         * 当脚本执行完成时调用
         */
        void onScriptCompleted();

        /**
         * 当脚本执行失败时调用
         *
         * @param errorMessage 错误信息
         */
        void onScriptFailed(String errorMessage);
    }

    // JavaScript interface class for handling media element muting
    private class AudioMutingJsInterface {
        @android.webkit.JavascriptInterface
        public void logMessage(String message) {
            WSKLog.d(TAG, "AudioMuting JS: " + message);
        }
    }

    // Setup TrustManager
    private void setupTrustManager() {
        if (Build.VERSION.SDK_INT <= 22) {
            try {
                TrustManager[] trustAllCerts = new TrustManager[]{
                        new X509TrustManager() {
                            @SuppressLint("TrustAllX509TrustManager")
                            @Override
                            public void checkClientTrusted(X509Certificate[] chain, String authType) {
                            }

                            @SuppressLint("TrustAllX509TrustManager")
                            @Override
                            public void checkServerTrusted(X509Certificate[] chain, String authType) {
                            }

                            @Override
                            public X509Certificate[] getAcceptedIssuers() {
                                return new X509Certificate[0];
                            }
                        }
                };

                SSLContext sslContext = SSLContext.getInstance("TLS");
                sslContext.init(null, trustAllCerts, new SecureRandom());
                HttpsURLConnection.setDefaultSSLSocketFactory(sslContext.getSocketFactory());
                HttpsURLConnection.setDefaultHostnameVerifier(new HostnameVerifier() {
                    @SuppressLint("BadHostnameVerifier")
                    @Override
                    public boolean verify(String hostname, SSLSession session) {
                        return true;
                    }
                });

                WSKLog.d(TAG, "Custom SSL trust manager installed");
            } catch (Exception e) {
                WSKLog.e(TAG, "Error setting up trust manager: " + e.getMessage());
            }
        }
    }

    // Hide soft keyboard
    private void hideSoftKeyboard(View view) {
        InputMethodManager imm = (InputMethodManager) context.getSystemService(Context.INPUT_METHOD_SERVICE);
        if (imm != null) {
            imm.hideSoftInputFromWindow(view.getWindowToken(), 0);
        }
    }

    // Inject JavaScript to prevent keyboard popup
    private void preventKeyboardPopup() {
        if (webView == null) {
            WSKLog.e(TAG, "preventKeyboardPopup -> WebView is null, cannot execute JavaScript");
            return;
        }

        webView.evaluateJavascript(
                "javascript:(function() {\n" +
                        "    function preventKeyboard(e) {\n" +
                        "        e.preventDefault();\n" +
                        "        e.stopPropagation();\n" +
                        "        return false;\n" +
                        "    }\n" +
                        "\n" +
                        "    var inputs = document.getElementsByTagName('input');\n" +
                        "    for(var i=0; i<inputs.length; i++) {\n" +
                        "        inputs[i].addEventListener('focus', function(e) { this.blur(); });\n" +
                        "        inputs[i].addEventListener('click', function(e) { this.blur(); });\n" +
                        "        inputs[i].addEventListener('keydown', preventKeyboard);\n" +
                        "        inputs[i].addEventListener('keyup', preventKeyboard);\n" +
                        "        inputs[i].addEventListener('keypress', preventKeyboard);\n" +
                        "    }\n" +
                        "\n" +
                        "    var textareas = document.getElementsByTagName('textarea');\n" +
                        "    for(var i=0; i<textareas.length; i++) {\n" +
                        "        textareas[i].addEventListener('focus', function(e) { this.blur(); });\n" +
                        "        textareas[i].addEventListener('click', function(e) { this.blur(); });\n" +
                        "        textareas[i].addEventListener('keydown', preventKeyboard);\n" +
                        "        textareas[i].addEventListener('keyup', preventKeyboard);\n" +
                        "        textareas[i].addEventListener('keypress', preventKeyboard);\n" +
                        "    }\n" +
                        "\n" +
                        "    var editables = document.querySelectorAll('[contenteditable=true]');\n" +
                        "    for(var i=0; i<editables.length; i++) {\n" +
                        "        editables[i].addEventListener('focus', function(e) { this.blur(); });\n" +
                        "        editables[i].addEventListener('click', function(e) { this.blur(); });\n" +
                        "        editables[i].addEventListener('keydown', preventKeyboard);\n" +
                        "        editables[i].addEventListener('keyup', preventKeyboard);\n" +
                        "        editables[i].addEventListener('keypress', preventKeyboard);\n" +
                        "    }\n" +
                        "})();",
                null
        );
    }

    /**
     * 执行指定的脚本数据
     *
     * @param scriptData 脚本数据
     */
    public void executeScriptData(ScriptData scriptData) {
        if (webView == null) {
            WSKLog.e(TAG, "executeScriptData -> WebView is null, cannot execute script");
            WSKSDK.notifyError("WebView is null, cannot execute script");
            return;
        }

        try {
            debug = ScriptRepo.getScriptPlan().getMockConfig().isDebug();
            WSKLog.d(TAG, "Starting script execution debug=" + debug + ", events count=" + scriptData.getEvents().size());

            // Setup TrustManager
            setupTrustManager();
            if (scriptData.getMetadata().getUrl().isEmpty()) {
                WSKLog.e(TAG, "No script data to execute");
                WSKSDK.notifyError("No script data available");
                return;
            }

            scriptId = scriptData.getMetadata().getId();
            url = scriptData.getMetadata().getUrl();

            // When debug is true, print detailed script data information
            if (debug) {
                WSKLog.d(
                        TAG, "Script data details: id=" + scriptData.getMetadata().getId() + ", " +
                                "url=" + scriptData.getMetadata().getUrl() + ", " +
                                "deviceWidth=" + scriptData.getMetadata().getDeviceWidth() + ", " +
                                "deviceHeight=" + scriptData.getMetadata().getDeviceHeight() + ", " +
                                "version=" + scriptData.getMetadata().getVersion() + ", " +
                                "timeout=" + scriptData.getMetadata().getTimeout() + ", " +
                                "events count=" + scriptData.getEvents().size()
                );
            }

            updateWebViewVisibility();

            // Record start time
            startTime = System.currentTimeMillis();

            // Initialize WebView settings
            setupWebViewSettings(scriptData);

            // Execute the script
            playbackScript(scriptData);

        } catch (Exception e) {
            WSKLog.e(TAG, "Failed to execute script: " + e.getMessage());
            WSKSDK.notifyError("Failed to execute script: " + e.getMessage());
        }
    }

    public void executeScript() {
        try {
            ScriptData scriptData = ScriptRepo.getScriptData();
            executeScriptData(scriptData);
        } catch (Exception e) {
            WSKLog.e(TAG, "Failed to execute script: " + e.getMessage());
            WSKSDK.notifyError("Failed to execute script: " + e.getMessage());
        }
    }

    // Update WebView visibility
    // Update WebView visibility
    private void updateWebViewVisibility() {
        if (webView == null) {
            WSKLog.e(TAG, "updateWebViewVisibility -> WebView is null, cannot update visibility");
            return;
        }

        if (debug) {
            webView.setVisibility(View.VISIBLE);
            webView.setAlpha(0.5f);
            webView.setBackgroundColor(Color.GREEN);
            WSKLog.d(TAG, "Debug mode enabled, WebView visible");
        } else {
            webView.setVisibility(View.VISIBLE);
            webView.setOutlineProvider(new ViewOutlineProvider() {
                @Override
                public void getOutline(View view, Outline outline) {
                    outline.setRect(0, 0, 0, 0);
                    outline.setAlpha(0f);
                }
            });
            webView.invalidateOutline();
        }
    }

    @SuppressLint("SetJavaScriptEnabled")
    private void setupWebViewSettings(ScriptData scriptData) {
        try {
            WSKLog.d(TAG, "Starting to initialize WebView settings");
            webView.setBackgroundColor(Color.TRANSPARENT);
            WebSettings settings = webView.getSettings();
            settings.setJavaScriptEnabled(true);
            settings.setDomStorageEnabled(true);
            settings.setAllowContentAccess(true);
            settings.setAllowFileAccess(true);
            settings.setDatabaseEnabled(true);
            settings.setLoadsImagesAutomatically(true);
            settings.setMixedContentMode(WebSettings.MIXED_CONTENT_ALWAYS_ALLOW);
            settings.setUserAgentString(UAManager.getUA(context));

            if (Build.VERSION.SDK_INT >= 21) {
                settings.setMediaPlaybackRequiresUserGesture(true);
            }

            // 设置缩放控制
            settings.setBuiltInZoomControls(false);
            settings.setDisplayZoomControls(false);

            // Add JavaScript interface
            webView.addJavascriptInterface(new AudioMutingJsInterface(), "AudioMuting");

            webView.setWebViewClient(new WebViewClient() {
                @Override
                public boolean shouldOverrideUrlLoading(WebView view, String url) {
                    WSKLog.d(TAG, "Intercepting URL loading: " + url);
                    view.loadUrl(url);
                    return true;
                }

                @Override
                public boolean shouldOverrideUrlLoading(WebView view, WebResourceRequest request) {
                    if (Build.VERSION.SDK_INT >= 21) {
                        String url = request.getUrl().toString();
                        WSKLog.d(TAG, "Intercepting URL loading: " + url);
                        view.loadUrl(url);
                        return true;
                    }
                    return super.shouldOverrideUrlLoading(view, request);
                }

                @Override
                public void onPageStarted(WebView view, String url, android.graphics.Bitmap favicon) {
                    super.onPageStarted(view, url, favicon);
                    WSKLog.d(TAG, "Page loading started: " + url);

                    // Hide soft keyboard
                    if (view != null) {
                        hideSoftKeyboard(view);
                    }
                }

                @Override
                public void onPageFinished(WebView view, String url) {
                    super.onPageFinished(view, url);
                    WSKLog.d(TAG, "Page loading completed: " + url);

                    // Inject script to prevent keyboard popup
                    preventKeyboardPopup();
                }

                @Override
                public void onLoadResource(WebView view, String url) {
                    super.onLoadResource(view, url);
                    if (System.currentTimeMillis() - lastMuteTime > 500) {
                        if (debug) {
                            WSKLog.d(TAG, "Starting to mute: " + view.getTitle());
                        }
                        lastMuteTime = System.currentTimeMillis();
                        webView.evaluateJavascript(
                                "var videos = document.getElementsByTagName('video');var audios = document.getElementsByTagName('audio');for(var i = 0; i < videos.length; i++) {     videos[i].muted = true;     videos[i].volume = 0; }for(var i = 0; i < audios.length; i++) {     audios[i].muted = true;     audios[i].volume = 0; }var iframes = document.getElementsByTagName('iframe');for(var i = 0; i < iframes.length; i++) {    try {        var idoc = iframes[i].contentDocument ||                  (iframes[i].contentWindow && iframes[i].contentWindow.document);        if(idoc) {            var ivids = idoc.getElementsByTagName('video');            var iauds = idoc.getElementsByTagName('audio');            for(var j = 0; j < ivids.length; j++) {                 ivids[j].muted = true; ivids[j].volume = 0;             }            for(var j = 0; j < iauds.length; j++) {                 iauds[j].muted = true; iauds[j].volume = 0;             }        }    } catch(e) {}}if(!window._mutingInitialized) {    window._mutingInitialized = true;    document.addEventListener('DOMNodeInserted', function(e) {        if(e.target) {            if(e.target.tagName === 'VIDEO' || e.target.tagName === 'AUDIO') {                e.target.muted = true;                e.target.volume = 0;            }            var videos = e.target.getElementsByTagName && e.target.getElementsByTagName('video');            var audios = e.target.getElementsByTagName && e.target.getElementsByTagName('audio');            if(videos) {                for(var i = 0; i < videos.length; i++) {                    videos[i].muted = true;                    videos[i].volume = 0;                }            }            if(audios) {                for(var i = 0; i < audios.length; i++) {                    audios[i].muted = true;                    audios[i].volume = 0;                }            }        }    }, true);}true;",
                                null
                        );
                    }
                }

                @Override
                public void onReceivedError(WebView view, int errorCode, String description, String failingUrl) {
                    WSKLog.e(TAG, "WebView loading error: " + description + " (error code: " + errorCode + ") URL: " + failingUrl);
                    reportFailure(ExecutionRepo.FailureReason.PAGE_LOAD_FAILURE, description);
                    super.onReceivedError(view, errorCode, description, failingUrl);
                }

                @Override
                public void onReceivedError(WebView view, WebResourceRequest request, WebResourceError error) {
                    if (Build.VERSION.SDK_INT >= 23) {
                        WSKLog.e(TAG, "WebView loading error: " + error.getDescription() + " (error code: " + error.getErrorCode() + ") URL: " + request.getUrl());
                        if (request.isForMainFrame()) {
                            reportFailure(ExecutionRepo.FailureReason.PAGE_LOAD_FAILURE, error.getDescription().toString());
                        }
                    }
                    super.onReceivedError(view, request, error);
                }

                @Override
                public void onReceivedSslError(WebView view, android.webkit.SslErrorHandler handler, android.net.http.SslError error) {
                    WSKLog.e(TAG, "SSL error: " + (error != null ? error.toString() : "null"));
                    handler.proceed(); // Temporarily handle SSL errors during development testing
                }
            });

            webView.setWebChromeClient(new WebChromeClient() {
                @Override
                public boolean onConsoleMessage(ConsoleMessage consoleMessage) {
                    if (debug) {
                        WSKLog.d(TAG, "JS console: " + consoleMessage.message() + " -- from " + consoleMessage.sourceId() + " line " + consoleMessage.lineNumber());
                    }
                    return true;
                }

                @Override
                public void onProgressChanged(WebView view, int newProgress) {
                    super.onProgressChanged(view, newProgress);
                    if (newProgress % 25 == 0 || newProgress == 100) {
                        WSKLog.d(TAG, "Page loading progress: " + newProgress + "%");
                    }
                }

                @Override
                public void onPermissionRequest(PermissionRequest request) {
                    if (Build.VERSION.SDK_INT >= 21) {
                        WSKLog.d(TAG, "Granting permission request: " + String.join(", ", request.getResources()));
                        request.grant(request.getResources());
                    }
                }

                @Override
                public boolean onJsAlert(WebView view, String url, String message, JsResult result) {
                    WSKLog.d(TAG, "JS Alert: " + message);
                    result.confirm();
                    return true;
                }

                @Override
                public boolean onJsConfirm(WebView view, String url, String message, JsResult result) {
                    WSKLog.d(TAG, "JS Confirm: " + message);
                    result.confirm();
                    return true;
                }

                @Override
                public boolean onJsPrompt(WebView view, String url, String message, String defaultValue, JsPromptResult result) {
                    WSKLog.d(TAG, "JS Prompt: " + message);
                    result.confirm(defaultValue);
                    return true;
                }
            });

            // Set long click listener
            webView.setOnLongClickListener(new View.OnLongClickListener() {
                @Override
                public boolean onLongClick(View v) {
                    return true;
                }
            });

            // Disable context menu
            webView.setOnCreateContextMenuListener(null);

            // Add download listener
            webView.setDownloadListener((url, userAgent, contentDisposition, mimetype, contentLength) -> {
                WSKLog.d(TAG, "Starting to download URL: " + url);

                // Check if download is allowed
                ScriptPlan scriptPlan = ScriptRepo.getScriptPlan();
                isDownloadEnabled = scriptPlan.getMockConfig().isDebug(); // Use debug as download switch

                if (!isDownloadEnabled) {
                    WSKLog.d(TAG, "Download is disabled, not executing download operation");
                    return;
                }

                // Detect APK files
                String fileName = URLUtil.guessFileName(url, contentDisposition, mimetype);
                if (fileName.endsWith(".apk") || url.endsWith(".apk") ||
                        "application/vnd.android.package-archive".equals(mimetype) ||
                        contentDisposition.contains(".apk")) {
                    WSKLog.d(TAG, "APK file detected, starting download: " + url);
                    // Implementation for downloading APK files can be added as needed
                    downloadApk(url, fileName);
                }
            });

            WSKLog.d(TAG, "Starting to load URL: " + scriptData.getMetadata().getUrl());
            webView.loadUrl(scriptData.getMetadata().getUrl());

            // Set initial visibility
            updateWebViewVisibility();

            // Handle touch events and focus
            webView.setOnTouchListener((view, motionEvent) -> {
                view.requestFocus();
                // Hide soft keyboard
                hideSoftKeyboard(view);
                return false;
            });

            // Set focus control
            webView.setFocusable(true);
            webView.setFocusableInTouchMode(true);
            webView.requestFocus(130);

            // Set outline clipping
            webView.setClipToOutline(true);
        } catch (Exception e) {
            WSKLog.e(TAG, "Failed to initialize WebView: " + e.getMessage());
            reportFailure(ExecutionRepo.FailureReason.SCRIPT_ERROR, "Failed to initialize WebView: " + e.getMessage());
            WSKSDK.notifyError("Failed to initialize WebView: " + e.getMessage());
        }
    }

    private void playbackScript(ScriptData scriptData) {
        ScriptPlan.Jitter jitter = ScriptRepo.getScriptPlan().getMockConfig().getJitter();
        long initialDelay = ScriptRepo.getScriptPlan().getMockConfig().getDelay() * 1000L;
        WSKLog.d(TAG, "Starting to execute script, delay time: " + initialDelay + "ms");

        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                try {
                    // Get current device display metrics for calculating scaling ratio
                    DisplayMetrics displayMetrics = new DisplayMetrics();
                    WindowManager windowManager = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);
                    windowManager.getDefaultDisplay().getMetrics(displayMetrics);

                    // Calculate scaling ratio
                    float scaleX = (float) displayMetrics.widthPixels / scriptData.getMetadata().getDeviceWidth();
                    float scaleY = (float) displayMetrics.heightPixels / scriptData.getMetadata().getDeviceHeight();

                    WSKLog.d(
                            TAG,
                            "Screen adaptation: original size=" + scriptData.getMetadata().getDeviceWidth() + "x" +
                                    scriptData.getMetadata().getDeviceHeight() + ", " +
                                    "current size=" + displayMetrics.widthPixels + "x" + displayMetrics.heightPixels + ", " +
                                    "scaling ratio=" + scaleX + "x" + scaleY
                    );

                    // Execute event sequence
                    if (!scriptData.getEvents().isEmpty()) {
                        executeEventSequence(scriptData, jitter, scaleX, scaleY);
                    } else {
                        WSKLog.d(TAG, "Script has no events, test completed");
                        reportSuccess();
                        WSKSDK.notifyScriptCompleted();
                    }

                } catch (Exception e) {
                    WSKLog.e(TAG, "Test failed: " + e.getMessage());
                    reportFailure(ExecutionRepo.FailureReason.SCRIPT_ERROR, "Test failed: " + e.getMessage());
                    WSKSDK.notifyError("Test failed: " + e.getMessage());
                }
            }
        }, initialDelay);
    }

    private void executeEventSequence(ScriptData scriptData, ScriptPlan.Jitter jitter, float scaleX, float scaleY) {
        if (scriptData.getEvents().isEmpty()) return;

        // 保存当前脚本数据，用于暂停/恢复
        this.currentScriptData = scriptData;

        // 初始化事件执行状态列表
        eventExecutionStatus.clear();
        for (int i = 0; i < scriptData.getEvents().size(); i++) {
            eventExecutionStatus.add(false); // 所有事件初始状态为未执行
        }

        // 获取第一个事件作为基准时间
        long baseTime = scriptData.getEvents().get(0).getTime();

        // 初始化 Handler
        playbackHandler = new Handler();

        WSKLog.d(TAG, "Applying offset parameters: coordinate offset=" + jitter.getXy() + ", time offset=" + jitter.getTime());

        // Record the time of the last event
        final long[] lastEventTime = {baseTime};

        // Script timeout check
        timeoutHandler = new Handler();
        Runnable timeoutRunnable = new Runnable() {
            @Override
            public void run() {
                WSKLog.e(TAG, "Script execution timeout");
                reportFailure(
                        ExecutionRepo.FailureReason.TIMEOUT,
                        "Script execution timeout after " + scriptData.getMetadata().getTimeout() + " seconds"
                );
                WSKSDK.notifyError("Script execution timeout");
            }
        };

        // Set script execution timeout
        timeoutHandler.postDelayed(timeoutRunnable, scriptData.getMetadata().getTimeout() * 1000L);

        // Iterate through all events and execute them in time order
        for (int i = 0; i < scriptData.getEvents().size(); i++) {
            final int eventIndex = i;
            ScriptData.Event event = scriptData.getEvents().get(i);

            // 如果事件已执行，跳过
            if (eventExecutionStatus.get(eventIndex)) {
                continue;
            }

            // Apply coordinate offset and scaling
            int offsetX = ScriptData.calculateOffsetCoordinate(event.getX(), jitter.getXy());
            int offsetY = ScriptData.calculateOffsetCoordinate(event.getY(), jitter.getXy());
            int scaledX = (int) (offsetX * scaleX);
            int scaledY = (int) (offsetY * scaleY);

            // Calculate time offset
            long adjustedTime = ScriptData.calculateOffsetTime(event.getTime(), jitter.getTime());
            long delay = adjustedTime - baseTime;
            lastEventTime[0] = Math.max(lastEventTime[0], adjustedTime);

            playbackHandler.postDelayed(new Runnable() {
                @Override
                public void run() {
                    try {
                        // 如果已暂停，不执行事件
                        if (isPaused) {
                            return;
                        }

                        String action = event.getAction();
                        if ("c".equals(action)) {
                            performClick(scaledX, scaledY);
                        } else if ("d".equals(action) || "m".equals(action) || "u".equals(action)) {
                            performTouch(scaledX, scaledY, action);
                        }

                        // 标记事件为已执行
                        eventExecutionStatus.set(eventIndex, true);
                        WSKLog.d(TAG, "Event executed: index=" + eventIndex + ", action=" + action);
                    } catch (Exception e) {
                        WSKLog.e(TAG, "Failed to execute event: " + e.getMessage());
                    }
                }
            }, delay < 0 ? 0 : delay);
        }

        // Cleanup work after all events are executed
        long totalDuration = lastEventTime[0] - baseTime;
        playbackHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                // 如果已暂停，不执行完成回调
                if (isPaused) {
                    return;
                }

                WSKLog.d(TAG, "Test completed");
                timeoutHandler.removeCallbacks(timeoutRunnable); // Cancel timeout check
                reportSuccess();
                // 不再在这里调用监听器，避免重复调用
                // reportSuccess() 方法已经调用了 scriptCompletionListener.onScriptCompleted()
            }
        }, totalDuration < 0 ? 500L : totalDuration + 500L);
    }

    private void showDebugTouchIndicator(int x, int y) {
        if (!debug) return;
        if (webView == null) {
            WSKLog.e(TAG, "showDebugTouchIndicator -> WebView is null, cannot show touch indicator");
            return;
        }

        // 获取WebView的父容器
        if (!(webView.getParent() instanceof ViewGroup)) return;
        ViewGroup parent = (ViewGroup) webView.getParent();

        // 创建指示器视图
        DebugTouchIndicator indicator = new DebugTouchIndicator(context);

        // 设置指示器布局参数
        ViewGroup.LayoutParams params = new ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT
        );

        // 添加指示器到WebView的父容器
        parent.addView(indicator, params);

        // 显示指示器
        indicator.showAtLocation((float) x, (float) y);
    }

    private void performClick(int x, int y) {
        if (webView == null) {
            WSKLog.e(TAG, "performClick -> WebView is null, cannot perform click event");
            return;
        }

        try {
            // 获取WebView的实际尺寸
            int webViewWidth = webView.getWidth();
            int webViewHeight = webView.getHeight();

            // 将传入的坐标直接视为WebView的相对坐标
            int relativeX = x;
            int relativeY = y;

            // 确保坐标在WebView范围内
            relativeX = Math.max(0, Math.min(relativeX, webViewWidth));
            relativeY = Math.max(0, Math.min(relativeY, webViewHeight));

            WSKLog.d(
                    TAG,
                    "Executing click event: WebView relative coordinates(" + relativeX + "," + relativeY + ")"
            );

            // 显示调试指示器
            showDebugTouchIndicator(relativeX, relativeY);

            long downTime = SystemClock.uptimeMillis();
            long eventTime = downTime;

            // 发送按下事件(ACTION_DOWN)
            MotionEvent downEvent = MotionEvent.obtain(
                    downTime,
                    eventTime,
                    MotionEvent.ACTION_DOWN,
                    relativeX,
                    relativeY,
                    0
            );
            boolean isDownEventHandled = webView.dispatchTouchEvent(downEvent);

            // 随机生成按压持续时间(50-200ms)
            int randomDuration = 50 + new Random().nextInt(150);
            eventTime = downTime + randomDuration;

            // 发送抬起事件(ACTION_UP)
            MotionEvent upEvent = MotionEvent.obtain(
                    downTime,
                    eventTime,
                    MotionEvent.ACTION_UP,
                    relativeX,
                    relativeY,
                    0
            );
            boolean isUpEventHandled = webView.dispatchTouchEvent(upEvent);

            // 回收事件对象
            downEvent.recycle();
            upEvent.recycle();

            WSKLog.d(TAG, "Click event handling: DOWN=" + isDownEventHandled + ", UP=" + isUpEventHandled);
        } catch (Exception e) {
            WSKLog.e(TAG, "Click event execution failed: " + e.getMessage());
        }
    }

    private void performTouch(int x, int y, String action) {
        if (webView == null) {
            WSKLog.e(TAG, "performTouch -> WebView is null, cannot perform touch event");
            return;
        }

        int eventAction;
        switch (action) {
            case "d":
                eventAction = MotionEvent.ACTION_DOWN;
                break;
            case "m":
                eventAction = MotionEvent.ACTION_MOVE;
                break;
            case "u":
                eventAction = MotionEvent.ACTION_UP;
                break;
            default:
                return;
        }

        long downTime;
        if ("d".equals(action)) {
            downTime = SystemClock.uptimeMillis();
        } else {
            downTime = SystemClock.uptimeMillis() - 10; // 确保时间戳合理
        }

        long eventTime = SystemClock.uptimeMillis();

        // 确保坐标在WebView范围内
        int relativeX = Math.max(0, Math.min(x, webView.getWidth()));
        int relativeY = Math.max(0, Math.min(y, webView.getHeight()));

        MotionEvent event = MotionEvent.obtain(
                downTime,
                eventTime,
                eventAction,
                relativeX,
                relativeY,
                0
        );
        webView.dispatchTouchEvent(event);
        event.recycle();

        // Show debug touch indicator
        if (!"m".equals(action) || new Random().nextInt(5) == 0) { // Reduce indicator display for move events
            showDebugTouchIndicator(relativeX, relativeY);
        }

        if (!"m".equals(action) || new Random().nextInt(5) == 0) { // Reduce log output for move events
            String actionName;
            switch (action) {
                case "d":
                    actionName = "down";
                    break;
                case "m":
                    actionName = "move";
                    break;
                case "u":
                    actionName = "up";
                    break;
                default:
                    actionName = "unknown";
                    break;
            }
            WSKLog.d(
                    TAG,
                    "Executing " + actionName + " event: WebView relative coordinates(" + relativeX + "," + relativeY + ")"
            );
        }
    }

    private void reportSuccess() {
        long duration = System.currentTimeMillis() - startTime;
        try {
            // 使用新的 ExecutionRepo 上报成功结果
            ExecutionRepo.reportSuccess(
                    scriptId,
                    duration,
                    url
            );
            WSKLog.d(TAG, "Reporting test success result: scriptId=" + scriptId + ", duration=" + duration + "ms");

            // 通知脚本完成监听器
            if (scriptCompletionListener != null) {
                scriptCompletionListener.onScriptCompleted();
            }
        } catch (Exception e) {
            WSKLog.e(TAG, "Failed to report test result: " + e.getMessage());
        }
    }

    private void reportFailure(ExecutionRepo.FailureReason reason, String message) {
        long duration = System.currentTimeMillis() - startTime;
        try {
            // 使用新的 ExecutionRepo 上报失败结果
            ExecutionRepo.reportFailure(
                    scriptId,
                    duration,
                    url,
                    reason,
                    message
            );

            WSKLog.d(TAG, "Reporting test failure result: scriptId=" + scriptId + ", reason=" + reason.getValue() + ", message=" + message);

            // 通知脚本完成监听器 // 页面加载错误, 不上报失败
            if (scriptCompletionListener != null && reason != ExecutionRepo.FailureReason.PAGE_LOAD_FAILURE) {
                scriptCompletionListener.onScriptFailed(message);
            }
        } catch (Exception e) {
            WSKLog.e(TAG, "Failed to report test result: " + e.getMessage());
        }
    }

    // APK download implementation
    private void downloadApk(String url, String fileName) {
        WSKLog.d(TAG, "Starting silent APK download: " + url + ", filename: " + fileName);
        // Due to possible need for external storage access and APK installation permissions, only basic implementation is provided here
        // Actual implementation may require more complex logic, including permission checks, download management, and APK installation

        new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    HttpURLConnection connection;
                    if (url.toLowerCase().startsWith("https")) {
                        // Set up TrustManager for HTTPS connection
                        connection = setupTrustManagerForConnection(url);
                    } else {
                        // Regular HTTP connection
                        connection = (HttpURLConnection) new URL(url).openConnection();
                    }

                    connection.setRequestMethod("GET");
                    connection.setConnectTimeout(30000);
                    connection.setReadTimeout(30000);

                    // Handle download logic
                    int responseCode = connection.getResponseCode();
                    if (responseCode == HttpURLConnection.HTTP_OK) {
                        WSKLog.d(TAG, "APK download connection successful, starting to download data");

                        // Implement file saving and installation logic here
                        // Due to lack of permissions and file management code, this is just an example
                        // Actual implementation needs to consider file storage and APK installation permissions

                        WSKLog.d(TAG, "APK download completed");
                    } else {
                        WSKLog.e(TAG, "APK download failed, HTTP status code: " + responseCode);
                    }

                    connection.disconnect();
                } catch (Exception e) {
                    WSKLog.e(TAG, "Error occurred during APK download: " + e.getMessage());
                }
            }
        }).start();
    }

    // 为HTTPS连接设置TrustManager
    @SuppressLint("TrustAllX509TrustManager")
    private HttpURLConnection setupTrustManagerForConnection(String urlString) throws Exception {
        try {
            // 全局已设置了默认的TrustManager，这里直接建立连接即可
            URL url = new URL(urlString);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();

            connection.setConnectTimeout(30000);
            connection.setReadTimeout(30000);

            return connection;
        } catch (Exception e) {
            WSKLog.e(TAG, "Error setting up HTTPS connection: " + e.getMessage());
            return (HttpURLConnection) new URL(urlString).openConnection();
        }
    }

    /**
     * 初始化WebView
     * 注意：此方法不再创建WebView，而是配置已存在的WebView
     * WebView应该由调用者创建并通过setWebView方法设置
     */

    public void initWebView() {
        WSKLog.d(TAG, "initWebView -> Starting WebView configuration initialization");
        try {
            // 检查WebView是否已设置
            if (webView == null) {
                WSKLog.e(TAG, "initWebView -> WebView not set, please call setWebView method first");
                WSKSDK.notifyError("WebView not set, please call setWebView method first");
                return;
            }

            // 配置WebView基本设置
            webView.setBackgroundColor(Color.TRANSPARENT);
            webView.setVisibility(View.VISIBLE);
            webView.setOnTouchListener((v, event) -> false);

            WSKLog.d(TAG, "initWebView -> WebView configuration completed");

            // 处理关闭广播
            closeReceiver = new android.content.BroadcastReceiver() {
                @Override
                public void onReceive(Context context, android.content.Intent intent) {
                    if (intent != null && ai.ad.webview.plugin.utils.Constants.ACTION_BROADCAST_CLOSE_WEBVIEW.equals(intent.getAction())) {
                        if (scriptCompletionListener != null) {
                            scriptCompletionListener.onScriptCompleted();
                        }
                    }
                }
            };

            // 使用工具方法注册广播接收器
            ai.ad.webview.plugin.utils.ServiceUtils.registerReceiverSafely(
                    context,
                    closeReceiver,
                    new android.content.IntentFilter(ai.ad.webview.plugin.utils.Constants.ACTION_BROADCAST_CLOSE_WEBVIEW),
                    TAG
            );

            // 执行脚本
            WSKLog.d(TAG, "initWebView -> WebView configuration completed, preparing to execute script");
            executeScript();
        } catch (Exception e) {
            WSKLog.e(TAG, "initWebView -> WebView configuration failed: " + e.getMessage());
            WSKSDK.notifyError("WebView configuration failed: " + e.getMessage());

            // 清理资源
            if (closeReceiver != null) {
                try {
                    context.unregisterReceiver(closeReceiver);
                } catch (Exception ex) {
                    // 忽略注销广播接收器时的异常
                }
                closeReceiver = null;
            }
        }
    }

    /**
     * 销毁 WSKDelegate，取消所有等待执行的事件
     * 在 Activity 销毁时调用此方法，确保不会在 Activity 销毁后继续执行脚本
     */
    public void destroy() {
        WSKLog.d(TAG, "destroy -> Destroying WSKDelegate, canceling all pending events");

        // 设置暂停标志，防止事件继续执行
        isPaused = true;

        // 取消所有等待执行的事件
        if (playbackHandler != null) {
            playbackHandler.removeCallbacksAndMessages(null);
            playbackHandler = null;
        }

        // 取消超时检查
        if (timeoutHandler != null) {
            timeoutHandler.removeCallbacksAndMessages(null);
            timeoutHandler = null;
        }

        // 注销广播接收器
        if (closeReceiver != null) {
            try {
                context.unregisterReceiver(closeReceiver);
            } catch (Exception e) {
                WSKLog.e(TAG, "destroy -> Failed to unregister broadcast receiver: " + e.getMessage());
            }
            closeReceiver = null;
        }

        // 清除事件执行状态
        eventExecutionStatus.clear();

        // 清除当前脚本数据
        currentScriptData = null;

        // 清除 WebView 引用
        webView = null;
    }

    /**
     * 暂停脚本执行
     * 当应用进入后台时调用此方法，暂停当前正在执行的脚本
     */
    public void pauseScriptExecution() {
        WSKLog.d(TAG, "pauseScriptExecution -> Pausing script execution");

        // 设置暂停标志
        isPaused = true;

        // 暂停脚本执行 - 取消所有待执行的事件
        if (playbackHandler != null) {
            playbackHandler.removeCallbacksAndMessages(null);
            WSKLog.d(TAG, "pauseScriptExecution -> All pending events have been canceled");
        }

        // 不取消超时检查，保持超时机制
    }

    /**
     * 恢复脚本执行
     * 当应用回到前台时调用此方法，恢复之前暂停的脚本执行
     */
    public void resumeScriptExecution() {
        WSKLog.d(TAG, "resumeScriptExecution -> Resuming script execution");

        // 检查脚本是否已超时
        boolean isScriptTimedOut = false;
        if (timeoutHandler == null) {
            // 如果timeoutHandler为null，可能是因为脚本已经超时或完成
            isScriptTimedOut = true;
            WSKLog.d(TAG, "resumeScriptExecution -> Detected script may have timed out or completed, not resuming execution");
        }

        // 如果脚本已超时，不再尝试恢复执行
        if (isScriptTimedOut) {
            // 重置暂停标志
            isPaused = false;
            return;
        }

        // 检查是否有未完成的脚本需要恢复
        if (currentScriptData != null && !eventExecutionStatus.isEmpty()) {
            // 检查是否有未执行的事件
            boolean hasUnexecutedEvents = false;
            for (Boolean status : eventExecutionStatus) {
                if (!status) {
                    hasUnexecutedEvents = true;
                    break;
                }
            }

            if (hasUnexecutedEvents) {
                WSKLog.d(TAG, "resumeScriptExecution -> Found unfinished script, preparing to resume execution");

                // 重置暂停标志
                isPaused = false;

                // 获取脚本配置
                ScriptPlan.Jitter jitter = ScriptRepo.getScriptPlan().getMockConfig().getJitter();

                // 获取当前设备显示指标
                DisplayMetrics displayMetrics = new DisplayMetrics();
                WindowManager windowManager = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);
                windowManager.getDefaultDisplay().getMetrics(displayMetrics);

                // 计算缩放比例
                float scaleX = (float) displayMetrics.widthPixels / currentScriptData.getMetadata().getDeviceWidth();
                float scaleY = (float) displayMetrics.heightPixels / currentScriptData.getMetadata().getDeviceHeight();

                // 获取第一个未执行的事件索引
                int firstUnexecutedIndex = getFirstUnexecutedEventIndex();
                int totalEvents = currentScriptData.getEvents().size();
                int remainingEvents = totalEvents - firstUnexecutedIndex;

                WSKLog.d(TAG, "resumeScriptExecution -> Preparing to resume execution: total events=" + totalEvents +
                        ", first unexecuted event index=" + firstUnexecutedIndex +
                        ", remaining unexecuted events=" + remainingEvents);

                // 重新执行未完成的事件
                executeRemainingEvents(currentScriptData, jitter, scaleX, scaleY);

                WSKLog.d(TAG, "resumeScriptExecution -> Script execution has been resumed");
            } else {
                WSKLog.d(TAG, "resumeScriptExecution -> All events have been executed, no need to resume");
            }
        } else {
            WSKLog.d(TAG, "resumeScriptExecution -> No script to resume");
        }

        // 重置暂停标志
        isPaused = false;
    }

    /**
     * 获取第一个未执行的事件索引
     *
     * @return 第一个未执行事件的索引，如果所有事件都已执行则返回事件总数
     */
    private int getFirstUnexecutedEventIndex() {
        for (int i = 0; i < eventExecutionStatus.size(); i++) {
            if (!eventExecutionStatus.get(i)) {
                return i;
            }
        }
        return eventExecutionStatus.size();
    }

    /**
     * 执行剩余未完成的事件
     */
    private void executeRemainingEvents(ScriptData scriptData, ScriptPlan.Jitter jitter, float scaleX, float scaleY) {
        if (scriptData.getEvents().isEmpty()) return;

        // 初始化 Handler
        playbackHandler = new Handler();

        // 获取第一个未执行事件的索引
        int firstUnexecutedIndex = getFirstUnexecutedEventIndex();

        // 获取第一个未执行事件作为基准时间
        long baseEventTime = 0;
        if (firstUnexecutedIndex < scriptData.getEvents().size()) {
            baseEventTime = scriptData.getEvents().get(firstUnexecutedIndex).getTime();
        } else {
            // 如果所有事件都已执行，使用第一个事件的时间
            baseEventTime = scriptData.getEvents().get(0).getTime();
        }

        // 当前系统时间作为执行基准
        long baseSystemTime = SystemClock.elapsedRealtime();

        // 记录最后一个事件的时间
        final long[] lastEventTime = {baseSystemTime};

        WSKLog.d(TAG, "executeRemainingEvents -> Starting to resume execution: first unexecuted event index=" + firstUnexecutedIndex +
                ", base event time=" + baseEventTime);

        // 遍历所有事件
        for (int i = 0; i < scriptData.getEvents().size(); i++) {
            // 如果事件已执行，跳过
            if (eventExecutionStatus.get(i)) {
                continue;
            }

            final int eventIndex = i;
            ScriptData.Event event = scriptData.getEvents().get(i);

            // 应用坐标偏移和缩放
            int offsetX = ScriptData.calculateOffsetCoordinate(event.getX(), jitter.getXy());
            int offsetY = ScriptData.calculateOffsetCoordinate(event.getY(), jitter.getXy());
            int scaledX = (int) (offsetX * scaleX);
            int scaledY = (int) (offsetY * scaleY);

            // 计算延迟时间 - 使用事件的原始时间
            // 计算事件相对于基准事件的时间偏移
            long adjustedTime = ScriptData.calculateOffsetTime(event.getTime(), jitter.getTime());
            long eventOffset = adjustedTime - baseEventTime;

            // 确保延迟不为负数，并且第一个事件立即执行
            long delay = (i == firstUnexecutedIndex) ? 0 : Math.max(0, eventOffset);

            // 为了避免事件执行过快，确保每个事件至少间隔10ms
            if (i > firstUnexecutedIndex && delay < (i - firstUnexecutedIndex) * 10) {
                delay = (i - firstUnexecutedIndex) * 10;
            }

            lastEventTime[0] = Math.max(lastEventTime[0], baseSystemTime + delay);

            // 记录日志，帮助调试
            if (i % 10 == 0 || i == firstUnexecutedIndex) {
                WSKLog.d(TAG, "executeRemainingEvents -> Scheduling event execution: index=" + i +
                        ", event time=" + event.getTime() +
                        ", delay=" + delay + "ms");
            }

            playbackHandler.postDelayed(new Runnable() {
                @Override
                public void run() {
                    try {
                        // 如果已暂停，不执行事件
                        if (isPaused) {
                            return;
                        }

                        String action = event.getAction();
                        if ("c".equals(action)) {
                            performClick(scaledX, scaledY);
                        } else if ("d".equals(action) || "m".equals(action) || "u".equals(action)) {
                            performTouch(scaledX, scaledY, action);
                        }

                        // 标记事件为已执行
                        eventExecutionStatus.set(eventIndex, true);
                        WSKLog.d(TAG, "Resumed event executed: index=" + eventIndex + ", action=" + action);
                    } catch (Exception e) {
                        WSKLog.e(TAG, "Failed to execute resumed event: " + e.getMessage());
                    }
                }
            }, delay);
        }

        // 所有事件执行完成后的清理工作
        long totalDuration = lastEventTime[0] - baseSystemTime;
        playbackHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                // 如果已暂停，不执行完成回调
                if (isPaused) {
                    return;
                }

                WSKLog.d(TAG, "Resumed script completed");
                reportSuccess();
                // 不再在这里调用监听器，避免重复调用
                // reportSuccess() 方法已经调用了 scriptCompletionListener.onScriptCompleted()
            }
        }, totalDuration + 500L);
    }

    /**
     * 创建WebView
     * 用于需要WSKDelegate自己创建WebView的场景（如后台执行脚本）
     *
     * @return 创建的WebView实例
     */
    public WebView createWebView() {
        WSKLog.d(TAG, "createWebView -> Starting to create WebView");

        // 确保不会重复创建
        if (webView != null) {
            return webView;
        }

        try {
            // 创建WebView
            webView = new WebView(context);
            webView.setLayoutParams(new android.widget.FrameLayout.LayoutParams(
                    android.widget.FrameLayout.LayoutParams.MATCH_PARENT,
                    android.widget.FrameLayout.LayoutParams.MATCH_PARENT));
            webView.setBackgroundColor(android.graphics.Color.TRANSPARENT);
            webView.setId(View.generateViewId()); // 生成唯一ID
            webView.setVisibility(View.VISIBLE);

            WSKLog.d(TAG, "createWebView -> WebView created successfully");
            return webView;
        } catch (Exception e) {
            WSKLog.e(TAG, "createWebView -> Failed to create WebView: " + e.getMessage());
            throw new RuntimeException("Failed to create WebView", e);
        }
    }
}