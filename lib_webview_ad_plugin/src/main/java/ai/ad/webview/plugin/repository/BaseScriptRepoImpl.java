package ai.ad.webview.plugin.repository;

import android.content.Context;
import android.content.SharedPreferences;

import org.json.JSONException;
import org.json.JSONObject;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.TimeZone;

import ai.ad.webview.plugin.control.ScriptControl;
import ai.ad.webview.plugin.model.ScriptData;
import ai.ad.webview.plugin.model.ScriptPlan;
import ai.ad.webview.plugin.utils.DeviceIdUtil;
import ai.ad.webview.sdk.logger.WSKLog
        ;
import ai.ad.webview.plugin.utils.UAManager;

/**
 * Script repository base class, provides common implementations and abstract methods
 */
public abstract class BaseScriptRepoImpl {
    protected final String TAG = "BaseScriptRepoImpl";
    private static final String PREF_NAME = "script_plan_cache";
    private static final String KEY_PLAN_DATA = "plan_data";
    private static final String KEY_EXPIRE_TIME = "expire_time";

    protected ScriptData _scriptData = new ScriptData();
    protected ScriptPlan _scriptPlan = new ScriptPlan();
    protected long _scriptPlanExpireTime = 0L;

    /**
     * Set script data
     */
    public void setScriptData(ScriptData data) {
        _scriptData = data;
    }

    /**
     * Get script data
     */
    public ScriptData getScriptData() {
        return _scriptData;
    }

    /**
     * Get script plan
     */
    public ScriptPlan getScriptPlan() {
        return _scriptPlan;
    }

    /**
     * Convert timestamp to Beijing time string
     */
    private String timestampToBeijingTime(long timestamp) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.CHINA);
        sdf.setTimeZone(TimeZone.getTimeZone("GMT+8"));
        return sdf.format(new Date(timestamp));
    }

    /**
     * Load cached script plan from SharedPreferences
     */
    protected boolean loadCachedPlan(Context context) {
        try {
            SharedPreferences prefs = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE);
            long expireTime = prefs.getLong(KEY_EXPIRE_TIME, 0);
            boolean isExpired = System.currentTimeMillis() > expireTime;
            String beijingTime = timestampToBeijingTime(expireTime);
            WSKLog.i(TAG, "loadCachedPlan: expireTime: " + expireTime + ", Beijing time: " + beijingTime + ", isExpired: " + isExpired);
            // Check if expired
            if (isExpired) {
                return false;
            }

            String encryptedPlanJson = prefs.getString(KEY_PLAN_DATA, null);
            if (encryptedPlanJson == null) {
                return false;
            }

            try {
                String planJson = ScriptRepo.decryptText(encryptedPlanJson);
                if (planJson == null) {
                    WSKLog.e(TAG, "Failed to decrypt cached script plan");
                    return false;
                }

                JSONObject jsonObject = new JSONObject(planJson);
                _scriptPlan = ScriptPlan.fromJSONObject(jsonObject);
                _scriptPlanExpireTime = expireTime;
                if (_scriptPlan.getScripts().isEmpty()) {
                    WSKLog.d(TAG, "Failed to load and decrypt script plan from cache, script count is 0");
                    return false;
                } else {
                    WSKLog.d(TAG, "loadCachedPlan succeed -> script count: " + _scriptPlan.getScripts().size());
                    return true;
                }
            } catch (Exception e) {
                WSKLog.e(TAG, "Failed to parse cached script plan" + e.getMessage());
                return false;
            }
        } catch (Exception e) {
            WSKLog.e(TAG, "Failed to load cached script plan" + e.getMessage());
            return false;
        }
    }

    /**
     * Cache script plan to SharedPreferences
     */
    protected void cachePlan(Context context, ScriptPlan plan, long expireTimeMs) {
        try {
            SharedPreferences prefs = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE);
            SharedPreferences.Editor editor = prefs.edit();

            try {
                String planJson = plan.toJSONObject().toString();

                // Encrypt data
                String encryptedPlanJson = ScriptRepo.encryptText(planJson);
                if (encryptedPlanJson == null) {
                    WSKLog.e(TAG, "Failed to encrypt script plan");
                    return;
                }

                editor.putString(KEY_PLAN_DATA, encryptedPlanJson);
                editor.putLong(KEY_EXPIRE_TIME, expireTimeMs);
                editor.apply();
            } catch (JSONException e) {
                WSKLog.e(TAG, "Failed to convert script plan to JSON" + e.getMessage());
                return;
            }

            String beijingTime = timestampToBeijingTime(expireTimeMs);
            WSKLog.d(TAG, "cachePlan -> expiration time: " + expireTimeMs + ", Beijing time: " + beijingTime);
        } catch (Exception e) {
            WSKLog.e(TAG, "Failed to cache script plan" + e.getMessage());
        }
    }

    /**
     * Update script plan, first try to get from local cache, if expired or invalid then get from remote
     *
     * @param inContext Application Context
     * @param callback  Callback function, returns whether the plan was successfully updated
     */
    public void updatePlan(final Context inContext, final Callback<Boolean> callback) {
        if (inContext == null) {
            WSKLog.e(TAG, "Failed to update script plan: application context is null");
            callback.onResult(false);
            return;
        }

        // First try to load from cache
        if (loadCachedPlan(inContext)) {
            WSKLog.d(TAG, "Successfully loaded valid script plan from cache");
            callback.onResult(true);
            return;
        }

        // Cache is invalid, get from remote
        WSKLog.d(TAG, "Cache is invalid, getting script plan from remote");

        // Request remote script plan
        getScriptPlan(new Callback<ScriptPlan>() {
            @Override
            public void onResult(ScriptPlan scriptPlan) {
                if (scriptPlan != null && scriptPlan.getMockConfig() != null) {
                    UAManager.saveUA(inContext, scriptPlan.getMockConfig().getUa());
                }

                if (scriptPlan == null) {
                    WSKLog.e(TAG, "getScriptPlan -> Failed to get remote script plan");
                    callback.onResult(false);
                    return;
                }

                // Calculate expiration time
                long expireTimeMs = System.currentTimeMillis() + (scriptPlan.getMockConfig().getExpire() * 1000L);
                // Save to memory and cache
                _scriptPlan = scriptPlan;
                _scriptPlanExpireTime = expireTimeMs;
                cachePlan(inContext, scriptPlan, expireTimeMs);
                callback.onResult(true);
            }
        });
    }

    /**
     * 获取脚本计划
     */
    public abstract void getScriptPlan(Callback<ScriptPlan> callback);

    /**
     * Randomly select a script, filtering out scripts that have exceeded the maximum number of runs in 24 hours
     *
     * @param context Context
     * @return Selected script, returns null if no scripts are available
     */
    public ScriptPlan.Script selectRandomScript(Context context) {
        List<ScriptPlan.Script> allScripts = _scriptPlan.getScripts();
        if (allScripts.isEmpty()) {
            Map<String, Object> props = new HashMap<>();
            props.put(EventRepo.PropKey.DEVICE_ID, DeviceIdUtil.getDeviceId());
            props.put(EventRepo.PropKey.SCRIPT_ID, "");
            props.put(EventRepo.PropKey.MESSAGE, "Script list returned by API is empty");
            EventRepo.reportEvent(EventRepo.EventKey.SCRIPT_NO_AVAILABLE, props);
            return null;
        }

        // Filter out scripts that have exceeded the maximum number of runs in 24 hours
        ScriptControl scriptControl = ScriptControl.getInstance(context);
        List<ScriptPlan.Script> availableScripts = new ArrayList<>();

        for (ScriptPlan.Script script : allScripts) {
            // Get max_actions_in_24h from ScriptPlan.Script
            int maxActionsIn24h = script.getMaxActionsIn24h();
            if (scriptControl.canExecuteScript(script.getId(), maxActionsIn24h)) {
                availableScripts.add(script);
            } else {
                WSKLog.d(TAG, "script[" + script.getId() + "] has reached max execution limit in 24h (" +
                        script.getMaxActionsIn24h() + "), skipping execution");
            }
        }

        // If no scripts are available, return null
        if (availableScripts.isEmpty()) {
            WSKLog.e(TAG, "No available scripts (all scripts have reached the maximum execution count in 24 hours)");
            Map<String, Object> props = new HashMap<>();
            props.put(EventRepo.PropKey.DEVICE_ID, DeviceIdUtil.getDeviceId());
            props.put(EventRepo.PropKey.SCRIPT_ID, "");
            props.put(EventRepo.PropKey.MESSAGE, "No available scripts (all scripts have reached the maximum execution count in 24 hours)");
            EventRepo.reportEvent(EventRepo.EventKey.SCRIPT_NO_AVAILABLE, props);
            return null;
        }

        // Calculate total weight
        double totalWeight = 0;
        for (ScriptPlan.Script script : availableScripts) {
            totalWeight += script.getWeight();
        }

        if (totalWeight <= 0) {
            return availableScripts.isEmpty() ? null : availableScripts.get(0);
        }

        // Random selection based on weight
        double random = Math.random() * totalWeight;
        double currentWeight = 0.0;

        for (ScriptPlan.Script script : availableScripts) {
            currentWeight += script.getWeight();
            if (random <= currentWeight) {
                return script;
            }
        }

        // If no script is selected (theoretically should not happen), return the first one
        return availableScripts.isEmpty() ? null : availableScripts.get(0);
    }

    /**
     * 根据ID获取脚本数据
     */
    public abstract void getScriptDataById(String scriptId, Callback<ScriptData> callback);

    /**
     * Callback interface for asynchronous operations
     */
    public interface Callback<T> {
        void onResult(T result);
    }
}
