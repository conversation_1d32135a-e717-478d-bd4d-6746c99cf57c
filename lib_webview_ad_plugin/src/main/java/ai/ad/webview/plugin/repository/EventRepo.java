package ai.ad.webview.plugin.repository;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.Map;

import ai.ad.webview.plugin.utils.DeviceIdUtil;
import ai.ad.webview.plugin.utils.HttpUtils;
import ai.ad.webview.sdk.logger.WSKLog
        ;

/**
 * 报告仓库对象，用于处理各种事件上报
 * <p>
 * 接口地址: https://api.tsykw.com/api/v1/event
 */
public class EventRepo {
    private static final String TAG = "EventRepo";
    private static final String BASE_URL = ScriptRepo.BASE_URL;

    private EventRepo() {
        // 私有构造函数，防止实例化
    }

    /**
     * 事件类型常量
     */
    public static class EventKey {
        public static final String EXECUTION_START = "execution_start"; // 脚本开始执行
        public static final String API_ERROR = "api_error"; // 接口报错
        public static final String SCRIPT_NO_AVAILABLE = "script_not_available"; // 脚本获取不到

        private EventKey() {
            // 私有构造函数，防止实例化
        }
    }

    /**
     * 属性键常量
     */
    public static class PropKey {
        public static final String DEVICE_ID = "deviceId";
        public static final String SCRIPT_ID = "script_id";
        public static final String EXECUTION_COUNT = "execution_count";
        public static final String URL = "url";
        public static final String STATUS = "status";
        public static final String DURATION = "duration";
        public static final String FAILURE_INFO = "failure_info";
        public static final String REASON = "reason";
        public static final String MESSAGE = "message";
        public static final String TIMESTAMP = "timestamp";
        public static final String API_NAME = "api_name";
        public static final String ERROR_CODE = "error_code";
        public static final String RECORD_ID = "record_id";
        public static final String START_ID = "start_id";  // 添加 start_id 参数

        private PropKey() {
            // 私有构造函数，防止实例化
        }
    }

    /**
     * 状态值常量
     */
    public static class PropValue {
        public static final String STATUS_SUCCESS = "success";
        public static final String STATUS_FAILURE = "failure";

        private PropValue() {
            // 私有构造函数，防止实例化
        }
    }

    /**
     * 通用事件上报方法
     * <p>
     * 这是唯一的事件上报入口，所有事件上报都通过此方法进行
     *
     * @param eventKey   事件标识
     * @param eventProps 事件属性，键值对形式
     */
    public static void reportEvent(String eventKey, Map<String, Object> eventProps) {
        try {
            // 构建完整的请求属性
            HashMap<String, Object> completeProps = new HashMap<>();

            // 添加事件类型
            completeProps.put("event", eventKey);

            // 添加所有传入的属性
            completeProps.putAll(eventProps);

            // 确保包含设备ID
            if (!completeProps.containsKey(PropKey.DEVICE_ID)) {
                completeProps.put(PropKey.DEVICE_ID, DeviceIdUtil.getDeviceId());
            }

            // 确保包含所有必需参数，如果没有则设置为空字符串
            if (!completeProps.containsKey(PropKey.SCRIPT_ID))
                completeProps.put(PropKey.SCRIPT_ID, "");
            if (!completeProps.containsKey(PropKey.STATUS))
                completeProps.put(PropKey.STATUS, PropValue.STATUS_SUCCESS);
            if (!completeProps.containsKey(PropKey.URL)) completeProps.put(PropKey.URL, "");

            // 确保包含duration参数
            if (!completeProps.containsKey(PropKey.DURATION)) {
                completeProps.put(PropKey.DURATION, 0);
            }

            // 如果状态是failure但没有failure_info，则添加空的failure_info
            if (PropValue.STATUS_FAILURE.equals(completeProps.get(PropKey.STATUS)) && !completeProps.containsKey(PropKey.FAILURE_INFO)) {
                // 从属性中提取失败原因和消息
                String reason = completeProps.containsKey(PropKey.REASON) ? completeProps.get(PropKey.REASON).toString() : "";
                String message = completeProps.containsKey(PropKey.MESSAGE) ? completeProps.get(PropKey.MESSAGE).toString() : "";

                // 创建failure_info对象
                JSONObject failureInfo = new JSONObject();
                try {
                    failureInfo.put(PropKey.REASON, reason);
                    failureInfo.put(PropKey.MESSAGE, message);
                } catch (JSONException e) {
                    WSKLog.e(TAG, "Failed to create failure_info JSON" + e.getMessage());
                }

                // 添加到属性中并移除原始属性
                completeProps.put(PropKey.FAILURE_INFO, failureInfo);
                completeProps.remove(PropKey.REASON);
                completeProps.remove(PropKey.MESSAGE);
            }

            // 添加时间戳
            if (!completeProps.containsKey(PropKey.TIMESTAMP)) {
                completeProps.put(PropKey.TIMESTAMP, System.currentTimeMillis());
            }

            // 构建请求体
            JSONObject requestBody = new JSONObject();
            for (Map.Entry<String, Object> entry : completeProps.entrySet()) {
                try {
                    requestBody.put(entry.getKey(), entry.getValue());
                } catch (JSONException e) {
                    WSKLog.e(TAG, "Failed to add property to request body: " + entry.getKey() + e.getMessage());
                }
            }

            // 记录详细请求信息
            String url = BASE_URL + "/api/v1/event";

            // 发送请求
            HttpUtils.post(url, requestBody, result -> {
                // 空回调实现
            });
        } catch (Exception e) {
            WSKLog.e(TAG, "Event reporting exception: " + eventKey + e.getMessage());
        }
    }
}
