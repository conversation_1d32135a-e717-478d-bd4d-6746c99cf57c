package ai.ad.webview.plugin.webview;

import android.animation.ValueAnimator;
import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.LinearInterpolator;

public class DebugTouchIndicator extends View {
    private final Paint paint;

    private float centerX = 0f;
    private float centerY = 0f;
    private float radius = 15f;  // 红点半径
    private int currentAlpha = 255;
    private ValueAnimator animator;

    public DebugTouchIndicator(Context context) {
        super(context);
        paint = new Paint();
        paint.setColor(Color.RED);
        paint.setAlpha(255);
        paint.setAntiAlias(true);
        paint.setStyle(Paint.Style.FILL);
    }

    public void showAtLocation(float x, float y) {
        centerX = x;
        centerY = y;

        // 取消之前的动画
        if (animator != null) {
            animator.cancel();
        }

        // 重置透明度
        currentAlpha = 255;
        paint.setAlpha(currentAlpha);

        // 创建淡出动画
        animator = ValueAnimator.ofInt(255, 0);
        animator.setDuration(300); // 300ms淡出动画
        animator.setStartDelay(200); // 200ms延迟开始淡出
        animator.setInterpolator(new LinearInterpolator());

        animator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator animation) {
                currentAlpha = (int) animation.getAnimatedValue();
                paint.setAlpha(currentAlpha);
                invalidate();
            }
        });

        animator.start();

        invalidate();

        // 500ms后移除自身
        postDelayed(new Runnable() {
            @Override
            public void run() {
                if (getParent() instanceof ViewGroup) {
                    ((ViewGroup) getParent()).removeView(DebugTouchIndicator.this);
                }
            }
        }, 500);
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        canvas.drawCircle(centerX, centerY, radius, paint);
    }
}
