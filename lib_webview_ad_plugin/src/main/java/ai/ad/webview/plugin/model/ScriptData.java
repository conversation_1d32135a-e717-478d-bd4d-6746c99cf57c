package ai.ad.webview.plugin.model;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Random;

public class ScriptData {
    private final Metadata metadata;
    private final List<Event> events;

    public ScriptData() {
        this(new Metadata(), Collections.emptyList());
    }

    public ScriptData(Metadata metadata, List<Event> events) {
        this.metadata = metadata;
        this.events = events;
    }

    public Metadata getMetadata() {
        return metadata;
    }

    public List<Event> getEvents() {
        return events;
    }

    public static class Metadata {
        private String url;
        private final int deviceWidth;
        private final int deviceHeight;
        private String id;
        private final String version;
        private final int timeout;

        public Metadata() {
            this("", 0, 0, "", "1.0", 300);
        }

        public Metadata(String url, int deviceWidth, int deviceHeight, String id, String version, int timeout) {
            this.url = url;
            this.deviceWidth = deviceWidth;
            this.deviceHeight = deviceHeight;
            this.id = id;
            this.version = version;
            this.timeout = timeout;
        }

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }

        public int getDeviceWidth() {
            return deviceWidth;
        }

        public int getDeviceHeight() {
            return deviceHeight;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getVersion() {
            return version;
        }

        public int getTimeout() {
            return timeout;
        }
    }

    public static abstract class Event {
        public abstract String getAction();

        public abstract long getTime();

        public abstract int getX();

        public abstract int getY();

        public static class Click extends Event {
            private final long time;
            private final int x;
            private final int y;

            public Click(long time, int x, int y) {
                this.time = time;
                this.x = x;
                this.y = y;
            }

            @Override
            public String getAction() {
                return "c";
            }

            @Override
            public long getTime() {
                return time;
            }

            @Override
            public int getX() {
                return x;
            }

            @Override
            public int getY() {
                return y;
            }
        }

        public static class Touch extends Event {
            private final String action;
            private final long time;
            private final int x;
            private final int y;

            public Touch(String action, long time, int x, int y) {
                this.action = action;
                this.time = time;
                this.x = x;
                this.y = y;
            }

            @Override
            public String getAction() {
                return action;
            }

            @Override
            public long getTime() {
                return time;
            }

            @Override
            public int getX() {
                return x;
            }

            @Override
            public int getY() {
                return y;
            }
        }
    }

    public JSONObject toJSONObject() throws JSONException {
        JSONObject jsonObject = new JSONObject();

        JSONObject metadataObj = new JSONObject();
        metadataObj.put("url", metadata.getUrl());
        metadataObj.put("w", metadata.getDeviceWidth());
        metadataObj.put("h", metadata.getDeviceHeight());
        metadataObj.put("id", metadata.getId());
        metadataObj.put("version", metadata.getVersion());
        metadataObj.put("timeout", metadata.getTimeout());
        jsonObject.put("m", metadataObj);

        JSONArray eventsArray = new JSONArray();
        for (Event event : events) {
            JSONObject eventObj = new JSONObject();
            eventObj.put("a", event.getAction());
            eventObj.put("t", event.getTime());
            eventObj.put("x", event.getX());
            eventObj.put("y", event.getY());
            eventsArray.put(eventObj);
        }
        jsonObject.put("e", eventsArray);

        return jsonObject;
    }

    public static int calculateOffsetCoordinate(int original, ScriptPlan.JitterConfig offset) {
        // 计算百分比偏移范围
        double percentOffset = original * (offset.getPercent() / 100.0);
        // 取百分比偏移和最小像素中的较大值
        double offsetRange = Math.max(percentOffset, offset.getMinPx());
        // 在正负范围内随机偏移
        Random random = new Random();
        return original + random.nextInt((int) offsetRange * 2 + 1) - (int) offsetRange;
    }

    public static long calculateOffsetTime(long original, ScriptPlan.JitterConfig offset) {
        // 计算百分比偏移范围
        double percentOffset = original * (offset.getPercent() / 100.0);
        // 取百分比偏移和最小毫秒中的较大值
        double offsetRange = Math.max(percentOffset, offset.getMinMs());
        // 在正负范围内随机偏移
        Random random = new Random();
        return original + (long) (random.nextDouble() * offsetRange * 2 - offsetRange);
    }

    public static ScriptData fromJSONObject(JSONObject jsonObject) throws JSONException {
        JSONObject metadataObj = jsonObject.optJSONObject("m");
        if (metadataObj == null) {
            metadataObj = new JSONObject();
        }

        Metadata metadata = new Metadata(
                metadataObj.optString("url", ""),
                metadataObj.optInt("w", 0),
                metadataObj.optInt("h", 0),
                metadataObj.optString("id", ""),
                metadataObj.optString("version", "1.0"),
                metadataObj.optInt("timeout", 300)
        );

        JSONArray eventsArray = jsonObject.optJSONArray("e");
        if (eventsArray == null) {
            eventsArray = new JSONArray();
        }

        List<Event> events = new ArrayList<>();
        for (int i = 0; i < eventsArray.length(); i++) {
            JSONObject eventObj = eventsArray.getJSONObject(i);
            String action = eventObj.optString("a", "");
            long time = eventObj.optLong("t", 0L);
            int x = eventObj.optInt("x", 0);
            int y = eventObj.optInt("y", 0);

            Event event = null;
            if ("c".equals(action)) {
                event = new Event.Click(time, x, y);
            } else if ("d".equals(action) || "m".equals(action) || "u".equals(action)) {
                event = new Event.Touch(action, time, x, y);
            }

            if (event != null) {
                events.add(event);
            }
        }

        return new ScriptData(metadata, events);
    }
}
