package ai.ad.webview.plugin.utils;

import android.content.Context;
import android.content.SharedPreferences;
import android.webkit.WebSettings;

import ai.ad.webview.sdk.logger.WSKLog;

public class UAManager {
    private static final String TAG = "UAManager";
    private static String cachedUA;
    private static String systemUA;

    // SharedPreferences 常量
    private static final String PREFS_NAME = "wsk_ua_prefs";
    private static final String KEY_USER_AGENT = "user_agent";

    private UAManager() {
        // 私有构造函数，防止实例化
    }

    public static void saveUA(Context context, String ua) {
        if (ua == null || ua.trim().isEmpty()) {
            return;
        }

        // 保存到内存缓存
        cachedUA = ua;

        // 保存到 SharedPreferences
        try {
            SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
            SharedPreferences.Editor editor = prefs.edit();
            editor.putString(KEY_USER_AGENT, ua);
            editor.apply();
            WSKLog.i(TAG, "UA saved to storage and memory,ua=" + ua);
        } catch (Exception e) {
            WSKLog.e(TAG, "Failed to save UA to storage,error=" + e.getMessage());
        }
    }

    public static String getUA(Context context) {
        // 1. 如果内存缓存中有 UA，直接返回
        if (cachedUA != null && !cachedUA.trim().isEmpty()) {
            WSKLog.i(TAG, "Using UA from memory cache,ua=" + cachedUA);
            return cachedUA;
        }

        // 2. 如果内存缓存中没有，尝试从 SharedPreferences 获取
        try {
            SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
            String savedUA = prefs.getString(KEY_USER_AGENT, null);
            if (savedUA != null && !savedUA.trim().isEmpty()) {
                cachedUA = savedUA; // 同时更新内存缓存
                WSKLog.i(TAG, "Using UA from storage,ua=" + savedUA);
                return savedUA;
            }
        } catch (Exception e) {
            WSKLog.e(TAG, "Failed to get UA from storage,error=" + e.getMessage());
        }

        // 3. 如果存储中也没有，使用系统默认 UA
        if (systemUA == null) {
            systemUA = WebSettings.getDefaultUserAgent(context);
        }
        WSKLog.i(TAG, "Using system default UA,ua=" + systemUA);
        return systemUA;
    }
}
