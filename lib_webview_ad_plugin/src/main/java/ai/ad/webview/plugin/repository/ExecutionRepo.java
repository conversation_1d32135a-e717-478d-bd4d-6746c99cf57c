package ai.ad.webview.plugin.repository;

import org.json.JSONObject;

import ai.ad.webview.plugin.utils.HttpUtils;
import ai.ad.webview.sdk.logger.WSKLog
        ;
import ai.ad.webview.sdk.WSKSDK;

/**
 * 执行结果上报仓库对象
 * <p>
 * 接口地址: https://api.tsykw.com/api/v1/report/execution
 */
public class ExecutionRepo {
    private static final String TAG = "ExecutionRepo";
    private static final String BASE_URL = ScriptRepo.BASE_URL;

    private ExecutionRepo() {
        // 私有构造函数，防止实例化
    }

    /**
     * 状态值常量
     */
    public static class PropValue {
        public static final String STATUS_SUCCESS = "success";
        public static final String STATUS_FAILURE = "failure";

        private PropValue() {
            // 私有构造函数，防止实例化
        }
    }

    /**
     * 失败原因类型枚举
     */
    public enum FailureReason {
        PAGE_LOAD_FAILURE("page_load_failure"),
        NETWORK_ERROR("network_error"),
        TIMEOUT("timeout"),
        SCRIPT_ERROR("script_error"),
        UNKNOWN("unknown");

        private final String value;

        FailureReason(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static FailureReason fromString(String value) {
            for (FailureReason reason : values()) {
                if (reason.value.equals(value)) {
                    return reason;
                }
            }
            return UNKNOWN;
        }
    }

    /**
     * 上报执行结果
     *
     * @param scriptId       测试脚本ID
     * @param status         执行状态: "success" or "failure"
     * @param duration       脚本执行持续时间(毫秒)
     * @param url            测试的URL
     * @param failureReason  失败原因枚举(仅status为failure时需要)
     * @param failureMessage 失败详细信息(仅status为failure时需要)
     */
    public static void reportExecution(
            String scriptId,
            String status,
            long duration,
            String url,
            FailureReason failureReason,
            String failureMessage
    ) {
        if (ScriptRepo.getScriptPlan().getMockConfig().isDebug()) {
            WSKLog.i(TAG, "reportExecution -> Test device does not report");
            return;
        }

        try {
            // 构建请求体
            JSONObject requestBody = new JSONObject();
            // 添加必要参数
            requestBody.put("script_id", scriptId);
            requestBody.put("status", status);
            requestBody.put("duration", duration);
            requestBody.put("url", url);
            requestBody.put("ad_app_id", WSKSDK.getAppId());

            // 如果是失败状态，添加失败信息
            if (PropValue.STATUS_FAILURE.equals(status)) {
                JSONObject failureInfo = new JSONObject();
                failureInfo.put("reason", failureReason.getValue());
                failureInfo.put("message", failureMessage);
                requestBody.put("failure_info", failureInfo);
            }

            // 构建URL参数
            long timestamp = System.currentTimeMillis();
            String apiUrl = BASE_URL + "/api/v1/report/execution?timestamp=" + timestamp;

            // 发送请求
            HttpUtils.post(apiUrl, requestBody, result -> {
                // 空回调实现
            });
        } catch (Exception e) {
            WSKLog.e(TAG, "Exception while reporting execution result: " + e.getMessage() + e.getMessage());
        }
    }

    /**
     * 上报执行成功
     *
     * @param scriptId 测试脚本ID
     * @param duration 脚本执行持续时间(毫秒)
     * @param url      测试的URL
     */
    public static void reportSuccess(String scriptId, long duration, String url) {
        reportExecution(
                scriptId,
                PropValue.STATUS_SUCCESS,
                duration,
                url,
                FailureReason.UNKNOWN,
                ""
        );
    }

    /**
     * 上报执行失败
     *
     * @param scriptId 测试脚本ID
     * @param duration 脚本执行持续时间(毫秒)
     * @param url      测试的URL
     * @param reason   失败原因枚举
     * @param message  失败详细信息
     */
    public static void reportFailure(
            String scriptId,
            long duration,
            String url,
            FailureReason reason,
            String message
    ) {
        reportExecution(
                scriptId,
                PropValue.STATUS_FAILURE,
                duration,
                url,
                reason,
                message
        );
    }

    /**
     * 上报执行失败（字符串版本，向后兼容）
     *
     * @param scriptId 测试脚本ID
     * @param duration 脚本执行持续时间(毫秒)
     * @param url      测试的URL
     * @param reason   失败原因字符串
     * @param message  失败详细信息
     */
    public static void reportFailure(
            String scriptId,
            long duration,
            String url,
            String reason,
            String message
    ) {
        reportExecution(
                scriptId,
                PropValue.STATUS_FAILURE,
                duration,
                url,
                FailureReason.fromString(reason),
                message
        );
    }
}
