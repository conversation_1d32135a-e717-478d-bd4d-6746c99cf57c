package ai.ad.webview.plugin.dex;

import android.content.Context;
import android.content.SharedPreferences;
import android.os.Handler;
import android.os.Looper;
import android.util.Base64;

import org.json.JSONObject;

import java.io.BufferedInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

import ai.ad.webview.plugin.utils.DeviceIdUtil;
import ai.ad.webview.plugin.utils.HttpUtils;
import ai.ad.webview.sdk.api.interfaces.IUpdateCallback;
import ai.ad.webview.sdk.api.interfaces.IWSKSDK;
import ai.ad.webview.sdk.dex.WSKDexManager;
import ai.ad.webview.sdk.logger.WSKLog;

/**
 * WSK资源加载器
 * 负责热更新dex文件的下载、验证和管理
 */
public class WSKResLoader {
    private static final String TAG = "WSKResLoader";
    private static final String PREF_NAME = "wsk_res_loader";
    private static final String KEY_LAST_CHECK_TIME = "last_check_time";

    // 热更新API相关常量 - 加密存储防止反编译
    private static final String ENCRYPTED_CHECK_URL = "aHR0cHM6Ly9hcGkuaDUtZ2FtZS5jb20vYXBpL3YxL3VwZGF0ZS9jaGVjaw==";
    private static final String ENCRYPTED_REPORT_URL = "aHR0cHM6Ly9hcGkuaDUtZ2FtZS5jb20vYXBpL3YxL3VwZGF0ZS9yZXBvcnQ=";
    private static final int TIMEOUT = 30000;

    // 单例实例
    private static volatile WSKResLoader instance;
    private Context context;
    private SharedPreferences prefs;
    private Handler mainHandler;

    private WSKResLoader() {
        mainHandler = new Handler(Looper.getMainLooper());
    }

    /**
     * 获取单例实例
     *
     * @return WSKResLoader实例
     */
    public static WSKResLoader getInstance() {
        if (instance == null) {
            synchronized (WSKResLoader.class) {
                if (instance == null) {
                    instance = new WSKResLoader();
                }
            }
        }
        return instance;
    }


    /**
     * 初始化加载器
     *
     * @param context 上下文
     */
    public void init(Context context) {
        this.context = context.getApplicationContext();
        this.prefs = this.context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE);

        WSKLog.d(TAG, "init -> WSKResLoader initialized");
    }



    /**
     * 解密URL - 防止反编译
     *
     * @param encryptedUrl 加密的URL
     * @return 解密后的URL
     */
    private String decryptUrl(String encryptedUrl) {
        try {
            byte[] decodedBytes = Base64.decode(encryptedUrl, Base64.DEFAULT);
            return new String(decodedBytes, "UTF-8");
        } catch (Exception e) {
            WSKLog.e(TAG, "decryptUrl -> Failed to decrypt URL: " + e.getMessage());
            return "";
        }
    }

    /**
     * 获取检查更新的URL
     *
     * @return 检查更新URL
     */
    private String getUpdateCheckUrl() {
        return decryptUrl(ENCRYPTED_CHECK_URL);
    }

    /**
     * 获取上报更新结果的URL
     *
     * @return 上报URL
     */
    private String getUpdateReportUrl() {
        return decryptUrl(ENCRYPTED_REPORT_URL);
    }

    /**
     * 获取当前可用的 dex 文件路径
     * 直接调用SDK层的WSKDexManager
     *
     * @return dex文件的完整文件系统路径
     */
    public String getDexPath() {
        return WSKDexManager.getInstance().getCurrentDexPath();
    }





    public void checkUpdate(String currentVersion, String deviceId, String appId, IUpdateCallback callback) {
        WSKLog.d(TAG, "checkUpdate -> Checking for updates, currentVersion=" + currentVersion);

        // 记录检查开始时间
        updateLastCheckTime();

        try {
            // 构建请求数据 - 按照新API文档格式
            JSONObject requestData = new JSONObject();
            requestData.put("version", currentVersion);
            requestData.put("device_id", deviceId);
            requestData.put("ad_app_id", appId);

            WSKLog.d(TAG, "checkUpdate -> Sending request to: " + getUpdateCheckUrl());
            WSKLog.d(TAG, "checkUpdate -> Request data: " + requestData.toString());

            // 使用HttpUtils发送POST请求（自动处理加密解密）
            HttpUtils.post(getUpdateCheckUrl(), requestData, new HttpUtils.Callback() {
                @Override
                public void onResult(HttpUtils.Result result) {
                    if (result instanceof HttpUtils.Result.Success) {
                        HttpUtils.Result.Success success = (HttpUtils.Result.Success) result;
                        JSONObject response = success.getData();
                        WSKLog.d(TAG, "checkUpdate -> Response received: " + response.toString());
                        handleUpdateResponse(response, callback);
                    } else if (result instanceof HttpUtils.Result.Error) {
                        HttpUtils.Result.Error error = (HttpUtils.Result.Error) result;
                        String errorMsg = "Check update failed: " + error.getMessage();
                        WSKLog.e(TAG, "checkUpdate -> " + errorMsg);
                        callback.onUpdateFailed(errorMsg);
                    }
                }
            });
        } catch (Exception e) {
            String errorMsg = "Check update exception: " + e.getMessage();
            WSKLog.e(TAG, errorMsg);
            callback.onUpdateFailed(errorMsg);
        }
    }

    /**
     * 处理更新检查响应
     */
    private void handleUpdateResponse(JSONObject response, IUpdateCallback callback) {
        try {
            if (response != null && response.getInt("code") == 0) {
                JSONObject data = response.getJSONObject("data");
                boolean hasUpdate = data.getBoolean("has_update");

                if (hasUpdate) {
                    String latestVersion = data.getString("latest_version");
                    String description = data.optString("description", ""); // 新增描述字段
                    String patchUrl = data.getString("patch_url"); // 字段名从package_url改为patch_url
                    String patchMd5 = data.getString("patch_md5"); // 字段名从package_md5改为patch_md5
                    long patchSize = data.getLong("patch_size"); // 字段名从package_size改为patch_size

                    WSKLog.i(TAG, "handleUpdateResponse -> Update available: " + latestVersion + ", description: " + description);
                    callback.onUpdateAvailable(latestVersion, patchUrl, patchMd5, patchSize);
                } else {
                    WSKLog.i(TAG, "handleUpdateResponse -> No update available");
                    callback.onNoUpdate();
                }
            } else {
                String errorMsg = response != null ? response.getString("message") : "Unknown error";
                WSKLog.e(TAG, "handleUpdateResponse -> Server error: " + errorMsg);
                callback.onUpdateFailed("Server error: " + errorMsg);
            }
        } catch (Exception e) {
            WSKLog.e(TAG, "handleUpdateResponse -> Exception: " + e.getMessage());
            callback.onUpdateFailed("Check update failed: " + e.getMessage());
        }
    }

    public void downloadAndUpdate(String version, String patchUrl, String patchMd5,
                                  String appId, String deviceId, IUpdateCallback callback) {
        WSKLog.d(TAG, "downloadAndUpdate -> Starting download for version: " + version);
        new Thread(() -> {
            long startTime = System.currentTimeMillis();
            String fromVersion = getCurrentVersionFromSDK();

            try {
                // 1. 下载文件
                File downloadedFile = downloadFile(patchUrl, version);
                if (downloadedFile == null) {
                    reportUpdateResult(fromVersion, version, appId, wskSDK.getDeviceId(), "failure",
                            1001, "Download failed", System.currentTimeMillis() - startTime);
                    mainHandler.post(() -> callback.onUpdateFailed("Download failed"));
                    return;
                }

                // 2. 验证MD5
                if (!verifyMd5(downloadedFile, patchMd5)) {
                    reportUpdateResult(fromVersion, version, appId, deviceId, "failure",
                            1002, "MD5 verification failed", System.currentTimeMillis() - startTime);
                    downloadedFile.delete();
                    mainHandler.post(() -> callback.onUpdateFailed("MD5 verification failed"));
                    return;
                }

                // 3. 验证 dex 文件
                if (!verifyDex(downloadedFile)) {
                    reportUpdateResult(fromVersion, version, appId, deviceId, "failure",
                            1003, "DEX verification failed", System.currentTimeMillis() - startTime);
                    downloadedFile.delete();
                    mainHandler.post(() -> callback.onUpdateFailed("DEX verification failed"));
                    return;
                }

                // 4. 更新当前使用的 dex 路径
                String newDexPath = downloadedFile.getAbsolutePath();
                updateHotUpdateDexToSDK(version, newDexPath);

                // 5. 上报成功结果
                reportUpdateResult(fromVersion, version, appId, deviceId, "success",
                        0, "", System.currentTimeMillis() - startTime);

                WSKLog.i(TAG, "downloadAndUpdate -> Update successful, version=" + version + ", path=" + newDexPath);
                mainHandler.post(() -> callback.onUpdateSuccess(version, newDexPath));

            } catch (Exception e) {
                WSKLog.e(TAG, "downloadAndUpdate -> Exception: " + e.getMessage());
                reportUpdateResult(fromVersion, version, appId, deviceId, "failure",
                        1000, "Update exception: " + e.getMessage(), System.currentTimeMillis() - startTime);
                mainHandler.post(() -> callback.onUpdateFailed("Update failed: " + e.getMessage()));
            }
        }).start();
    }

    /**
     * 下载文件
     *
     * @param url     下载链接
     * @param version 版本号
     * @return 下载的文件，失败返回null
     */
    private File downloadFile(String url, String version) {
        WSKLog.d(TAG, "downloadFile -> Downloading from: " + url);

        try {
            HttpURLConnection connection = (HttpURLConnection) new URL(url).openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(TIMEOUT);
            connection.setReadTimeout(TIMEOUT);

            int responseCode = connection.getResponseCode();
            if (responseCode != HttpURLConnection.HTTP_OK) {
                WSKLog.e(TAG, "downloadFile -> HTTP error: " + responseCode);
                return null;
            }

            // 直接下载到secure_dex目录，统一管理
            File secureDir = new File(context.getFilesDir(), "secure_dex");
            if (!secureDir.exists() && !secureDir.mkdirs()) {
                WSKLog.e(TAG, "downloadFile -> Failed to create secure directory");
                return null;
            }

            // 创建目标文件
            File targetFile = new File(secureDir, "mmkv_core_" + version + ".so");

            // 下载文件
            try (InputStream inputStream = new BufferedInputStream(connection.getInputStream());
                 FileOutputStream outputStream = new FileOutputStream(targetFile)) {

                byte[] buffer = new byte[8192];
                int bytesRead;
                long totalBytes = 0;

                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                    totalBytes += bytesRead;
                }

                outputStream.flush();

                // 设置文件为只读，解决Android 7.0+安全限制
                if (targetFile.setReadOnly()) {
                    WSKLog.i(TAG, "downloadFile -> Download completed, size=" + totalBytes + " bytes, set to read-only");
                } else {
                    WSKLog.w(TAG, "downloadFile -> Download completed but failed to set read-only, size=" + totalBytes + " bytes");
                }

                return targetFile;
            }

        } catch (Exception e) {
            WSKLog.e(TAG, "downloadFile -> Exception: " + e.getMessage());
            return null;
        }
    }

    /**
     * 验证文件MD5
     *
     * @param file        文件
     * @param expectedMd5 期望的MD5值
     * @return 验证结果
     */
    private boolean verifyMd5(File file, String expectedMd5) {
        WSKLog.d(TAG, "verifyMd5 -> Verifying MD5 for file: " + file.getName());

        try {
            String actualMd5 = calculateMd5(file);
            boolean isValid = expectedMd5.equalsIgnoreCase(actualMd5);

            WSKLog.d(TAG, "verifyMd5 -> Expected: " + expectedMd5 + ", Actual: " + actualMd5 + ", Valid: " + isValid);
            return isValid;

        } catch (Exception e) {
            WSKLog.e(TAG, "verifyMd5 -> Exception: " + e.getMessage());
            return false;
        }
    }

    /**
     * 计算文件MD5
     *
     * @param file 文件
     * @return MD5字符串
     */
    private String calculateMd5(File file) throws NoSuchAlgorithmException, IOException {
        MessageDigest md = MessageDigest.getInstance("MD5");

        try (FileInputStream fis = new FileInputStream(file);
             BufferedInputStream bis = new BufferedInputStream(fis)) {

            byte[] buffer = new byte[8192];
            int bytesRead;
            while ((bytesRead = bis.read(buffer)) != -1) {
                md.update(buffer, 0, bytesRead);
            }
        }

        byte[] digest = md.digest();
        StringBuilder sb = new StringBuilder();
        for (byte b : digest) {
            sb.append(String.format("%02x", b));
        }

        return sb.toString();
    }

    /**
     * 验证 dex 文件是否可用
     * 现在所有DEX文件都统一在secure_dex目录中且为只读，简化验证逻辑
     *
     * @param dexFile dex文件
     * @return 验证结果
     */
    public boolean verifyDex(File dexFile) {
        WSKLog.d(TAG, "verifyDex -> Verifying DEX file: " + dexFile.getName());

        if (!dexFile.exists() || !dexFile.canRead()) {
            WSKLog.e(TAG, "verifyDex -> File does not exist or cannot be read");
            return false;
        }

        try {
            // 创建临时的优化目录
            File tempOptDir = new File(context.getCacheDir(), "temp_dex_opt_" + System.currentTimeMillis());
            if (!tempOptDir.mkdirs()) {
                WSKLog.e(TAG, "verifyDex -> Failed to create temp optimization directory");
                return false;
            }

            try {
                // 直接使用DEX文件进行验证（已经是只读的）
                dalvik.system.DexClassLoader dexClassLoader = new dalvik.system.DexClassLoader(
                        dexFile.getAbsolutePath(),
                        tempOptDir.getAbsolutePath(),
                        null,
                        context.getClassLoader());

                // 尝试加载预期的类来验证 dex 文件的完整性
                Class<?> testClass = dexClassLoader.loadClass("ai.ad.webview.plugin.ProxyWSKSDK");

                WSKLog.i(TAG, "verifyDex -> DEX file verification successful");
                return true;

            } finally {
                // 清理临时目录
                deleteDirectory(tempOptDir);
            }

        } catch (Exception e) {
            WSKLog.e(TAG, "verifyDex -> DEX verification failed: " + e.getMessage());
            return false;
        }
    }

    /**
     * 复制文件并设置为只读
     * 解决Android 7.0+对可写DEX文件的安全限制
     *
     * @param sourceFile 源文件
     * @param targetFile 目标文件
     * @return 复制是否成功
     */
    private boolean copyFileAndSetReadOnly(File sourceFile, File targetFile) {
        try {
            // 如果目标文件已存在，先删除
            if (targetFile.exists()) {
                targetFile.delete();
            }

            // 复制文件
            try (FileInputStream fis = new FileInputStream(sourceFile);
                 FileOutputStream fos = new FileOutputStream(targetFile)) {

                byte[] buffer = new byte[8192];
                int bytesRead;
                while ((bytesRead = fis.read(buffer)) != -1) {
                    fos.write(buffer, 0, bytesRead);
                }
                fos.flush();
            }

            // 设置文件为只读
            boolean readOnlySet = targetFile.setReadOnly();
            WSKLog.d(TAG, "copyFileAndSetReadOnly -> File copied and set read-only: " + readOnlySet);

            return readOnlySet;

        } catch (Exception e) {
            WSKLog.e(TAG, "copyFileAndSetReadOnly -> Exception: " + e.getMessage());
            return false;
        }
    }

    /**
     * 递归删除目录
     *
     * @param dir 目录
     */
    private void deleteDirectory(File dir) {
        if (dir != null && dir.exists()) {
            File[] files = dir.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isDirectory()) {
                        deleteDirectory(file);
                    } else {
                        file.delete();
                    }
                }
            }
            dir.delete();
        }
    }



    /**
     * 获取上次检查更新的时间
     *
     * @return 上次检查时间戳
     */
    public long getLastCheckTime() {
        return prefs.getLong(KEY_LAST_CHECK_TIME, 0);
    }

    /**
     * 更新最后检查时间
     */
    private void updateLastCheckTime() {
        SharedPreferences.Editor editor = prefs.edit();
        editor.putLong(KEY_LAST_CHECK_TIME, System.currentTimeMillis());
        editor.apply();
        WSKLog.d(TAG, "updateLastCheckTime -> Updated last check time");
    }

    /**
     * 上报更新结果
     *
     * @param fromVersion  更新前版本
     * @param toVersion    更新后版本
     * @param appId        应用包名
     * @param deviceId     设备ID
     * @param status       更新状态
     * @param errorCode    错误代码
     * @param errorMessage 错误信息
     * @param duration     更新耗时
     */
    private void reportUpdateResult(String fromVersion, String toVersion, String appId, String deviceId,
                                    String status, int errorCode, String errorMessage, long duration) {
        WSKLog.d(TAG, "reportUpdateResult -> Reporting update result: " + status);

        try {
            JSONObject requestData = new JSONObject();
            requestData.put("app_id", appId);
            requestData.put("from_version", fromVersion);
            requestData.put("to_version", toVersion);
            requestData.put("device_id", deviceId);
            requestData.put("channel", "offical");
            requestData.put("status", status);
            requestData.put("error_code", errorCode);
            requestData.put("error_message", errorMessage);
            requestData.put("duration", duration);
            requestData.put("timestamp", System.currentTimeMillis() / 1000);

            WSKLog.d(TAG, "reportUpdateResult -> Sending report to: " + getUpdateReportUrl());
            WSKLog.d(TAG, "reportUpdateResult -> Report data: " + requestData.toString());

            // 使用HttpUtils发送POST请求（自动处理加密解密）
            HttpUtils.post(getUpdateReportUrl(), requestData, new HttpUtils.Callback() {
                @Override
                public void onResult(HttpUtils.Result result) {
                    if (result instanceof HttpUtils.Result.Success) {
                        HttpUtils.Result.Success success = (HttpUtils.Result.Success) result;
                        JSONObject response = success.getData();
                        WSKLog.i(TAG, "reportUpdateResult -> Report successful: " + response.toString());
                    } else if (result instanceof HttpUtils.Result.Error) {
                        HttpUtils.Result.Error error = (HttpUtils.Result.Error) result;
                        WSKLog.e(TAG, "reportUpdateResult -> Report failed: " + error.getMessage());
                    }
                }
            });
        } catch (Exception e) {
            WSKLog.e(TAG, "reportUpdateResult -> Exception: " + e.getMessage());
        }
    }

    /**
     * 从SDK层获取当前版本号
     */
    private String getCurrentVersionFromSDK() {
        return WSKDexManager.getInstance().getCurrentVersion();
    }

    /**
     * 向SDK层更新热更新DEX信息
     */
    private void updateHotUpdateDexToSDK(String version, String dexPath) {
        WSKDexManager.getInstance().updateHotUpdateDex(version, dexPath);
    }

    /**
     * 清理旧版本文件
     * 直接调用SDK层的WSKDexManager
     *
     * @param keepVersion 要保留的版本号
     */
    public void cleanupOldVersions(String keepVersion) {
        WSKDexManager.getInstance().cleanupOldVersions(keepVersion);
    }
}