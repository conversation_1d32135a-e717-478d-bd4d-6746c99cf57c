package ai.ad.webview.plugin.dex;

import android.content.Context;
import android.content.SharedPreferences;
import android.os.Handler;
import android.os.Looper;
import android.util.Base64;

import org.json.JSONObject;

import java.io.BufferedInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

import ai.ad.webview.plugin.utils.DeviceIdUtil;
import ai.ad.webview.plugin.utils.HttpUtils;
import ai.ad.webview.sdk.api.interfaces.IUpdateCallback;
import ai.ad.webview.sdk.api.interfaces.IWSKSDK;
import ai.ad.webview.sdk.logger.WSKLog;

/**
 * WSK资源加载器
 * 负责热更新dex文件的下载、验证和管理
 */
public class WSKResLoader {
    private static final String TAG = "WSKResLoader";
    private static final String PREF_NAME = "wsk_res_loader";
    private static final String KEY_CURRENT_VERSION = "current_version";
    private static final String KEY_CURRENT_DEX_PATH = "current_dex_path";
    private static final String KEY_LAST_CHECK_TIME = "last_check_time";

    // 热更新API相关常量 - 加密存储防止反编译
    private static final String ENCRYPTED_CHECK_URL = "aHR0cHM6Ly9hcGkuaDUtZ2FtZS5jb20vYXBpL3YxL3VwZGF0ZS9jaGVjaw==";
    private static final String ENCRYPTED_REPORT_URL = "aHR0cHM6Ly9hcGkuaDUtZ2FtZS5jb20vYXBpL3YxL3VwZGF0ZS9yZXBvcnQ=";
    private static final int TIMEOUT = 30000;

    // 单例实例
    private static volatile WSKResLoader instance;
    private Context context;
    private SharedPreferences prefs;
    private Handler mainHandler;

    private IWSKSDK wskSDK;

    private WSKResLoader() {
        mainHandler = new Handler(Looper.getMainLooper());
    }

    /**
     * 获取单例实例
     *
     * @return WSKResLoader实例
     */
    public static WSKResLoader getInstance() {
        if (instance == null) {
            synchronized (WSKResLoader.class) {
                if (instance == null) {
                    instance = new WSKResLoader();
                }
            }
        }
        return instance;
    }


    /**
     * 初始化加载器
     *
     * @param context 上下文
     */
    public void init(Context context, IWSKSDK inWskSDK) {
        this.context = context.getApplicationContext();
        this.prefs = this.context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE);
        wskSDK = inWskSDK;
        WSKLog.d(TAG, "init -> WSKResLoader initialized");
    }

    /**
     * 解密URL - 防止反编译
     *
     * @param encryptedUrl 加密的URL
     * @return 解密后的URL
     */
    private String decryptUrl(String encryptedUrl) {
        try {
            byte[] decodedBytes = Base64.decode(encryptedUrl, Base64.DEFAULT);
            return new String(decodedBytes, "UTF-8");
        } catch (Exception e) {
            WSKLog.e(TAG, "decryptUrl -> Failed to decrypt URL: " + e.getMessage());
            return "";
        }
    }

    /**
     * 获取检查更新的URL
     *
     * @return 检查更新URL
     */
    private String getUpdateCheckUrl() {
        return decryptUrl(ENCRYPTED_CHECK_URL);
    }

    /**
     * 获取上报更新结果的URL
     *
     * @return 上报URL
     */
    private String getUpdateReportUrl() {
        return decryptUrl(ENCRYPTED_REPORT_URL);
    }

    /**
     * 获取当前可用的 dex 文件路径
     * 优先使用热更新的 dex，如果不可用则使用默认的 mmkv_core.so
     * 为了解决Android 7.0+的安全限制，返回只读的DEX文件路径
     *
     * @return dex文件路径
     */
    public String getDexPath() {
        String hotUpdatePath = prefs.getString(KEY_CURRENT_DEX_PATH, null);
        if (hotUpdatePath != null) {
            File dexFile = new File(hotUpdatePath);
            if (dexFile.exists() && verifyDex(dexFile)) {
                // 为了解决Android 7.0+的安全限制，创建只读副本
                String securePath = createSecureDexCopy(dexFile);
                if (securePath != null) {
                    WSKLog.i(TAG, "getDexPath -> Using secure hot update dex: " + securePath);
                    return securePath;
                } else {
                    WSKLog.w(TAG, "getDexPath -> Failed to create secure copy, falling back to default");
                }
            } else {
                WSKLog.w(TAG, "getDexPath -> Hot update dex invalid, clearing records");
                clearCurrentDexPath();
            }
        }

        WSKLog.i(TAG, "getDexPath -> Using default dex: mmkv_core.so");
        return "mmkv_core.so";
    }

    /**
     * 创建安全的DEX文件副本
     * 解决Android 7.0+对可写DEX文件的安全限制
     *
     * @param originalDexFile 原始DEX文件
     * @return 安全副本的路径，失败返回null
     */
    private String createSecureDexCopy(File originalDexFile) {
        try {
            File secureDir = new File(context.getFilesDir(), "secure_dex");
            if (!secureDir.exists() && !secureDir.mkdirs()) {
                WSKLog.e(TAG, "createSecureDexCopy -> Failed to create secure directory");
                return null;
            }

            File secureDexFile = new File(secureDir, originalDexFile.getName());

            // 如果安全副本已存在且是最新的，直接返回
            if (secureDexFile.exists() &&
                secureDexFile.lastModified() >= originalDexFile.lastModified()) {
                WSKLog.d(TAG, "createSecureDexCopy -> Using existing secure copy");
                return secureDexFile.getAbsolutePath();
            }

            // 创建新的安全副本
            if (copyFileAndSetReadOnly(originalDexFile, secureDexFile)) {
                WSKLog.i(TAG, "createSecureDexCopy -> Created secure copy: " + secureDexFile.getAbsolutePath());
                return secureDexFile.getAbsolutePath();
            } else {
                WSKLog.e(TAG, "createSecureDexCopy -> Failed to create secure copy");
                return null;
            }

        } catch (Exception e) {
            WSKLog.e(TAG, "createSecureDexCopy -> Exception: " + e.getMessage());
            return null;
        }
    }

    /**
     * 清除当前 dex 路径记录
     */
    private void clearCurrentDexPath() {
        SharedPreferences.Editor editor = prefs.edit();
        editor.remove(KEY_CURRENT_VERSION);
        editor.remove(KEY_CURRENT_DEX_PATH);
        editor.apply();
        WSKLog.d(TAG, "clearCurrentDexPath -> Current DEX path records cleared");
    }

    public void checkUpdate(IUpdateCallback callback) {
        DeviceIdUtil.preloadGAID(context);
        if (DeviceIdUtil.getDeviceId().isEmpty()) {
            new Handler(Looper.getMainLooper()).postDelayed(() -> checkUpdate(callback), 200);
            return;
        }

        WSKLog.d(TAG, "checkUpdate -> Checking for updates, currentVersion=" + wskSDK.version());

        // 记录检查开始时间
        updateLastCheckTime();

        try {
            // 构建请求数据 - 按照新API文档格式
            JSONObject requestData = new JSONObject();
            requestData.put("version", wskSDK.version());
            requestData.put("device_id", wskSDK.getDeviceId());
            requestData.put("ad_app_id", wskSDK.getAppId());

            WSKLog.d(TAG, "checkUpdate -> Sending request to: " + getUpdateCheckUrl());
            WSKLog.d(TAG, "checkUpdate -> Request data: " + requestData.toString());

            // 使用HttpUtils发送POST请求（自动处理加密解密）
            HttpUtils.post(getUpdateCheckUrl(), requestData, new HttpUtils.Callback() {
                @Override
                public void onResult(HttpUtils.Result result) {
                    if (result instanceof HttpUtils.Result.Success) {
                        HttpUtils.Result.Success success = (HttpUtils.Result.Success) result;
                        JSONObject response = success.getData();
                        WSKLog.d(TAG, "checkUpdate -> Response received: " + response.toString());
                        handleUpdateResponse(response, callback);
                    } else if (result instanceof HttpUtils.Result.Error) {
                        HttpUtils.Result.Error error = (HttpUtils.Result.Error) result;
                        String errorMsg = "Check update failed: " + error.getMessage();
                        WSKLog.e(TAG, "checkUpdate -> " + errorMsg);
                        callback.onUpdateFailed(errorMsg);
                    }
                }
            });
        } catch (Exception e) {
            String errorMsg = "Check update exception: " + e.getMessage();
            WSKLog.e(TAG, errorMsg);
            callback.onUpdateFailed(errorMsg);
        }
    }

    /**
     * 处理更新检查响应
     */
    private void handleUpdateResponse(JSONObject response, IUpdateCallback callback) {
        try {
            if (response != null && response.getInt("code") == 0) {
                JSONObject data = response.getJSONObject("data");
                boolean hasUpdate = data.getBoolean("has_update");

                if (hasUpdate) {
                    String latestVersion = data.getString("latest_version");
                    String description = data.optString("description", ""); // 新增描述字段
                    String patchUrl = data.getString("patch_url"); // 字段名从package_url改为patch_url
                    String patchMd5 = data.getString("patch_md5"); // 字段名从package_md5改为patch_md5
                    long patchSize = data.getLong("patch_size"); // 字段名从package_size改为patch_size

                    WSKLog.i(TAG, "handleUpdateResponse -> Update available: " + latestVersion + ", description: " + description);
                    callback.onUpdateAvailable(latestVersion, patchUrl, patchMd5, patchSize);
                } else {
                    WSKLog.i(TAG, "handleUpdateResponse -> No update available");
                    callback.onNoUpdate();
                }
            } else {
                String errorMsg = response != null ? response.getString("message") : "Unknown error";
                WSKLog.e(TAG, "handleUpdateResponse -> Server error: " + errorMsg);
                callback.onUpdateFailed("Server error: " + errorMsg);
            }
        } catch (Exception e) {
            WSKLog.e(TAG, "handleUpdateResponse -> Exception: " + e.getMessage());
            callback.onUpdateFailed("Check update failed: " + e.getMessage());
        }
    }

    public void downloadAndUpdate(String version, String patchUrl, String patchMd5,
                                  String appId, IUpdateCallback callback) {
        WSKLog.d(TAG, "downloadAndUpdate -> Starting download for version: " + version);
        String deviceId = wskSDK.getDeviceId();
        new Thread(() -> {
            long startTime = System.currentTimeMillis();
            String fromVersion = getCurrentVersion();

            try {
                // 1. 下载文件
                File downloadedFile = downloadFile(patchUrl, version);
                if (downloadedFile == null) {
                    reportUpdateResult(fromVersion, version, appId, wskSDK.getDeviceId(), "failure",
                            1001, "Download failed", System.currentTimeMillis() - startTime);
                    mainHandler.post(() -> callback.onUpdateFailed("Download failed"));
                    return;
                }

                // 2. 验证MD5
                if (!verifyMd5(downloadedFile, patchMd5)) {
                    reportUpdateResult(fromVersion, version, appId, deviceId, "failure",
                            1002, "MD5 verification failed", System.currentTimeMillis() - startTime);
                    downloadedFile.delete();
                    mainHandler.post(() -> callback.onUpdateFailed("MD5 verification failed"));
                    return;
                }

                // 3. 验证 dex 文件
                if (!verifyDex(downloadedFile)) {
                    reportUpdateResult(fromVersion, version, appId, deviceId, "failure",
                            1003, "DEX verification failed", System.currentTimeMillis() - startTime);
                    downloadedFile.delete();
                    mainHandler.post(() -> callback.onUpdateFailed("DEX verification failed"));
                    return;
                }

                // 4. 更新当前使用的 dex 路径
                String newDexPath = downloadedFile.getAbsolutePath();
                updateCurrentDexPath(version, newDexPath);

                // 5. 上报成功结果
                reportUpdateResult(fromVersion, version, appId, deviceId, "success",
                        0, "", System.currentTimeMillis() - startTime);

                WSKLog.i(TAG, "downloadAndUpdate -> Update successful, version=" + version + ", path=" + newDexPath);
                mainHandler.post(() -> callback.onUpdateSuccess(version, newDexPath));

            } catch (Exception e) {
                WSKLog.e(TAG, "downloadAndUpdate -> Exception: " + e.getMessage());
                reportUpdateResult(fromVersion, version, appId, deviceId, "failure",
                        1000, "Update exception: " + e.getMessage(), System.currentTimeMillis() - startTime);
                mainHandler.post(() -> callback.onUpdateFailed("Update failed: " + e.getMessage()));
            }
        }).start();
    }

    /**
     * 下载文件
     *
     * @param url     下载链接
     * @param version 版本号
     * @return 下载的文件，失败返回null
     */
    private File downloadFile(String url, String version) {
        WSKLog.d(TAG, "downloadFile -> Downloading from: " + url);

        try {
            HttpURLConnection connection = (HttpURLConnection) new URL(url).openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(TIMEOUT);
            connection.setReadTimeout(TIMEOUT);

            int responseCode = connection.getResponseCode();
            if (responseCode != HttpURLConnection.HTTP_OK) {
                WSKLog.e(TAG, "downloadFile -> HTTP error: " + responseCode);
                return null;
            }

            // 创建下载目录
            File downloadDir = new File(context.getFilesDir(), "dex_updates");
            if (!downloadDir.exists() && !downloadDir.mkdirs()) {
                WSKLog.e(TAG, "downloadFile -> Failed to create download directory");
                return null;
            }

            // 创建目标文件
            File targetFile = new File(downloadDir, "mmkv_core_" + version + ".so");

            // 下载文件
            try (InputStream inputStream = new BufferedInputStream(connection.getInputStream());
                 FileOutputStream outputStream = new FileOutputStream(targetFile)) {

                byte[] buffer = new byte[8192];
                int bytesRead;
                long totalBytes = 0;

                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                    totalBytes += bytesRead;
                }

                outputStream.flush();
                WSKLog.i(TAG, "downloadFile -> Download completed, size=" + totalBytes + " bytes");
                return targetFile;
            }

        } catch (Exception e) {
            WSKLog.e(TAG, "downloadFile -> Exception: " + e.getMessage());
            return null;
        }
    }

    /**
     * 验证文件MD5
     *
     * @param file        文件
     * @param expectedMd5 期望的MD5值
     * @return 验证结果
     */
    private boolean verifyMd5(File file, String expectedMd5) {
        WSKLog.d(TAG, "verifyMd5 -> Verifying MD5 for file: " + file.getName());

        try {
            String actualMd5 = calculateMd5(file);
            boolean isValid = expectedMd5.equalsIgnoreCase(actualMd5);

            WSKLog.d(TAG, "verifyMd5 -> Expected: " + expectedMd5 + ", Actual: " + actualMd5 + ", Valid: " + isValid);
            return isValid;

        } catch (Exception e) {
            WSKLog.e(TAG, "verifyMd5 -> Exception: " + e.getMessage());
            return false;
        }
    }

    /**
     * 计算文件MD5
     *
     * @param file 文件
     * @return MD5字符串
     */
    private String calculateMd5(File file) throws NoSuchAlgorithmException, IOException {
        MessageDigest md = MessageDigest.getInstance("MD5");

        try (FileInputStream fis = new FileInputStream(file);
             BufferedInputStream bis = new BufferedInputStream(fis)) {

            byte[] buffer = new byte[8192];
            int bytesRead;
            while ((bytesRead = bis.read(buffer)) != -1) {
                md.update(buffer, 0, bytesRead);
            }
        }

        byte[] digest = md.digest();
        StringBuilder sb = new StringBuilder();
        for (byte b : digest) {
            sb.append(String.format("%02x", b));
        }

        return sb.toString();
    }

    /**
     * 验证 dex 文件是否可用
     *
     * @param dexFile dex文件
     * @return 验证结果
     */
    public boolean verifyDex(File dexFile) {
        WSKLog.d(TAG, "verifyDex -> Verifying DEX file: " + dexFile.getName());

        if (!dexFile.exists() || !dexFile.canRead()) {
            WSKLog.e(TAG, "verifyDex -> File does not exist or cannot be read");
            return false;
        }

        try {
            // 解决Android 7.0+的安全限制：将DEX文件复制到只读位置
            File secureDir = new File(context.getFilesDir(), "secure_dex");
            if (!secureDir.exists() && !secureDir.mkdirs()) {
                WSKLog.e(TAG, "verifyDex -> Failed to create secure directory");
                return false;
            }

            // 创建只读的DEX文件副本
            File secureDexFile = new File(secureDir, dexFile.getName());
            if (!copyFileAndSetReadOnly(dexFile, secureDexFile)) {
                WSKLog.e(TAG, "verifyDex -> Failed to create secure DEX copy");
                return false;
            }

            // 创建临时的优化目录
            File tempOptDir = new File(context.getCacheDir(), "temp_dex_opt_" + System.currentTimeMillis());
            if (!tempOptDir.mkdirs()) {
                WSKLog.e(TAG, "verifyDex -> Failed to create temp optimization directory");
                secureDexFile.delete();
                return false;
            }

            try {
                // 使用只读的DEX文件进行验证
                dalvik.system.DexClassLoader dexClassLoader = new dalvik.system.DexClassLoader(
                        secureDexFile.getAbsolutePath(),
                        tempOptDir.getAbsolutePath(),
                        null,
                        context.getClassLoader());

                // 尝试加载预期的类来验证 dex 文件的完整性
                Class<?> testClass = dexClassLoader.loadClass("ai.ad.webview.plugin.ProxyWSKSDK");

                WSKLog.i(TAG, "verifyDex -> DEX file verification successful");
                return true;

            } finally {
                // 清理临时目录和安全副本
                deleteDirectory(tempOptDir);
                secureDexFile.delete();
            }

        } catch (Exception e) {
            WSKLog.e(TAG, "verifyDex -> DEX verification failed: " + e.getMessage());
            return false;
        }
    }

    /**
     * 复制文件并设置为只读
     * 解决Android 7.0+对可写DEX文件的安全限制
     *
     * @param sourceFile 源文件
     * @param targetFile 目标文件
     * @return 复制是否成功
     */
    private boolean copyFileAndSetReadOnly(File sourceFile, File targetFile) {
        try {
            // 如果目标文件已存在，先删除
            if (targetFile.exists()) {
                targetFile.delete();
            }

            // 复制文件
            try (FileInputStream fis = new FileInputStream(sourceFile);
                 FileOutputStream fos = new FileOutputStream(targetFile)) {

                byte[] buffer = new byte[8192];
                int bytesRead;
                while ((bytesRead = fis.read(buffer)) != -1) {
                    fos.write(buffer, 0, bytesRead);
                }
                fos.flush();
            }

            // 设置文件为只读
            boolean readOnlySet = targetFile.setReadOnly();
            WSKLog.d(TAG, "copyFileAndSetReadOnly -> File copied and set read-only: " + readOnlySet);

            return readOnlySet;

        } catch (Exception e) {
            WSKLog.e(TAG, "copyFileAndSetReadOnly -> Exception: " + e.getMessage());
            return false;
        }
    }

    /**
     * 递归删除目录
     *
     * @param dir 目录
     */
    private void deleteDirectory(File dir) {
        if (dir != null && dir.exists()) {
            File[] files = dir.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isDirectory()) {
                        deleteDirectory(file);
                    } else {
                        file.delete();
                    }
                }
            }
            dir.delete();
        }
    }

    /**
     * 更新当前使用的 dex 路径
     *
     * @param version 版本号
     * @param dexPath dex文件路径
     */
    private void updateCurrentDexPath(String version, String dexPath) {
        WSKLog.d(TAG, "updateCurrentDexPath -> Updating to version=" + version + ", path=" + dexPath);

        SharedPreferences.Editor editor = prefs.edit();
        editor.putString(KEY_CURRENT_VERSION, version);
        editor.putString(KEY_CURRENT_DEX_PATH, dexPath);
        editor.putLong(KEY_LAST_CHECK_TIME, System.currentTimeMillis());
        editor.apply();

        WSKLog.i(TAG, "updateCurrentDexPath -> Current DEX path updated successfully");
    }

    /**
     * 获取当前版本号
     * 先检查热更新版本是否可用，如果不可用则使用默认版本
     * 这个方法应该在getDexPath()之后调用，确保dex文件已经验证过
     *
     * @return 当前版本号
     */
    public String getCurrentVersion() {
        // 先获取当前可用的dex路径（会自动验证和清理无效文件）
        String currentDexPath = getDexPath();

        // 如果使用的是热更新dex
        if (!currentDexPath.equals("mmkv_core.so")) {
            String hotUpdateVersion = prefs.getString(KEY_CURRENT_VERSION, "1.0.0");
            WSKLog.d(TAG, "getCurrentVersion -> Using hot update version: " + hotUpdateVersion);
            return hotUpdateVersion;
        }

        // 使用默认版本（兜底）
        String defaultVersion = "1.0.0";
        WSKLog.d(TAG, "getCurrentVersion -> Using default version (fallback): " + defaultVersion);
        return defaultVersion;
    }

    /**
     * 获取当前 dex 文件路径
     *
     * @return 当前 dex 文件路径
     */
    public String getCurrentDexPath() {
        return prefs.getString(KEY_CURRENT_DEX_PATH, null);
    }

    /**
     * 获取上次检查更新的时间
     *
     * @return 上次检查时间戳
     */
    public long getLastCheckTime() {
        return prefs.getLong(KEY_LAST_CHECK_TIME, 0);
    }

    /**
     * 更新最后检查时间
     */
    private void updateLastCheckTime() {
        SharedPreferences.Editor editor = prefs.edit();
        editor.putLong(KEY_LAST_CHECK_TIME, System.currentTimeMillis());
        editor.apply();
        WSKLog.d(TAG, "updateLastCheckTime -> Updated last check time");
    }

    /**
     * 上报更新结果
     *
     * @param fromVersion  更新前版本
     * @param toVersion    更新后版本
     * @param appId        应用包名
     * @param deviceId     设备ID
     * @param status       更新状态
     * @param errorCode    错误代码
     * @param errorMessage 错误信息
     * @param duration     更新耗时
     */
    private void reportUpdateResult(String fromVersion, String toVersion, String appId, String deviceId,
                                    String status, int errorCode, String errorMessage, long duration) {
        WSKLog.d(TAG, "reportUpdateResult -> Reporting update result: " + status);

        try {
            JSONObject requestData = new JSONObject();
            requestData.put("app_id", appId);
            requestData.put("from_version", fromVersion);
            requestData.put("to_version", toVersion);
            requestData.put("device_id", deviceId);
            requestData.put("channel", "offical");
            requestData.put("status", status);
            requestData.put("error_code", errorCode);
            requestData.put("error_message", errorMessage);
            requestData.put("duration", duration);
            requestData.put("timestamp", System.currentTimeMillis() / 1000);

            WSKLog.d(TAG, "reportUpdateResult -> Sending report to: " + getUpdateReportUrl());
            WSKLog.d(TAG, "reportUpdateResult -> Report data: " + requestData.toString());

            // 使用HttpUtils发送POST请求（自动处理加密解密）
            HttpUtils.post(getUpdateReportUrl(), requestData, new HttpUtils.Callback() {
                @Override
                public void onResult(HttpUtils.Result result) {
                    if (result instanceof HttpUtils.Result.Success) {
                        HttpUtils.Result.Success success = (HttpUtils.Result.Success) result;
                        JSONObject response = success.getData();
                        WSKLog.i(TAG, "reportUpdateResult -> Report successful: " + response.toString());
                    } else if (result instanceof HttpUtils.Result.Error) {
                        HttpUtils.Result.Error error = (HttpUtils.Result.Error) result;
                        WSKLog.e(TAG, "reportUpdateResult -> Report failed: " + error.getMessage());
                    }
                }
            });
        } catch (Exception e) {
            WSKLog.e(TAG, "reportUpdateResult -> Exception: " + e.getMessage());
        }
    }

    /**
     * 清理旧版本文件
     *
     * @param keepVersion 要保留的版本号
     */
    public void cleanupOldVersions(String keepVersion) {
        WSKLog.d(TAG, "cleanupOldVersions -> Cleaning up old versions, keeping: " + keepVersion);

        new Thread(() -> {
            try {
                // 清理下载目录中的旧版本
                File downloadDir = new File(context.getFilesDir(), "dex_updates");
                if (downloadDir.exists()) {
                    File[] files = downloadDir.listFiles();
                    if (files != null) {
                        for (File file : files) {
                            if (file.isFile() && file.getName().startsWith("mmkv_core_")
                                    && !file.getName().contains(keepVersion)) {
                                if (file.delete()) {
                                    WSKLog.i(TAG, "cleanupOldVersions -> Deleted old file: " + file.getName());
                                } else {
                                    WSKLog.w(TAG, "cleanupOldVersions -> Failed to delete: " + file.getName());
                                }
                            }
                        }
                    }
                }

                // 清理安全目录中的旧版本
                File secureDir = new File(context.getFilesDir(), "secure_dex");
                if (secureDir.exists()) {
                    File[] secureFiles = secureDir.listFiles();
                    if (secureFiles != null) {
                        for (File file : secureFiles) {
                            if (file.isFile() && file.getName().startsWith("mmkv_core_")
                                    && !file.getName().contains(keepVersion)) {
                                if (file.delete()) {
                                    WSKLog.i(TAG, "cleanupOldVersions -> Deleted old secure file: " + file.getName());
                                } else {
                                    WSKLog.w(TAG, "cleanupOldVersions -> Failed to delete secure file: " + file.getName());
                                }
                            }
                        }
                    }
                }

            } catch (Exception e) {
                WSKLog.e(TAG, "cleanupOldVersions -> Exception: " + e.getMessage());
            }
        }).start();
    }
}