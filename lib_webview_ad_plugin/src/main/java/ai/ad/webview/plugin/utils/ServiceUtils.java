package ai.ad.webview.plugin.utils;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Build;

import ai.ad.webview.sdk.logger.WSKLog;

/**
 * 服务工具类，用于处理服务启动逻辑
 */
public class ServiceUtils {
    private static final String TAG = "ServiceUtils";

    private ServiceUtils() {
        // 私有构造函数，防止实例化
    }

    /**
     * 启动服务，根据Android版本和通知权限状态自动选择合适的启动方式
     *
     * @param context       上下文
     * @param serviceIntent 要启动的服务Intent
     * @param serviceName   服务名称，用于日志记录
     */
    public static void startService(Context context, Intent serviceIntent, String serviceName) {
        try {
            context.startService(serviceIntent);
            WSKLog.d(TAG, "Successfully started regular service: " + serviceName);
        } catch (Exception e) {
            WSKLog.e(TAG, "Failed to start regular service " + serviceName + ": " + e.getMessage());
        }
    }

    /**
     * 安全地注册广播接收器，处理不同Android版本的兼容性
     *
     * @param context  上下文
     * @param receiver 广播接收器
     * @param filter   意图过滤器
     * @param logTag   日志标签
     */
    public static void registerReceiverSafely(Context context, BroadcastReceiver receiver,
                                              IntentFilter filter, String logTag) {
        try {
            if (Build.VERSION.SDK_INT >= 33) { // Android 13 (TIRAMISU)
                // Android 13及以上版本需要明确指定标志
                context.registerReceiver(receiver, filter, 2);
                WSKLog.d(TAG, logTag + ": Registering broadcast receiver with RECEIVER_NOT_EXPORTED flag");
            } else {
                // 旧版本Android
                context.registerReceiver(receiver, filter);
                WSKLog.d(TAG, logTag + ": Registering broadcast receiver");
            }
        } catch (Exception e) {
            WSKLog.e(TAG, logTag + ": Failed to register broadcast receiver: " + e.getMessage());
        }
    }
}
