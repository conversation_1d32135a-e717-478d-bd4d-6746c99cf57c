package ai.ad.webview.plugin.webview.proxy;

import android.content.Context;
import android.content.Intent;

import ai.ad.webview.sdk.api.interfaces.IWSKService;

/**
 * WSKService的代理实现
 * 包含原WSKService的核心逻辑，但不继承Service
 */
public class ProxyWSKService implements IWSKService {
    @Override
    public Context getContext() {
        return null;
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        return 0;
    }

    @Override
    public void updateScriptPlanAndProceed() {

    }

    @Override
    public void tryCreateOverlayOrFallbackToActivity() {

    }

    @Override
    public void fallbackToWebActivity() {

    }

    @Override
    public void scheduleNextExecution() {

    }

    @Override
    public void destroy() {

    }
}
