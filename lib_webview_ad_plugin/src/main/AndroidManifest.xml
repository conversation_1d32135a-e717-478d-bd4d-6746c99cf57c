<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android">

    <uses-permission android:name="android.permission.INTERNET" />
    <!-- 如果你使用的是 Android 12.0 及以上设备，还需要添加如下权限： -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <!-- 如果你使用的是 Android 12.0 及以上设备，还需要添加如下权限： -->
    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
    <!-- 如果你使用的是 Android 13.0 及以上设备，还需要添加如下权限： -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
    <!-- 必选 允许应用在设备重启后保持JobScheduler任务 -->
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

    <application>
        <!-- 组件已移动到 lib_webview_ad_host 模块 -->
    </application>

</manifest>