plugins {
    id 'com.android.library'
}

android {
    namespace 'ai.ad.webview.sdk'
    compileSdk 28

    defaultConfig {
        minSdk 21
        targetSdk 28
        versionCode 1
        versionName "1.0"
    }

    buildTypes {
        debug {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro', 'consumer-rules.pro'
            consumerProguardFiles 'consumer-rules.pro'
        }
        release {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro', 'consumer-rules.pro'
            consumerProguardFiles 'consumer-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

dependencies {
    compileOnly project(':lib_webview_ad_sdk')
    compileOnly 'com.google.android.gms:play-services-ads-identifier:18.0.1'
}