改动的目的是 为了实现 本sdk 的 插件化, 能编译成 dex , 动态让 宿主 sdk 加载, 所以无需包含四大组件
代理的四大组件是为了测试用而已, 本项目编译成插件 dex 你还有什么建议?

我预期的重构步骤如下
bensdk 最终只暴露WSKSDK

具体 proxy'改动如下:
WSKJobService.java
WSKActivity.java
WSKService.java
内部的具体逻辑, 全部改成代理模式
ProxyWSKJobService.java
ProxyWSKActivity.java
ProxyWSKService.java

WSKSDK.java 能new这三个代理类的实例
组件类要从 webresourcesdk 获取代理类的实例

要设计代理类的接口类, 方便替换代理
代理接口类 需要在混淆配置里, 增加防止接口被混淆

混淆配置 需加namespace 前缀 = ai.ad.core

混淆配置 去掉WSKJobService.java, WSKActivity.java, WSKService.java
这三个类的混淆配置

请你说出你的重构方案, 还有改进点

最后执行 /Users/<USER>/Projects/android_webview_ad_sdk/run_recoder.sh
验证修改 