# WebView Ad SDK 新架构流程图

## 组件关系图

```mermaid
flowchart TD
    A[InAppOverlayManager] --> B[InAppOverlayView]
    B --> C[WSKDelegate]
    B <--> D[ScriptScheduler]
    C --> E[WebView]
    D --> F[ScriptRepo]
```

## 脚本执行流程

```mermaid
sequenceDiagram
    participant M as InAppOverlayManager
    participant V as InAppOverlayView
    participant S as ScriptScheduler
    participant R as ScriptRepo
    participant D as WSKDelegate
    participant W as WebView

    M->>V: 创建
    V->>S: 设置任务监听器
    M->>V: show()
    V->>S: start()
    S->>S: 定时检查
    S->>R: 获取脚本数据
    R-->>S: 返回脚本数据
    S->>V: onScriptReady(scriptData)
    V->>D: 设置脚本完成监听器
    V->>D: executeScriptData(scriptData)
    D->>W: 执行脚本
    W-->>D: 脚本执行完成
    D-->>V: onScriptCompleted()
    V-->>S: notifyScriptCompleted()
    S->>S: 重置执行状态
```

## 新架构优势

1. **简化的组件关系**：
   - 移除了 IInAppOverlayScriptController 中间层
   - InAppOverlayView 直接与 ScriptScheduler 交互

2. **明确的职责划分**：
   - InAppOverlayView：负责创建和管理 WebView
   - ScriptScheduler：负责定时拉取脚本计划
   - WSKDelegate：负责执行脚本

3. **简化的脚本执行流程**：
   - ScriptScheduler 获取脚本数据并通知 InAppOverlayView
   - InAppOverlayView 使用 WSKDelegate 执行脚本
   - 执行完成后通知 ScriptScheduler

4. **更好的资源管理**：
   - WebView 只在 InAppOverlayView 中创建
   - 脚本执行完成后及时释放资源

## 使用方法

```java
// 初始化
InAppOverlayManager manager = new InAppOverlayManager();
manager.initialize(application);

// 启用悬浮窗
manager.enableOverlay();

// 禁用悬浮窗
manager.disableOverlay();
```
