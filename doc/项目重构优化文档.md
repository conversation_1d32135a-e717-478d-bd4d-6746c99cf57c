# Android WebView Ad SDK 项目重构优化文档

## 1. 现状分析

当前项目是一个 Android WebView 广告 SDK，主要功能包括脚本执行、事件上报等。目前的项目结构存在以下问题：

1. **职责不清晰**：部分类（如 ScriptRepo）承担了过多职责，包括数据获取、存储和上报等
2. **模块边界模糊**：业务逻辑、数据访问、工具类等混合在一起
3. **代码复用性低**：存在重复代码，特别是在网络请求和数据处理方面
4. **可测试性差**：由于依赖关系复杂，单元测试难以实施

## 2. 架构优化目标

1. **清晰的模块划分**：按功能和职责划分模块，降低模块间耦合
2. **单一职责原则**：每个类只负责一个特定功能
3. **依赖注入**：使用依赖注入降低组件间硬依赖
4. **可测试性**：提高代码的可测试性
5. **代码复用**：减少重复代码，提高复用性

## 3. 推荐架构模式

推荐采用 **Clean Architecture（干净架构）** 结合 **MVVM 模式**，将应用分为以下几层：

1. **表现层（Presentation）**：UI 组件和 ViewModel
2. **领域层（Domain）**：业务逻辑和用例
3. **数据层（Data）**：数据源、仓库实现和模型

## 4. 模块划分建议

### 4.1 按功能模块划分

```
lib_webview_ad_sdk/
├── core/                  # 核心功能和基础设施
│   ├── di/                # 依赖注入
│   ├── network/           # 网络相关
│   ├── storage/           # 存储相关
│   └── utils/             # 通用工具类
├── script/                # 脚本相关功能
│   ├── data/              # 脚本数据层
│   │   ├── repository/    # 仓库实现
│   │   ├── source/        # 数据源
│   │   └── model/         # 数据模型
│   ├── domain/            # 脚本业务逻辑
│   │   ├── usecase/       # 用例
│   │   └── model/         # 领域模型
│   └── presentation/      # 脚本表现层
│       ├── viewmodel/     # ViewModel
│       └── ui/            # UI 组件
├── report/                # 上报相关功能
│   ├── data/              # 上报数据层
│   ├── domain/            # 上报业务逻辑
│   └── presentation/      # 上报表现层
├── webview/               # WebView 相关功能
│   ├── data/              # WebView 数据层
│   ├── domain/            # WebView 业务逻辑
│   └── presentation/      # WebView 表现层
└── security/              # 安全相关功能
    ├── crypto/            # 加解密
    └── permission/        # 权限管理
```

### 4.2 按层级划分

```
lib_webview_ad_sdk/
├── data/                  # 数据层
│   ├── repository/        # 仓库实现
│   ├── source/            # 数据源
│   │   ├── local/         # 本地数据源
│   │   └── remote/        # 远程数据源
│   └── model/             # 数据模型
├── domain/                # 领域层
│   ├── repository/        # 仓库接口
│   ├── usecase/           # 用例
│   └── model/             # 领域模型
├── presentation/          # 表现层
│   ├── viewmodel/         # ViewModel
│   └── ui/                # UI 组件
│       ├── activity/      # Activity
│       ├── fragment/      # Fragment
│       ├── view/          # 自定义 View
│       └── adapter/       # Adapter
└── common/                # 通用功能
    ├── di/                # 依赖注入
    ├── utils/             # 工具类
    ├── extension/         # 扩展函数
    └── security/          # 安全相关
```

## 5. 具体模块优化建议

### 5.1 Script 模块优化

#### 5.1.1 数据层（Data）

**ScriptRepository 实现**：
```kotlin
class ScriptRepositoryImpl(
    private val remoteDataSource: ScriptRemoteDataSource,
    private val localDataSource: ScriptLocalDataSource,
    private val encryptionManager: EncryptionManager
) : ScriptRepository {
    
    override suspend fun getScriptPlan(appId: String): Result<ScriptPlan> {
        // 实现逻辑
    }
    
    override suspend fun getScriptData(scriptId: String): Result<ScriptData> {
        // 实现逻辑
    }
    
    // 其他方法
}
```

**数据源**：
```kotlin
interface ScriptRemoteDataSource {
    suspend fun fetchScriptPlan(appId: String): Result<ScriptPlanDto>
    suspend fun fetchScriptData(scriptId: String): Result<ScriptDataDto>
}

interface ScriptLocalDataSource {
    suspend fun getScriptPlan(): ScriptPlanDto?
    suspend fun saveScriptPlan(scriptPlan: ScriptPlanDto)
    suspend fun getScriptData(scriptId: String): ScriptDataDto?
    suspend fun saveScriptData(scriptData: ScriptDataDto)
}
```

#### 5.1.2 领域层（Domain）

**仓库接口**：
```kotlin
interface ScriptRepository {
    suspend fun getScriptPlan(appId: String): Result<ScriptPlan>
    suspend fun getScriptData(scriptId: String): Result<ScriptData>
    suspend fun selectRandomScript(): Result<Script>
    // 其他方法
}
```

**用例**：
```kotlin
class GetScriptPlanUseCase(private val repository: ScriptRepository) {
    suspend operator fun invoke(appId: String): Result<ScriptPlan> {
        return repository.getScriptPlan(appId)
    }
}

class ExecuteScriptUseCase(
    private val repository: ScriptRepository,
    private val webViewManager: WebViewManager
) {
    suspend operator fun invoke(scriptId: String): Result<Unit> {
        // 实现逻辑
    }
}
```

#### 5.1.3 表现层（Presentation）

**ViewModel**：
```kotlin
class ScriptViewModel(
    private val getScriptPlanUseCase: GetScriptPlanUseCase,
    private val executeScriptUseCase: ExecuteScriptUseCase
) : ViewModel() {
    
    private val _scriptState = MutableLiveData<ScriptState>()
    val scriptState: LiveData<ScriptState> = _scriptState
    
    fun loadScriptPlan(appId: String) {
        viewModelScope.launch {
            _scriptState.value = ScriptState.Loading
            getScriptPlanUseCase(appId).fold(
                onSuccess = { _scriptState.value = ScriptState.Success(it) },
                onFailure = { _scriptState.value = ScriptState.Error(it.message ?: "Unknown error") }
            )
        }
    }
    
    fun executeScript(scriptId: String) {
        viewModelScope.launch {
            _scriptState.value = ScriptState.Loading
            executeScriptUseCase(scriptId).fold(
                onSuccess = { _scriptState.value = ScriptState.ExecutionSuccess },
                onFailure = { _scriptState.value = ScriptState.Error(it.message ?: "Execution failed") }
            )
        }
    }
}

sealed class ScriptState {
    object Loading : ScriptState()
    data class Success(val scriptPlan: ScriptPlan) : ScriptState()
    object ExecutionSuccess : ScriptState()
    data class Error(val message: String) : ScriptState()
}
```

### 5.2 Report 模块优化

#### 5.2.1 数据层（Data）

**ReportRepository 实现**：
```kotlin
class ReportRepositoryImpl(
    private val remoteDataSource: ReportRemoteDataSource,
    private val deviceInfoProvider: DeviceInfoProvider
) : ReportRepository {
    
    override suspend fun reportEvent(eventKey: String, eventProps: Map<String, String>): Result<Unit> {
        // 实现逻辑
    }
    
    override suspend fun reportExecutionStart(scriptId: String, url: String): Result<Unit> {
        val deviceId = deviceInfoProvider.getDeviceId()
        return reportEvent(
            "execution_start",
            mapOf(
                "deviceId" to deviceId,
                "script_id" to scriptId,
                "url" to url
            )
        )
    }
    
    // 其他方法
}
```

#### 5.2.2 领域层（Domain）

**仓库接口**：
```kotlin
interface ReportRepository {
    suspend fun reportEvent(eventKey: String, eventProps: Map<String, String>): Result<Unit>
    suspend fun reportExecutionStart(scriptId: String, url: String): Result<Unit>
    suspend fun reportExecutionResult(scriptId: String, status: String, duration: Long, url: String, failureReason: String? = null, failureMessage: String? = null): Result<Unit>
    suspend fun postCompletionStatus(recordId: Int): Result<Unit>
}
```

**用例**：
```kotlin
class ReportEventUseCase(private val repository: ReportRepository) {
    suspend operator fun invoke(eventKey: String, eventProps: Map<String, String>): Result<Unit> {
        return repository.reportEvent(eventKey, eventProps)
    }
}

class ReportExecutionStartUseCase(private val repository: ReportRepository) {
    suspend operator fun invoke(scriptId: String, url: String): Result<Unit> {
        return repository.reportExecutionStart(scriptId, url)
    }
}
```

### 5.3 安全模块优化

#### 5.3.1 加解密

**加解密管理器**：
```kotlin
interface EncryptionManager {
    fun encrypt(data: String): String
    fun decrypt(encryptedData: String): String
    fun generateKey(): String
}

class AESEncryptionManager : EncryptionManager {
    override fun encrypt(data: String): String {
        // 实现 AES 加密
    }
    
    override fun decrypt(encryptedData: String): String {
        // 实现 AES 解密
    }
    
    override fun generateKey(): String {
        // 生成密钥
    }
}
```

#### 5.3.2 安全存储

```kotlin
interface SecureStorage {
    fun saveString(key: String, value: String)
    fun getString(key: String): String?
    fun saveEncrypted(key: String, value: String)
    fun getDecrypted(key: String): String?
}

class SecureStorageImpl(
    private val context: Context,
    private val encryptionManager: EncryptionManager
) : SecureStorage {
    private val prefs = context.getSharedPreferences("secure_prefs", Context.MODE_PRIVATE)
    
    override fun saveString(key: String, value: String) {
        prefs.edit().putString(key, value).apply()
    }
    
    override fun getString(key: String): String? {
        return prefs.getString(key, null)
    }
    
    override fun saveEncrypted(key: String, value: String) {
        val encrypted = encryptionManager.encrypt(value)
        saveString(key, encrypted)
    }
    
    override fun getDecrypted(key: String): String? {
        val encrypted = getString(key) ?: return null
        return encryptionManager.decrypt(encrypted)
    }
}
```

## 6. 依赖注入

推荐使用 Koin 或 Hilt 进行依赖注入，简化组件间的依赖关系。

**使用 Koin 的示例**：
```kotlin
val appModule = module {
    // 单例
    single { NetworkClient() }
    single<ScriptRepository> { ScriptRepositoryImpl(get(), get(), get()) }
    single<ReportRepository> { ReportRepositoryImpl(get(), get()) }
    single<EncryptionManager> { AESEncryptionManager() }
    single<SecureStorage> { SecureStorageImpl(androidContext(), get()) }
    
    // 工厂
    factory { GetScriptPlanUseCase(get()) }
    factory { ExecuteScriptUseCase(get(), get()) }
    factory { ReportEventUseCase(get()) }
    
    // ViewModel
    viewModel { ScriptViewModel(get(), get()) }
}
```

## 7. 网络层优化

推荐使用 Retrofit + Kotlin 协程进行网络请求：

```kotlin
interface ApiService {
    @GET("script/plan")
    suspend fun getScriptPlan(@Query("appId") appId: String): Response<ScriptPlanDto>
    
    @GET("script/data/{scriptId}")
    suspend fun getScriptData(@Path("scriptId") scriptId: String): Response<ScriptDataDto>
    
    @POST("report/event")
    suspend fun reportEvent(@Body event: EventRequestDto): Response<Unit>
}

class NetworkClient {
    private val retrofit = Retrofit.Builder()
        .baseUrl("https://api.example.com/")
        .addConverterFactory(GsonConverterFactory.create())
        .build()
    
    val apiService: ApiService = retrofit.create(ApiService::class.java)
}
```

## 8. 错误处理

统一错误处理机制：

```kotlin
sealed class Result<out T> {
    data class Success<T>(val data: T) : Result<T>()
    data class Error(val exception: Exception) : Result<Nothing>()
}

inline fun <T> runCatching(block: () -> T): Result<T> {
    return try {
        Result.Success(block())
    } catch (e: Exception) {
        Result.Error(e)
    }
}

inline fun <T, R> Result<T>.map(transform: (T) -> R): Result<R> {
    return when (this) {
        is Result.Success -> Result.Success(transform(data))
        is Result.Error -> this
    }
}

inline fun <T> Result<T>.fold(
    onSuccess: (T) -> Unit,
    onFailure: (Exception) -> Unit
) {
    when (this) {
        is Result.Success -> onSuccess(data)
        is Result.Error -> onFailure(exception)
    }
}
```

## 9. 日志系统优化

统一日志系统，支持不同级别的日志和日志文件输出：

```kotlin
object Logger {
    private const val SDK_TAG = "WSKSDK"
    private var isDebugEnabled = false
    private var logToFile = false
    private var logFilePath: String? = null
    
    fun init(debug: Boolean, logToFile: Boolean = false, logFilePath: String? = null) {
        this.isDebugEnabled = debug
        this.logToFile = logToFile
        this.logFilePath = logFilePath
    }
    
    fun d(tag: String, message: String) {
        if (isDebugEnabled) {
            Log.d("$SDK_TAG:$tag", message)
            if (logToFile) {
                writeToFile("D", tag, message)
            }
        }
    }
    
    fun e(tag: String, message: String, throwable: Throwable? = null) {
        Log.e("$SDK_TAG:$tag", message, throwable)
        if (logToFile) {
            writeToFile("E", tag, message, throwable)
        }
    }
    
    // 其他日志方法
    
    private fun writeToFile(level: String, tag: String, message: String, throwable: Throwable? = null) {
        // 实现日志文件写入
    }
}
```

## 10. 测试策略

### 10.1 单元测试

为每个模块编写单元测试，特别是业务逻辑和数据处理部分：

```kotlin
class ScriptRepositoryTest {
    @Test
    fun `getScriptPlan should return cached plan when available`() {
        // 测试实现
    }
    
    @Test
    fun `getScriptPlan should fetch from remote when cache is empty`() {
        // 测试实现
    }
}
```

### 10.2 集成测试

测试模块间的交互：

```kotlin
class ScriptIntegrationTest {
    @Test
    fun `execute script should report execution start and result`() {
        // 测试实现
    }
}
```

### 10.3 UI 测试

使用 Espresso 测试 UI 组件：

```kotlin
class WebViewActivityTest {
    @Test
    fun testWebViewLoading() {
        // 测试实现
    }
}
```

## 11. 迁移策略

### 11.1 渐进式迁移

1. **创建新的架构骨架**：先创建新的架构结构和核心接口
2. **模块化重构**：一个模块一个模块地进行重构
3. **适配层**：在过渡期使用适配层连接新旧代码
4. **测试验证**：每完成一个模块的重构就进行测试验证
5. **逐步替换**：逐步用新实现替换旧实现

### 11.2 优先级

1. 首先重构核心基础设施（网络、存储、日志等）
2. 然后重构数据层（Repository 和数据源）
3. 接着重构领域层（用例和业务逻辑）
4. 最后重构表现层（UI 和 ViewModel）

## 12. 总结

通过以上优化，项目将获得以下好处：

1. **清晰的职责划分**：每个类和模块都有明确的职责
2. **松耦合**：通过依赖注入和接口隔离，降低组件间耦合
3. **可测试性**：便于编写单元测试和集成测试
4. **可维护性**：代码结构清晰，易于理解和维护
5. **可扩展性**：新功能可以方便地集成到现有架构中

这种架构不仅适用于当前项目，也为未来的功能扩展和维护提供了坚实的基础。
