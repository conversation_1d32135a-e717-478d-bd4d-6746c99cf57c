定时启动透明activity方案调研

 Android 版本 | API 版本 | Build.VERSION_CODES.?? |
|----------------------|----------|--------------------------|
| Android 14 (Upside Down Cake) | 34 | UPSIDE_DOWN_CAKE |
| Android 13 (Tiramisu) | 33 | TIRAMISU |
| Android 12 (Snow Cone) | 32 | S |
| Android 11 (Red Velvet Cake) | 30 | R |
| Android 10 (Q) | 29 | Q |
| Android 9 (Pie) | 28 | P |
| Android 8.1 (Oreo) | 27 | O_MR1 |
| Android 8.0 (Oreo) | 26 | O |
| Android 7.1 (Nougat) | 25 | N_MR1 |
| Android 7.0 (Nougat) | 24 | N |
| Android 6.0 (Marshmallow) | 23 | M |
| Android 5.1 (Lollipop) | 22 | LOLLIPOP_MR1 |
| Android 5.0 (Lollipop) | 21 | LOLLIPOP |
| Android 4.4 (KitKat) | 19 | KITKAT |
| Android 4.3 (Jelly Bean MR2) | 18 | JELLY_BEAN_MR2 |

# 通过前台服务, 启动透明activity

| 方案描述 | 适用场景 | 限制条件 |
|----------|----------|----------|
| 利用漏洞，无需发送通知，也能启动前台服务 | 4.3-Android 7.1 |  |
| 普通服务,启动acitivy | x-9 | |
| 前台服务:无需申请通知权限，发送通知然后启动前台服务 | Android x-12 | 无法指定前台服务类型，14、15无法使用前台服务启动Activity |
| 前台服务:有通知权限，发送通知权限启动前台服务 | Android 13 | 需要通知权限 |

# 子进程保活

# 总结与实现细节

## 实现方案可行性分析

| Android版本 | 实现方案 | 可行性 | 权限需求 | 最佳实践 |
|------------|---------|-------|---------|---------|
| Android 14-15 | 普通服务 + JobScheduler | 不可行 | - | 从Android 10开始，后台应用无法直接启动Activity，14-15更加严格 |
| Android 14-15 | 不实现定时启动透明Activity | 高 | - | 使用其他替代方案，如通知、Widget或工作管理器 |
| Android 13 | 前台服务 + JobScheduler | 中 | 需要通知权限 (`POST_NOTIFICATIONS`) | 用户授权通知权限后才能实现 |
| Android 9-12 | 前台服务 + JobScheduler | 高 | 无需单独申请通知权限 | 需要设置合适的前台服务通知，提高用户体验 |
| Android 8 | 前台服务 + JobScheduler | 高 | 无需单独申请通知权限 | 需要适配后台限制策略 |
| Android 7.1及以下 | 可利用无通知前台服务漏洞 | 高 | 最小权限需求 | 更容易实现，但不推荐在新应用中使用 |

## 前台服务实现关键点

1. **进程选择**：
   - **主进程**：适合大多数场景，简化实现
   - **子进程**：提高稳定性，主进程被杀时仍可保持运行，但增加实现复杂度

2. **JobScheduler定时任务设置**：

   ```java
   JobInfo jobInfo = new JobInfo.Builder(JOB_ID, new ComponentName(context, YourJobService.class))
       .setPeriodic(15 * 60 * 1000)  // 最小15分钟间隔（Android 7.0+限制）
       .setRequiredNetworkType(JobInfo.NETWORK_TYPE_ANY)
       .setPersisted(true)  // 需要RECEIVE_BOOT_COMPLETED权限
       .build();
   ```

3. **前台服务启动**：

   ```java
   // Android 8.0+
   if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
       context.startForegroundService(serviceIntent);
   } else {
       context.startService(serviceIntent);
   }
   ```

4. **权限处理**：

   ```java
   // Android 13+
   if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
       if (ContextCompat.checkSelfPermission(context, Manifest.permission.POST_NOTIFICATIONS) 
               != PackageManager.PERMISSION_GRANTED) {
           // 请求权限
       }
   }
   ```

5. **透明Activity设计**：
   - 使用透明主题
   - 最小化生命周期影响
   - 仅执行必要操作后迅速结束

## 总结建议

- **Android 14-15**：不建议使用定时启动透明Activity，使用通知或其他官方推荐方式
- **Android 9-13**：使用前台服务 + JobScheduler实现，注意Android 13需要通知权限
- **Android 8及以下**：可使用前台服务或更宽松的后台策略实现
- **子进程使用建议**：仅在需要更高可靠性且能接受复杂度增加的情况下使用子进程
