# 事件录制回放说明文档

## 1. 概述
本文档描述了事件录制和回放功能的数据结构及实现细节。系统使用 JSON 格式存储所有录制的事件数据，支持点击和滑动等触摸事件的记录与重现。

## 2. 数据结构

### 2.1 完整数据结构示例
```json
{

    // 元数据对象
    "m": {
        "w":    1080,                  // webview真实的宽度
        "h": 2340,                  // 设备屏幕高度
    },

    // 事件数组
    "e": [
        {
            "a": "c",               // 点击事件
            "t": 0,                 // 相对时间（毫秒）
            "x": 100,               // X坐标
            "y": 200                // Y坐标
        },
        {
            "a": "d",               // 按下事件
            "t": 1000,
            "x": 150,
            "y": 300
        },
        {
            "a": "m",               // 移动事件
            "t": 1050,
            "x": 200,
            "y": 350
        },
        {
            "a": "u",               // 抬起事件
            "t": 1100,
            "x": 250,
            "y": 400
        }
    ]
}
```

#### 动作类型说明
- `c`: 点击事件（click）
- `d`: 按下事件（down）
- `m`: 移动事件（move）
- `u`: 抬起事件（up）


## 4. 重要说明

### 4.1 时间处理
- 所有时间戳均使用相对时间（相对于序列第一个事件的时间）
- 单位：毫秒
- 使用相对时间可避免绝对时间戳带来的同步问题
- 时间偏移处理：
  - 通过 metadata 中的 `offset.time` 参数控制时间偏移
  - 偏移范围：原时间的±百分比（percent参数）
  - 最小偏移时间：min_ms毫秒
  - 实际偏移值 = max(原时间 * percent/100, min_ms)
  - 在正负偏移范围内随机取值
  - 示例：
    ```json
    {
        "m": {
            "offset": {
                "time": {
                    "percent": 0.5,    // 偏移范围：原时间的±0.5%
                    "min_ms": 20       // 最小偏移20毫秒
                }
            }
        },
        "e": [
            {"a": "c", "t": 1000},   // 实际执行时间: 1000 ± max(5, 20)ms
            {"a": "c", "t": 100}     // 实际执行时间: 100 ± 20ms（因为0.5%小于最小值）
        ]
    }
    ```

### 4.2 坐标处理
- 所有坐标值基于WebView的实际尺寸
- 坐标原点为WebView左上角
- 坐标偏移处理：
  - 通过 metadata 中的 `offset.xy` 参数控制坐标偏移
  - 偏移范围：原坐标的±百分比（percent参数）
  - 最小偏移像素：min_px像素
  - 实际偏移值 = max(原坐标 * percent/100, min_px)
  - 在正负偏移范围内随机取值
  - 示例：
    ```json
    {
        "m": {
            "offset": {
                "xy": {
                    "percent": 0.1,    // 偏移范围：原坐标的±0.1%（1‰）
                    "min_px": 1        // 最小偏移1像素
                }
            }
        },
        "e": [
            {"a": "c", "x": 500, "y": 300},   // x: 500 ± max(0.5, 1)px, y: 300 ± max(0.3, 1)px
            {"a": "c", "x": 50, "y": 30}      // x: 50 ± 1px, y: 30 ± 1px（因为0.1%小于最小值）
        ]
    }
    ```

偏移设计的目的：
- 增加事件执行的随机性
- 避免机械式的固定位置点击
- 支持精确的小数比例（如1‰）
- 通过最小偏移值确保小范围操作也有随机性
- 通过百分比偏移确保大范围操作有更合理的随机范围
- 模拟更真实的人工操作特征



0 WSKService的启动后, 
1 通过scriptREPO获取ScriptPlan (ScriptPlan要缓存在 scriptREPO， 有效时间由MockConfig.expire决定)
2 根据val scripts: List<Script> = emptyList(),中的权重, 随机获取其中的script item， 调用 fun setScriptData(data: ScriptData) 设置到repo
3 如果 repo。scriptplan 与scriptdata的有效， 则在 webresourceservice里，调用launchWSKActivity
4 启动后 ，调用系统api， 设置一个定时任务，在SCRIPTPLAN.MOCKCONFIG.interval秒后， 重新走1 2 3 逻辑
5 WSKActivity无需涉及任何scriptrepo的方法， 也无需获取数据
6 WSKDelegate 从 repo获取数据, 并根据 mockconfig, 以及 scrptdata回放脚本即可
7 WSKDelegate 脚本执行开始, 调用 scriptrepo的reportExecutionStart,回传脚本开始执行事件 ， 你要新增接口， 参数你自己设计
8 WSKDelegate 脚本执行结束后, 调用 scriptrepo的reportExecutionResult,回传脚本执行情况
9 scriptRepo增加获取device_id的方案, 优先用gaid, 如果gaid获取异常或是失败(最多等待超时3000s), 则用 mediaDRMid，如果mediaDRMid获取失败，或是都生成类似0000-00这种，则生成自定义uuid， 生成后保存到sharepreferment里， 下次直接重新获取
