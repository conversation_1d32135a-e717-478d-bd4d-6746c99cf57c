# OverlayService分析

## 概述
OverlayService是Android WebView广告SDK中的一个关键服务组件，它的主要功能是在应用上层创建一个透明的覆盖层，用于显示网页内容进行自动化测试。

## 业务逻辑分析

### 主要成员变量
- `private WindowManager windowManager;` - 用于管理覆盖窗口
- `private View overlayView;` - 覆盖层视图
- `private WebView webView;` - 用于加载网页内容的WebView组件
- `private String testCode;` - 测试代码/ID，用于初始化WebView内容

### 启动时机
OverlayService的启动时机是通过TestWebSDK.startTesting()方法触发的。具体流程：
1. 当应用调用`TestWebSDK.getInstance().startTesting(activity, code)`方法时
2. TestWebSDK会创建一个Intent指向OverlayService
3. 根据Android版本调用startForegroundService或startService启动服务
4. 传递"code"参数给服务

### 销毁时机
OverlayService的销毁时机有以下几种情况：
1. 当应用调用`TestWebSDK.getInstance().stopTesting(context)`时，会显式停止服务
2. 当应用调用`TestWebSDK.getInstance().release(context)`时，会先调用stopTesting再释放资源
3. 当应用或系统由于资源原因销毁服务时

### 启动后的行为

#### onCreate()方法
服务创建时会：
1. 记录日志"OverlayService created"
2. 如果Android API级别≥26，创建前台通知渠道和通知
3. 调用startForeground然后马上stopForeground(true)来降低服务优先级但保持运行

#### onStartCommand()方法
当服务启动后：
1. 从Intent中获取"code"参数
2. 调用setupOverlay()方法设置覆盖层
3. 返回START_REDELIVER_INTENT标志，表示如果服务被系统杀死，系统将重新启动服务并传递最后一个Intent

#### setupOverlay()方法
该方法是服务的核心功能，执行以下操作：
1. 获取WindowManager服务
2. 创建适当的WindowManager.LayoutParams参数
3. 尝试加载overlay_webview布局，如果失败则使用activity_main布局
4. 如果无法加载任何布局，通知错误并返回
5. 设置overlayView背景为透明
6. 查找或创建WebView组件
7. 将overlayView添加到WindowManager中显示在屏幕上
8. 如果testCode有效，初始化WebView
9. 通知TestWebSDK，WebView已加载(notifyWebViewLoaded)
10. 如果过程中发生异常，则尝试启动WebViewActivity作为备选方案

#### initializeWebView()方法
负责初始化WebView内容：
1. 创建WebViewHelper实例
2. 调用helper.getData(code)加载测试内容

#### onDestroy()方法
服务销毁时：
1. 记录日志"OverlayService正在销毁"
2. 清理视图资源：
   - 从WindowManager移除overlayView
   - 停止WebView加载并清理WebView资源
   - 销毁WebView
3. 通知TestWebSDK服务已停止(notifyServiceStopped)
4. 调用父类的onDestroy()方法

## 备选方案机制
当setupOverlay()方法失败时，OverlayService会调用launchWebViewActivity()方法作为备选方案：
1. 创建指向WebViewActivity的Intent
2. 添加各种标志使Activity正确显示
3. 传递testCode和dialog_mode=true参数
4. 启动Activity
5. 通知WebView已加载

### WebViewActivity的透明度与可见性
WebViewActivity是作为OverlayService的备选方案，但与OverlayService不同的是，它以Activity的形式显示WebView。具体特性如下：

1. **透明度设置**：
   - WebViewActivity使用了主题"Theme.TransparentDialog"（在values.xml中定义）
   - 该主题具有以下关键属性：
     - `android:windowIsTranslucent="true"` - 窗口是半透明的
     - `android:windowBackground="@android:color/transparent"` - 窗口背景是完全透明的
     - `android:backgroundDimEnabled="false"` - 禁用背景暗化效果
     - `android:windowFullscreen="true"` - 全屏显示
     - `android:windowIsFloating="false"` - 不是浮动窗口

2. **布局特性**：
   - 使用了activity_webview.xml布局，包含一个全屏的WebView
   - 在onCreate()方法中设置背景透明：`window.setBackgroundDrawableResource(17170445)`
   - WebView背景色被设置为透明：`this.webView.setBackgroundColor(0)`

3. **启动方式**：
   - 通过Intent启动，添加了以下标志：
     - `FLAG_ACTIVITY_NEW_TASK (0x10000000)`
     - `FLAG_ACTIVITY_CLEAR_TOP (0x4000000)`
     - `FLAG_ACTIVITY_SINGLE_TOP (65536)`
   - 这些标志确保活动以适当的方式在任务栈中启动

### 对主Activity的影响
虽然WebViewActivity是透明的，但作为一个完整的Activity，它会对用户与主Activity的交互产生以下影响：

1. **输入拦截**：
   - WebViewActivity会接收所有的触摸事件，通过设置了OnTouchListener，但不消费这些事件（返回false）
   - 这意味着虽然它理论上允许触摸事件传递到底层Activity，但由于它是一个完整的Activity，它实际上会阻止底层Activity接收触摸事件

2. **界面交互**：
   - 当WebViewActivity处于活动状态时，它成为前台Activity
   - 用户无法直接与主Activity交互，因为WebViewActivity会截获所有用户输入
   - 即使WebViewActivity是透明的，它仍然是一个独立的活动，会影响用户与主Activity的交互

3. **视觉影响**：
   - 虽然WebViewActivity的背景是透明的，但其中的WebView内容是可见的
   - 这意味着用户可以看到底层Activity的UI，但无法与之交互，直到WebViewActivity被关闭

4. **销毁机制**：
   - WebViewActivity可以通过多种方式被关闭：
     - 通过发送广播"com.android.mobile.testweb.CLOSE_WEBVIEW"
     - 通过发送带有"com.android.mobile.testweb.ACTION_CLOSE"操作的Intent
     - 当TestWebSDK.stopTesting()被调用时

总的来说，当主要机制（OverlayService）失败时，WebViewActivity作为备选方案提供了一种替代方式来显示WebView，但它会完全阻断用户与主Activity的交互，直到测试完成或手动停止测试。

### 实现WebViewActivity点击事件穿透
为了允许用户通过WebViewActivity与底层主Activity进行交互，可以实现触摸事件穿透，以下是几种可行的解决方案：

1. **修改主题设置**：
   在values.xml中修改Theme.TransparentDialog主题，添加触摸穿透相关属性：
   ```xml
   <style name="Theme.TransparentDialog" parent="Theme.AppCompat.NoActionBar">
       <!-- 保留现有属性 -->
       <item name="android:windowIsTranslucent">true</item>
       <item name="android:windowBackground">@android:color/transparent</item>
       <item name="android:backgroundDimEnabled">false</item>
       <item name="android:windowFullscreen">true</item>
       
       <!-- 添加触摸穿透相关属性 -->
       <item name="android:windowNoTouchModal">true</item>  <!-- 关键属性 -->
       <item name="android:windowNotTouchable">true</item>  <!-- 关键属性 -->
   </style>
   ```

2. **修改窗口标志**：
   在WebViewActivity的onCreate方法中添加以下代码：
   ```java
   @Override
   protected void onCreate(Bundle savedInstanceState) {
       super.onCreate(savedInstanceState);
       // 现有初始化代码...
       
       // 设置窗口不接收触摸事件，让事件穿透到底层Activity
       Window window = getWindow();
       if (window != null) {
           window.setFlags(
               WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE,
               WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE
           );
       }
   }
   ```

3. **选择性事件拦截**：
   如果需要WebView的部分区域可交互，其他区域事件穿透，可以重写dispatchTouchEvent方法：
   ```java
   @Override
   public boolean dispatchTouchEvent(MotionEvent ev) {
       // 定义WebView中需要处理触摸事件的区域
       Rect interactiveArea = new Rect();
       boolean shouldIntercept = false;
       
       // 根据业务逻辑确定是否拦截
       if (webView != null) {
           webView.getGlobalVisibleRect(interactiveArea);
           int x = (int) ev.getRawX();
           int y = (int) ev.getRawY();
           
           // 判断触摸点是否在交互区域
           shouldIntercept = interactiveArea.contains(x, y) && isWebViewInteractive();
       }
       
       if (shouldIntercept) {
           // 在指定区域内，由当前Activity处理
           return super.dispatchTouchEvent(ev);
       } else {
           // 在非交互区域，事件穿透到底层Activity
           return false;
       }
   }
   
   private boolean isWebViewInteractive() {
       // 根据实际业务需求确定WebView是否需要交互
       return false; // 默认不交互，完全穿透
   }
   ```

4. **使用自定义WebView**：
   创建自定义WebView类，控制触摸事件的处理：
   ```java
   public class TouchThroughWebView extends WebView {
       private boolean allowTouch = false;
       
       // 构造函数...
       
       public void setAllowTouch(boolean allow) {
           this.allowTouch = allow;
       }
       
       @Override
       public boolean onTouchEvent(MotionEvent event) {
           if (allowTouch) {
               return super.onTouchEvent(event);
           }
           // 不处理触摸事件，允许穿透
           return false;
       }
   }
   ```

5. **结合FLAG_NOT_TOUCH_MODAL标志**：
   如果希望触摸事件仅在WebView区域外穿透：
   ```java
   Window window = getWindow();
   if (window != null) {
       WindowManager.LayoutParams params = window.getAttributes();
       params.flags |= WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL;
       window.setAttributes(params);
   }
   ```
   
实施注意事项：
- 方案1和2适用于完全触摸穿透的场景，WebViewActivity将完全不接收触摸事件
- 方案3和4适用于需要部分区域交互、部分区域穿透的场景
- 方案5是一种折中方案，仅WebView覆盖区域接收触摸事件，其他区域穿透
- 在Android不同版本上，部分标志可能效果不同，建议在多个版本上测试

在实现触摸穿透的同时，应考虑用户体验的连贯性。当WebViewActivity完全不接收触摸事件时，可能需要提供其他方式让用户意识到WebViewActivity的存在，例如添加视觉指示器或提供关闭按钮。

## 与TestWebSDK的交互
OverlayService通过TestWebSDK的几个方法与外界通信：
1. notifyWebViewLoaded() - 通知WebView已成功加载
2. notifyError() - 通知发生错误
3. notifyServiceStopped() - 通知服务已停止
