请你按以下需求, 修改现有的代码

1 ScriptPlan.kt 对应的是以下数据结构
{
    "scripts": [
        {
            "url": "https://example.com",    // 测试网址
            "weight": 1.0,                   // 权重值，数值类型
            "id": "script_001"               // 测试脚本ID
        }
    ],
    "mock_config": {                        //模拟真实用户的配置
        "uas": [{ua:"Mozilla/5.0...","weight": 1.0},                   // 权重值，数值类型], // User Agent字符串
        "delay": 5,                         // 进入网页后启动延时(秒)
        "interval": 60,                     // 测试用例执行间隔(秒)
        "expire": 3600,                     // 配置过期时间(秒)，默认1小时
        "jitter": {                         // 模拟真实用户的抖动配置
            "xy": {
                "percent": 0.1,             // 坐标抖动范围(±0.1%)
                "min_px": 1                 // 最小抖动像素
            },
            "time": {
                "percent": 0.5,             // 时间抖动范围(±0.5%)
                "min_ms": 20                // 最小抖动毫秒
            }
        },
        "debug": false                      // 调试模式开关
    }
}

2 ScriptData.kt 对应的是以下数据结构
{
    "m": {                                  // 元数据对象
        "url":"录制脚本时,用的url",
        "w": 1080,                         // webview宽度(px)
        "h": 2340,                         // 设备屏幕高度(px)
        "id": "script_001",                // 脚本ID
        "version": "1.0",                  // 脚本版本
        "timeout": 300                     // 脚本整体执行超时时间(s) ，默认5分钟
    },
    "e": [                                 // 事件数组
        {
            "a": "c",                      // 动作类型：点击(click)
            "t": 0,                        // 相对时间(ms)
            "x": 100,                      // X坐标
            "y": 200                       // Y坐标
        },
        {
            "a": "d",                      // 动作类型：按下(down)
            "t": 1000,                     // 相对时间(ms)
            "x": 150,                      // X坐标
            "y": 300                       // Y坐标
        },
        {
            "a": "m",                      // 动作类型：移动(move)
            "t": 1050,                     // 相对时间(ms)
            "x": 200,                      // X坐标
            "y": 350                       // Y坐标
        },
        {
            "a": "u",                      // 动作类型：抬起(up)
            "t": 1100,                     // 相对时间(ms)
            "x": 250,                      // X坐标
            "y": 400                       // Y坐标
        }
    ]


3 请你按以上json结构, 重新整理scripData中与scriptPlan
4 ScriptRepo.kt 新增获取 scriptPan的接口 , 以及根据 scriptID获取 scrptData的接口
5 ScriptRepo里面的返回值， 你都写死mock数据
6 WSKActionRecordActivity.kt 里， 录制事件后，生成的json 要与scriptdata的结构一致
7 WSKActivity 启动后， 通过srpiptRepo获取 scriptPlan,
8 WSKDelegate回放脚本时， 需要传入scriptPlan即可， 然后再通过ScriptRepo获取Scriptdata， 你也需要修改回放脚本的代码



详细需求文档如下

# 网页测试用例系统需求文档

## 1. 接口规范

### 1.1 获取测试用例列表接口

#### 1.1.1 基础信息

- **接口路径**：`/api/v1/test-cases`
- **请求方式**：POST
- **接口说明**：获取待执行的测试用例配置列表

#### 1.1.2 请求参数

| 参数名     | 类型   | 必填 | 描述           | 来源                 | 示例值      |
|------------|--------|------|----------------|----------------------|-------------|
| ad_app_id  | string | 是   | 应用标识符     | -                    | "app_123"   |
| osVersion  | string | 是   | 操作系统版本   | Build.VERSION.RELEASE | "12.0"      |
| brand      | string | 是   | 设备品牌       | Build.BRAND          | "samsung"   |
| model      | string | 是   | 设备型号       | Build.MODEL          | "SM-G9980"  |
| deviceId   | string | 是   | 设备唯一标识符 | -                    | "device_xyz"|

#### 1.1.3 响应数据结构

```json
{
    "scripts": [
        {
            "url": "https://example.com",    // 测试网址
            "weight": 1.0,                   // 权重值，数值类型
            "id": "script_001"               // 测试脚本ID
        }
    ],
    "mock_config": {                        //模拟真实用户的配置
        "ua": "Mozilla/5.0...",             // User Agent字符串
        "delay": 5,                         // 进入网页后启动延时(秒)
        "interval": 60,                     // 测试用例执行间隔(秒)
        "expire": 3600,                     // 配置过期时间(秒)，默认1小时
        "jitter": {                         // 模拟真实用户的抖动配置
            "xy": {
                "percent": 0.1,             // 坐标抖动范围(±0.1%)
                "min_px": 1                 // 最小抖动像素
            },
            "time": {
                "percent": 0.5,             // 时间抖动范围(±0.5%)
                "min_ms": 20                // 最小抖动毫秒
            }
        },
        "debug": false                      // 调试模式开关
    }
}
```

### 1.2 获取测试脚本接口

#### 1.2.1 基础信息

- **接口路径**：`/api/v1/test-scripts/{id}`
- **请求方式**：GET
- **接口说明**：根据脚本ID获取详细的测试脚本数据

#### 1.2.2 请求参数

| 参数名 | 类型   | 必填 | 描述        | 示例值      |
|--------|--------|------|-------------|-------------|
| id     | string | 是   | 测试脚本ID  | "script_001"|

#### 1.2.3 响应数据结构

```json
{
    "m": {                                  // 元数据对象
        "url":"录制脚本时,用的url",
        "w": 1080,                         // webview宽度(px)
        "h": 2340,                         // 设备屏幕高度(px)
        "id": "script_001",                // 脚本ID
        "version": "1.0",                  // 脚本版本
        "timeout": 300                     // 脚本整体执行超时时间(s) ，默认5分钟
    },
    "e": [                                 // 事件数组
        {
            "a": "c",                      // 动作类型：点击(click)
            "t": 0,                        // 相对时间(ms)
            "x": 100,                      // X坐标
            "y": 200                       // Y坐标
        },
        {
            "a": "d",                      // 动作类型：按下(down)
            "t": 1000,                     // 相对时间(ms)
            "x": 150,                      // X坐标
            "y": 300                       // Y坐标
        },
        {
            "a": "m",                      // 动作类型：移动(move)
            "t": 1050,                     // 相对时间(ms)
            "x": 200,                      // X坐标
            "y": 350                       // Y坐标
        },
        {
            "a": "u",                      // 动作类型：抬起(up)
            "t": 1100,                     // 相对时间(ms)
            "x": 250,                      // X坐标
            "y": 400                       // Y坐标
        }
    ]
}
```

## 2. 事件类型说明

### 2.1 支持的事件类型

| 事件代码 | 事件名称 | 说明     |
|----------|----------|----------|
| c        | click    | 点击事件 |
| d        | down     | 按下事件 |
| m        | move     | 移动事件 |
| u        | up       | 抬起事件 |

### 2.2 事件参数说明

| 参数 | 类型   | 说明               |
|------|--------|-------------------|
| a    | string | 事件类型代码       |
| t    | number | 相对起始时间的毫秒数|
| x    | number | 横向坐标(像素)     |
| y    | number | 纵向坐标(像素)     |


## 3. 数据埋点回报

### 3.1 测试执行数据回报接口

#### 3.1.1 基础信息

- **接口路径**：`/api/v1/report/execution`
- **请求方式**：POST
- **接口说明**：上报测试脚本执行结果数据

#### 3.1.2 请求参数

| 参数名      | 类型   | 必填 | 描述                | 来源     | 示例值      |
|-------------|--------|------|---------------------|----------|-------------|
| deviceId    | string | 是   | 设备唯一标识符      | -        | "device_xyz"|
| script_id   | string | 是   | 测试脚本ID          | -        | "script_001"|
| status      | string | 是   | 执行状态            | -        | "success"   |
| timestamp   | number | 是   | 上报时间戳(毫秒)    | 系统时间 | 1634567890123|

#### 3.1.3 请求数据结构

```json
{
    "deviceId": "device_xyz",               // 设备唯一标识符(与获取脚本接口的deviceId一致)
    "script_id": "script_001",              // 测试脚本ID
    "status": "success",                    // 执行状态: "success" 或 "failure"
    "duration": 15000,                      // 脚本执行持续时间(毫秒)
    "url": "https://example.com",           // 测试的URL
    "failure_info": {                       // 失败信息(仅status为failure时需要)
        "reason": "network_error",          // 失败原因
        "message": "无法连接到服务器",       // 失败详细信息
    }
}
```

#### 3.1.4 响应数据结构, 不返回

```json
{
}
```

### 3.2 失败原因类型

| 失败原因类型      | 描述                          |
|-------------------|-------------------------------|
| page_load_failure | 页面加载失败                  |
| network_error     | 网络异常                      |
| timeout           | 操作或加载超时                |
| script_error      | 脚本执行错误                  |
| unknown           | 未知错误                      |



# 后台URL与脚本配置优化方案

当前问题描述：需要将一个URL关联到多个脚本，每个脚本有不同权重。

## 方案优化：

### 1. 配置元数据结构
```json
{
  "url": "https://www.baidu.com/",
  "total_weight": 4,
  "scripts": [
    {"id": "script_1", "weight": 1},
    {"id": "script_2", "weight": 1},
    {"id": "script_3", "weight": 2}
  ]
}
```

### 2. 生成配置文件时自动分解为：
```json
[
  {"url": "https://www.baidu.com/", "weight": 1, "id": "script_1"},
  {"url": "https://www.baidu.com/", "weight": 1, "id": "script_2"},
  {"url": "https://www.baidu.com/", "weight": 2, "id": "script_3"}
]
```

这种结构设计可以：
- 在配置界面直观显示URL与多个脚本的关系
- 保持权重分配的灵活性
- 自动生成符合现有系统要求的扁平化配置
