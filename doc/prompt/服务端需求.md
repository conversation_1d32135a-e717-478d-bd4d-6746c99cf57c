# 服务端需求规范

## 1. 测试设备管理
### 功能概述
- 维护测试设备白名单列表
- 当请求来自白名单设备时，接口返回调试标识

### 配置方式
- 通过管理后台配置 `device_id` 数组
- 设备ID存储要求：
  - 唯一性校验

### 接口影响
当请求头 `X-Device-ID` 匹配白名单时，`/api/v1/test-cases` 响应体包含：
```json
{
  "debug": true
}
```

## 2. 脚本管理
### 核心要素
| 字段       | 类型   | 说明                          | 示例值                  |
|-----------|--------|-----------------------------|------------------------|
| script_id | string | 系统生成的唯一标识（UUIDv4）   | "550e8400-e29b-41d4..." |
| name      | string | 脚本显示名称（支持中英文）      | "广告拦截v1.2"         |
| raw_data  | json   | 原始脚本源代码（不可修改）      | 录制的事件 |
录制事件如下:
{
    "m": {                                  // 元数据对象
        "url":"录制脚本时,用的url",
        "w": 1080,                         // webview宽度(px)
        "h": 2340,                         // 设备屏幕高度(px)
        "id": "script_001",                // 脚本ID 服务端动态生成后, 把整份json放到cdn
        "version": "1.0",                  // 脚本版本
        "timeout": 300                     // 脚本整体执行超时时间(s) ，默认5分钟
    },
    "e": [                                 // 事件数组
        {
            "a": "c",                      // 动作类型：点击(click)
            "t": 0,                        // 相对时间(ms)
            "x": 100,                      // X坐标
            "y": 200                       // Y坐标
        },
        {
            "a": "d",                      // 动作类型：按下(down)
            "t": 1000,                     // 相对时间(ms)
            "x": 150,                      // X坐标
            "y": 300                       // Y坐标
        },
        {
            "a": "m",                      // 动作类型：移动(move)
            "t": 1050,                     // 相对时间(ms)
            "x": 200,                      // X坐标
            "y": 350                       // Y坐标
        },
        {
            "a": "u",                      // 动作类型：抬起(up)
            "t": 1100,                     // 相对时间(ms)
            "x": 250,                      // X坐标
            "y": 400                       // Y坐标
        }
    ]


## 3. 测试计划生成
### 配置参数
```json
{
  "test_cases": [
    {
      "url": "被测页面地址",
      "script_id": "关联脚本ID",
      "weight": 0.35
    }
  ]
}
```
