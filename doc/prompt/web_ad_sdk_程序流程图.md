# lib_webview_ad_sdk 程序流程图

## SDK 整体流程

```mermaid
flowchart TD
    Start[开始] --> Init[初始化 WSKSDK]
    Init --> Attach[调用 attach 方法]
    Attach --> StartService[启动 WSKService]
    StartService --> UpdatePlan[更新脚本计划]
    UpdatePlan --> GetScript[获取脚本数据]
    GetScript --> DisplayDecision{选择显示方案}
    DisplayDecision -->|Android 11以下| TransparentActivity[透明 Activity 方案]
    DisplayDecision -->|Android 11及以上| OverlayWindow[悬浮窗方案]
    TransparentActivity --> ExecuteScript[执行脚本]
    OverlayWindow --> ExecuteScript
    ExecuteScript --> ReportResult[上报执行结果]
    ReportResult --> ScheduleNext[设置下次执行定时任务]
    ScheduleNext --> End[结束]
```

## 初始化和启动流程

```mermaid
flowchart TD
    Start[开始] --> InitSDK[初始化 WSKSDK]
    InitSDK --> SetCallback[设置回调接口 TestWebCallback]
    SetCallback --> AttachSDK[调用 attach 方法]
    AttachSDK --> CreateIntent[创建 WSKService Intent]
    CreateIntent --> StartService[启动 WSKService]
    StartService --> NotifyStarted[通知 SDK 已启动]
    NotifyStarted --> End[结束]
```

## 脚本获取流程

```mermaid
flowchart TD
    Start[开始] --> CheckCache{检查缓存的脚本计划}
    CheckCache -->|有效| LoadCache[加载缓存的脚本计划]
    CheckCache -->|无效或过期| FetchRemote[从远程获取脚本计划]
    FetchRemote --> SaveCache[保存脚本计划到缓存]
    LoadCache --> SelectScript[随机选择脚本]
    SaveCache --> SelectScript
    SelectScript --> FetchScriptData[获取脚本数据]
    FetchScriptData --> DecryptData[解密脚本数据]
    DecryptData --> SaveData[保存脚本数据]
    SaveData --> End[结束]
```

## 显示方案选择流程

```mermaid
flowchart TD
    Start[开始] --> CheckVersion{检查 Android 版本}
    CheckVersion -->|Android 11以下| SupportOutApp[支持应用外显示]
    CheckVersion -->|Android 11及以上| NotSupportOutApp[不支持应用外显示]
    SupportOutApp --> TryOverlay{尝试创建悬浮窗}
    NotSupportOutApp --> TryOverlay
    TryOverlay -->|成功| UseOverlay[使用悬浮窗方案]
    TryOverlay -->|失败| UseActivity[使用透明 Activity 方案]
    UseOverlay --> ConfigureWebView[配置 WebView]
    UseActivity --> LaunchActivity[启动 WSKActivity]
    LaunchActivity --> InitWebView[初始化 WebView]
    ConfigureWebView --> End[结束]
    InitWebView --> End
```

## 脚本执行流程

```mermaid
flowchart TD
    Start[开始] --> GetScriptData[获取脚本数据]
    GetScriptData --> InitWebView[初始化 WebView]
    InitWebView --> SetupWebView[设置 WebView 参数]
    SetupWebView --> LoadURL[加载目标 URL]
    LoadURL --> WaitForLoad[等待页面加载完成]
    WaitForLoad --> ReportStart[上报执行开始]
    ReportStart --> CheckEvents{检查事件列表}
    CheckEvents -->|有事件| ExecuteEvents[执行事件序列]
    CheckEvents -->|无事件| ReportSuccess[上报执行成功]
    ExecuteEvents --> ProcessEvents[处理每个事件]
    ProcessEvents --> ApplyJitter[应用随机偏移]
    ApplyJitter --> PerformAction[执行动作]
    PerformAction --> CheckComplete{是否完成所有事件}
    CheckComplete -->|否| ProcessEvents
    CheckComplete -->|是| ReportSuccess
    ReportSuccess --> NotifyComplete[通知脚本执行完成]
    NotifyComplete --> End[结束]
```

## 事件执行流程

```mermaid
flowchart TD
    Start[开始] --> GetEvent[获取事件]
    GetEvent --> CheckAction{检查事件类型}
    CheckAction -->|点击 c| PerformClick[执行点击]
    CheckAction -->|按下 d| PerformDown[执行按下]
    CheckAction -->|移动 m| PerformMove[执行移动]
    CheckAction -->|抬起 u| PerformUp[执行抬起]
    PerformClick --> ApplyCoordinates[应用坐标偏移和缩放]
    PerformDown --> ApplyCoordinates
    PerformMove --> ApplyCoordinates
    PerformUp --> ApplyCoordinates
    ApplyCoordinates --> CalculateDelay[计算时间延迟]
    CalculateDelay --> ExecuteAction[执行动作]
    ExecuteAction --> End[结束]
```

## 上报事件流程

```mermaid
flowchart TD
    Start[开始] --> PrepareData[准备上报数据]
    PrepareData --> CheckEventType{检查事件类型}
    CheckEventType -->|执行开始| ReportStart[上报执行开始]
    CheckEventType -->|执行结果| ReportResult[上报执行结果]
    CheckEventType -->|完成状态| ReportCompletion[上报完成状态]
    CheckEventType -->|其他事件| ReportGeneric[上报通用事件]
    ReportStart --> CreateProps[创建事件属性]
    ReportResult --> CreateProps
    ReportCompletion --> CreateProps
    ReportGeneric --> CreateProps
    CreateProps --> SendReport[发送上报请求]
    SendReport --> LogResult[记录上报结果]
    LogResult --> End[结束]
```

## 定时任务流程

```mermaid
flowchart TD
    Start[开始] --> ScheduleJob[设置 JobScheduler 任务]
    ScheduleJob --> WaitForTrigger[等待触发时间]
    WaitForTrigger --> JobServiceStart[JobService 开始执行]
    JobServiceStart --> StartService[启动 WSKService]
    StartService --> UpdatePlan[更新脚本计划]
    UpdatePlan --> ExecuteScript[执行脚本]
    ExecuteScript --> CalculateNextInterval[计算下次执行间隔]
    CalculateNextInterval --> ScheduleNextJob[设置下次执行任务]
    ScheduleNextJob --> End[结束]
```

## 透明 Activity 方案流程

```mermaid
flowchart TD
    Start[开始] --> CreateActivity[创建 WSKActivity]
    CreateActivity --> SetTransparent[设置透明背景]
    SetTransparent --> AllowClickThrough[允许点击穿透]
    AllowClickThrough --> InitWebView[初始化 WebView]
    InitWebView --> RegisterReceiver[注册关闭广播接收器]
    RegisterReceiver --> ExecuteScript[执行脚本]
    ExecuteScript --> WaitForCompletion[等待执行完成]
    WaitForCompletion --> NotifyComplete[通知执行完成]
    NotifyComplete --> CloseActivity[关闭 Activity]
    CloseActivity --> End[结束]
```

## 悬浮窗方案流程

```mermaid
flowchart TD
    Start[开始] --> CheckPermission{检查悬浮窗权限}
    CheckPermission -->|有权限| CreateOverlay[创建悬浮窗]
    CheckPermission -->|无权限| FallbackToActivity[降级到透明 Activity]
    CreateOverlay --> ConfigureWebView[配置 WebView]
    ConfigureWebView --> AddToWindow[添加到窗口管理器]
    AddToWindow --> ExecuteScript[执行脚本]
    ExecuteScript --> WaitForCompletion[等待执行完成]
    WaitForCompletion --> NotifyComplete[通知执行完成]
    NotifyComplete --> RemoveOverlay[移除悬浮窗]
    RemoveOverlay --> End[结束]
    FallbackToActivity --> End
```