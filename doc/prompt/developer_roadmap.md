# 开发路线图

## 项目进度表

| 编号 | 任务 | 人员 | 排期 | 状态 |
|------|------|------|------|------|
| 1 | 脚本数据结构定义 | 双端 | 2023/3/19-2023/3/26 | ✅ 已完成 |
| 2 | 透明Activity实现，不阻断用户交互 | 客户端 | 2023/3/19-2023/3/26 | ✅ 已完成 |
| 3 | 脚本录制工具 | 客户端 | 2023/3/26-2023/4/2 | ✅ 已完成 |
| 4 | 客户端模拟真人静默回放脚本 | 客户端 | 2023/3/26-2023/4/2 | ✅ 已完成 |
| 5 | 后台合规保活,SDK安卓系统版本适配 | 客户端 | 2023/3/26-2023/4/9 | ✅ 已完成 适配 安卓 7~16|
| 6 | 客户端服务端接口定义 | 双端 | 2023/4/2-2023/4/9 | ✅ 已完成 |
| 7 | 客户端混淆防止逆向 | 客户端 | 2023/4/2-2023/4/12 | ✅ 已完成 |
| 8 | 实现远程脚本获取测试计划 (urls + config) | 服务端 | ✅ 已完成 |
| 9 | 实现远程脚本获取(scripts) | 服务端 | 2023/4/12-2023/4/17 | ✅ 已完成 |
| 10 | 脚本执行结果统计上报 | 双端 | 2023/4/14-2023/4/18 | ✅ 已完成 |
| 11 | 客户端与服务端接口联调 | 双端 | 2023/4/9-2023/4/18 | ✅ 已完成 |
| 12 | web端测试设备管理 | 服务端 | 2023/4/2-2023/4/25 | ✅ 已完成 |
| 13 | web端网页自动化脚本管理 | 服务端 | 2023/4/20-2023/5/1 | ✅ 已完成 |
| 14 | web端网页测试计划管理 | 服务端 | 2023/5/1-2023/5/8 | ✅ 已完成 |
| 15 | 静默执行透明activity时，返回按键和顶部通知栏失灵的问题解决 | 客户端 | 2023/5/1-2023/5/6 |✅ 已完成 |
| 16 | 数据分析报表 | 服务端 | 2023/4/25-2023/5/1 | ✅ 已完成 |
| 17 | 多机型、多系统测试 | 双端 | 2023/4/25-2023/5/1 | ✅ 已完成 |
| **M1** | **原型版本验证** | **双端** | **2023/5/1** | **🎯 里程碑** |
| 18 | 热更新支持,以及迭代优化原型版本 | 双端 | 2023/5/01-2023/5/18 | ⏳ 待开发 |
| **M2** | **大范围验证** | **双端** | **2023/5/19** | **🎯 里程碑** |

## 版本规划

### 原型版本验证（2023/4/19）
需完成以下核心功能：
- 基础SDK功能实现（透明Activity、脚本录制、回放）
- 基础服务端接口
- 客户端混淆保护
- 远程脚本获取测试计划功能
- 远程脚本获取功能
- 脚本执行结果统计上报
- 接口联调

### 大范围验证（2023/5/19）
在原型版本基础上完成：
- 完整的web管理系统（设备、脚本、测试计划）
- 关键技术难点攻克
- 数据分析报表
- 多机型、多系统测试

## 图例说明
- ✅ 已完成：任务已经完成
- 🔄 开发中：任务正在开发过程中
- ⏳ 待开发：任务尚未开始
- ⚠️ 难点待解决：任务存在技术难点，需要重点攻克
- 🎯 里程碑：项目关键节点
