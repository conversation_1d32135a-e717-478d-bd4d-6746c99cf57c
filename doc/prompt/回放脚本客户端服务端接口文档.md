# 回放脚本客户端服务端接口文档

## 1. 接口概述

该文档描述了Android WebView广告SDK中回放脚本的客户端与服务端之间的接口交互。

## 2. 请求接口

### 2.1 获取测试数据

#### 请求信息
- **请求方式**: POST
- **请求参数**:
  ```
  code: String          // 测试代码
  osVersion: String     // 系统版本(Build.VERSION.RELEASE)
  brand: String         // 设备品牌(Build.BRAND)
  model: String         // 设备型号(Build.MODEL)
  deviceId: String      // 设备ID(Settings.Secure.ANDROID_ID)
  ```

#### 响应数据
服务端返回加密的JSON数据，解密后的数据结构如下：

```json
{
  "data": {
    "recordId": Number,     // 记录ID
    "status": Number,       // 状态码，1表示成功
    "script": String,       // 回放脚本JSON字符串
    "url": String          // 目标网页URL
  }
}
```

#### 脚本数据结构
script字段解析后的JSON结构：
```json
{
  "ua": String,            // User-Agent字符串
  "metadata": {
    "mute": Boolean        // 是否静音
  },
  "events": [              // 事件数组
    {
      "eventType": String, // 事件类型(click/swipe)
      "timestamp": Number, // 点击事件时间戳
      "startTime": Number, // 滑动事件开始时间戳
      "x": Number,         // 点击坐标X
      "y": Number,         // 点击坐标Y
      "startX": Number,    // 滑动起始X坐标
      "startY": Number,    // 滑动起始Y坐标
      "endX": Number,      // 滑动结束X坐标
      "endY": Number,      // 滑动结束Y坐标
      "points": [          // 滑动路径点
        {
          "x": Number,     // 路径点X坐标
          "y": Number,     // 路径点Y坐标
          "t": Number      // 路径点时间戳
        }
      ]
    }
  ]
}
```

## 3. 数据加密解密

### 3.1 解密算法
使用RC4+Base64+MD5的组合加密方案：

1. 密钥处理：
   - 原始密钥进行MD5得到32位字符串
   - 取前16位进行MD5得到keya
   - 取后16位进行MD5得到keyb
   - 从加密数据取前8位得到keyc
   - 最终密钥 = keya + MD5(keya + keyc)

2. 解密步骤：
   - 去除前8位密钥标识
   - Base64解码
   - 使用最终密钥进行RC4解密
   - 验证解密数据的完整性

### 3.2 关键参数
```java
密钥长度: 8字节
IV向量: "0ef7e31120b811e9f10740e018ad22dd"
加密密钥: "L7trZqD2XPGpMPvG"
```

## 4. 回放流程

1. 初始化WebView并加载目标URL
2. 根据脚本metadata设置WebView参数（如静音等）
3. 按时间顺序回放事件：
   - 点击事件：模拟触摸事件
   - 滑动事件：模拟连续触摸事件
4. 完成回放后通知服务端

## 5. 状态回调

SDK提供以下回调通知：
- onTestWebSDKStarted(): WebView加载完成
- onTestWebSDKCompleted(): 脚本执行完成
- onError(String errorMessage): 发生错误
