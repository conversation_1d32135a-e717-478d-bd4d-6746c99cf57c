# Android WebView Ad SDK 项目架构需求热更新文档

## 📋 项目概述

Android WebView Ad SDK 是一个支持热更新的广告SDK，采用模块化架构设计，通过动态加载DEX文件实现功能热更新。

## 🏗️ 项目架构

### 模块结构

```
android_webview_ad_sdk/
├── lib_webview_ad_sdk/          # SDK基础模块
├── lib_webview_ad_plugin/       # 插件业务模块（编译成DEX）
├── actionRecoder/               # 测试应用
├── hotUpgradeTester/           # 热更新测试工具
└── doc/                        # 文档目录
```

### 依赖关系

```mermaid
graph TD
    A[第三方应用] --> B[lib_webview_ad_sdk]
    C[lib_webview_ad_plugin] --> B
    C --> D[DEX文件 mmkv_core.so]
    A --> E[动态下载DEX文件]
    B --> F[WSKDexManager]
    B --> G[WSKUpdateManager]
    B --> H[WSKResManager]
```

## 🎯 核心需求

### 1. 模块职责分离

- **lib_webview_ad_sdk**：
  - 提供公开API接口
  - 管理DEX文件加载和版本控制
  - 处理热更新逻辑
  - 第三方应用直接集成此模块

- **lib_webview_ad_plugin**：
  - 实现具体业务逻辑
  - 包含加解密、网络请求等敏感功能
  - 编译成DEX文件（如 mmkv_core.so）
  - 通过热更新动态下载和加载

### 2. 热更新机制

- **启动时热更新**：应用启动后，先完成热更新检查，再执行业务逻辑
- **超时保护**：热更新最多等待10秒，超时则使用当前可用的DEX
- **DeviceId 异步处理**：确保 deviceId 获取完成后再发起热更新请求
- **版本管理**：自动管理DEX文件版本，清理旧版本文件
- **安全性**：所有DEX文件设置为只读，符合Android 7.0+安全要求

### 3. 核心业务逻辑

SDK的核心业务逻辑包括：
1. `_instance.initialize(application, appId, callback)`
2. `_instance.createInAppOverlayManager().initialize(application)`

这些逻辑必须在热更新完成后才能执行。

## 🔄 程序流程

### 初始化流程

```mermaid
sequenceDiagram
    participant App as 第三方应用
    participant SDK as WSKSDK
    participant UM as WSKUpdateManager
    participant DM as WSKDexManager
    participant RM as WSKResManager
    participant Plugin as ProxyWSKSDK

    App->>SDK: initialize(application, appId, callback)
    SDK->>UM: initializeWithUpdate()
    UM->>DM: init(application)
    DM->>DM: ensureDefaultDexExists()
    UM->>RM: createObject("default", "ProxyWSKSDK")
    RM->>DM: getCurrentDexPath()
    DM-->>RM: DEX文件路径
    RM->>Plugin: 加载并创建实例
    Plugin-->>UM: 临时实例
    UM->>Plugin: checkUpdate() (最多10秒)
    Plugin->>Plugin: 执行热更新检查
    Plugin-->>UM: 更新结果
    UM->>RM: createObject() (使用最新DEX)
    RM->>Plugin: 创建最终实例
    Plugin-->>UM: 最终实例
    UM-->>SDK: onUpdateComplete(instance, isUpdated)
    SDK->>SDK: executeBusinessLogic()
    SDK->>Plugin: initialize() + createInAppOverlayManager().initialize()
    Plugin-->>App: onWSKSDKStarted()
```

### 热更新详细流程

```mermaid
flowchart TD
    A[应用启动] --> B[WSKSDK.initialize()]
    B --> C[WSKUpdateManager.initializeWithUpdate()]
    C --> D[初始化WSKDexManager]
    D --> E[确保默认DEX存在]
    E --> F[创建临时实例]
    F --> G[检查DeviceId状态]
    G --> H{DeviceId是否可用}
    H -->|已可用| I[执行热更新检查]
    H -->|未可用| J[等待DeviceId获取]
    J --> K{等待结果}
    K -->|获取成功| I
    K -->|超时/失败| L[使用默认值继续]
    L --> I
    I --> M{热更新结果}
    M -->|有更新| N[下载新DEX文件]
    M -->|无更新| O[使用当前DEX]
    M -->|超时/失败| O
    N --> P[验证新DEX文件]
    P --> Q[更新DEX路径记录]
    Q --> R[创建最终实例]
    O --> R
    R --> S[执行核心业务逻辑]
    S --> T[SDK初始化完成]
```

## 📁 关键类说明

### WSKDexManager
- **位置**：`lib_webview_ad_sdk/src/main/java/ai/ad/webview/sdk/dex/WSKDexManager.java`
- **职责**：
  - 管理DEX文件路径和版本信息
  - 确保默认DEX文件存在
  - 提供当前可用的DEX文件路径
  - 清理旧版本文件

### WSKUpdateManager
- **位置**：`lib_webview_ad_sdk/src/main/java/ai/ad/webview/sdk/update/WSKUpdateManager.java`
- **职责**：
  - 封装热更新逻辑
  - 提供10秒超时保护
  - 管理热更新完成回调
  - 创建临时实例和最终实例

### WSKResManager
- **位置**：`lib_webview_ad_sdk/src/main/java/ai/ad/webview/sdk/dex/WSKResManager.java`
- **职责**：
  - 负责DEX文件加载
  - 管理ClassLoader缓存
  - 支持"default"标识自动获取DEX路径

### WSKResLoader
- **位置**：`lib_webview_ad_plugin/src/main/java/ai/ad/webview/plugin/dex/WSKResLoader.java`
- **职责**：
  - 执行网络请求检查更新
  - 下载和验证DEX文件
  - 处理加解密逻辑
  - 上报更新结果

## 🔧 技术特性

### 1. Android 7.0+兼容性
- 所有DEX文件存储在 `secure_dex` 目录
- 文件设置为只读权限
- 避免"Writable dex file is not allowed"错误

### 2. 线程安全
- 使用 `CountDownLatch` 和 `AtomicBoolean` 确保线程安全
- 主线程回调，避免UI线程问题

### 3. DeviceId 异步处理
- 热更新前确保 deviceId 已获取完成
- 最多等待4秒获取 deviceId（20次重试，每次200ms）
- 获取失败时使用默认值继续更新流程
- 避免因 deviceId 未准备导致的更新请求失败

### 4. 错误处理
- 超时处理：10秒后自动使用当前DEX
- 网络失败：继续使用当前DEX
- 文件损坏：自动清理并使用默认DEX

### 5. 版本管理
- 自动记录当前使用的DEX版本
- 支持版本回退
- 自动清理旧版本文件

## 📊 配置说明

### 热更新API
- **检查更新**：`POST /api/v1/update/check`
- **上报结果**：`POST /api/v1/update/report`

### 超时配置
```java
private static final int UPDATE_TIMEOUT_SECONDS = 10; // 10秒超时
```

### 文件路径
- **默认DEX**：`{filesDir}/secure_dex/mmkv_core.so`
- **热更新DEX**：`{filesDir}/secure_dex/mmkv_core_{version}.so`

## 🚀 使用示例

### 第三方应用集成
```java
// 在Application中初始化
WSKSDK.initialize(this, "YOUR_APP_ID", new IWSKCallback() {
    @Override
    public void onWSKSDKStarted() {
        // SDK初始化完成，可以使用SDK功能
        Log.d("SDK", "SDK started successfully");
    }

    @Override
    public void onWSKSDKCompleted() {
        // SDK业务逻辑执行完成
        Log.d("SDK", "SDK completed");
    }

    @Override
    public void onError(String error) {
        // 初始化失败
        Log.e("SDK", "SDK initialization failed: " + error);
    }
});

// 在Activity中使用
WSKSDK.attach(this);
```

### 热更新测试
```java
// 测试指定DEX文件
String result = WSKSDK.test("/path/to/test.dex");
Log.d("Test", result);
```

## 📈 性能优化

### 1. 启动优化
- 热更新在启动时同步执行，确保使用最新版本
- 10秒超时保证不会无限等待
- 失败时快速回退到可用版本

### 2. 内存优化
- ClassLoader缓存避免重复加载
- 及时清理旧版本文件
- 使用单例模式减少内存占用

### 3. 网络优化
- 支持断点续传
- MD5校验确保文件完整性
- 失败重试机制

## 🔒 安全考虑

### 1. 代码保护
- 敏感业务逻辑在DEX中，难以反编译
- 网络请求加解密在plugin层
- API密钥等敏感信息不暴露在SDK层

### 2. 文件安全
- DEX文件只读权限
- MD5校验防止文件篡改
- 安全目录存储

### 3. 运行时保护
- 异常捕获和处理
- 版本验证和回退
- 超时保护机制

## 📝 开发指南

### 1. 添加新功能
- 业务功能在 `lib_webview_ad_plugin` 中实现
- 公开接口在 `lib_webview_ad_sdk` 中定义
- 通过热更新发布新功能

### 2. 调试技巧
- 使用 `hotUpgradeTester` 测试热更新
- 查看日志确认DEX加载状态
- 使用 `WSKSDK.test()` 验证DEX文件

### 3. 发布流程
1. 修改 `lib_webview_ad_plugin` 代码
2. 编译生成新的DEX文件
3. 上传到服务器
4. 客户端自动检查和下载更新

## 🎯 未来规划

### 1. 功能增强
- 支持增量更新
- 多DEX文件管理
- 更细粒度的模块化

### 2. 性能优化
- 预加载机制
- 智能缓存策略
- 网络优化

### 3. 安全加固
- 代码混淆增强
- 反调试保护
- 运行时检测

## 📋 架构优势

### 1. 模块化设计
- **清晰的职责分离**：SDK层负责基础设施，Plugin层负责业务逻辑
- **独立开发**：两个模块可以独立开发和测试
- **灵活部署**：Plugin层可以通过热更新独立发布

### 2. 热更新机制
- **快速迭代**：无需发布新版本即可更新功能
- **风险控制**：支持版本回退和错误恢复
- **用户体验**：无感知更新，不影响用户使用

### 3. 安全性
- **代码保护**：核心业务逻辑在DEX中，难以逆向
- **运行时安全**：多层异常处理和超时保护
- **文件安全**：只读权限和MD5校验

## 🔧 技术实现细节

### 1. DEX文件管理
```java
// 文件命名规则
mmkv_core.so           // 默认DEX文件
mmkv_core_1.2.3.so     // 版本1.2.3的DEX文件
mmkv_core_1.2.4.so     // 版本1.2.4的DEX文件
```

### 2. 版本控制
```java
// SharedPreferences存储
"wsk_current_version"     // 当前使用的版本
"wsk_hot_update_version"  // 热更新的版本
"wsk_hot_update_path"     // 热更新DEX路径
```

### 3. 线程模型
```java
// 主线程：UI操作和回调
// 后台线程：网络请求和文件操作
// 超时控制：CountDownLatch + 定时器
```

### 4. DeviceId 异步处理机制
```java
// DeviceId 检查逻辑
private static void ensureDeviceIdReady(IWSKSDK instance, DeviceIdCallback callback) {
    String deviceId = instance.getDeviceId();
    if (deviceId != null && !deviceId.equals("unknown") && !deviceId.trim().isEmpty()) {
        callback.onDeviceIdReady(deviceId);
        return;
    }

    // 轮询等待 deviceId 准备完成
    final int maxRetries = 20;      // 最多重试20次
    final int retryInterval = 200;  // 每次间隔200ms
    // 总等待时间：20 * 200ms = 4秒
}
```

## 📊 监控和日志

### 1. 关键日志
- DEX文件加载状态
- 热更新检查结果
- 版本切换记录
- 异常和错误信息

### 2. 性能监控
- 初始化耗时
- 热更新耗时
- 内存使用情况
- 网络请求状态

### 3. 错误上报
- 自动上报更新失败
- 记录异常堆栈
- 统计成功率

## 🚀 部署和运维

### 1. 服务端配置
- 热更新API服务
- DEX文件存储和分发
- 版本管理和灰度发布

### 2. 客户端配置
- 更新频率控制
- 网络环境适配
- 存储空间管理

### 3. 监控告警
- 更新成功率监控
- 异常率告警
- 性能指标监控

---

## 📞 联系信息

如有问题或建议，请联系开发团队。

**文档版本**：v1.0
**最后更新**：2024年12月
**维护者**：Android WebView Ad SDK Team