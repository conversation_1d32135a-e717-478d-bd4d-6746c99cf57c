<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- GitHub风格对话框主题 -->
    <style name="AlertDialogTheme" parent="ThemeOverlay.AppCompat.Dialog.Alert">
        <item name="android:background">#161B22</item>
        <item name="android:textColorPrimary">#FFFFFF</item>
        <item name="android:textColor">#FFFFFF</item>
        <item name="android:textColorSecondary">#8B949E</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="colorAccent">#2F81F7</item>
        <item name="buttonBarNegativeButtonStyle">@style/GitHubDialogButtonStyle</item>
        <item name="buttonBarPositiveButtonStyle">@style/GitHubDialogButtonStyle</item>
        <item name="buttonBarNeutralButtonStyle">@style/GitHubDialogButtonStyle</item>
    </style>

    <!-- 列表对话框主题 - 深色背景浅色文字 -->
    <style name="ListDialogTheme" parent="ThemeOverlay.AppCompat.Dialog.Alert">
        <item name="android:background">#2D3239</item>
        <item name="android:textColorPrimary">#FFFFFF</item>
        <item name="android:textColor">#FFFFFF</item>
        <item name="android:textColorSecondary">#D0D7DE</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="colorAccent">#2F81F7</item>
        <item name="buttonBarNegativeButtonStyle">@style/GitHubDialogButtonStyle</item>
        <item name="buttonBarPositiveButtonStyle">@style/GitHubDialogButtonStyle</item>
        <item name="buttonBarNeutralButtonStyle">@style/GitHubDialogButtonStyle</item>
        <!-- 确保列表项文字颜色 -->
        <item name="android:textColorAlertDialogListItem">#FFFFFF</item>
    </style>

    <!-- GitHub风格按钮样式 -->
    <style name="GitHubDialogButtonStyle" parent="Widget.AppCompat.Button.ButtonBar.AlertDialog">
        <item name="android:textColor">#2F81F7</item>
        <item name="android:background">?attr/selectableItemBackground</item>
    </style>
</resources>