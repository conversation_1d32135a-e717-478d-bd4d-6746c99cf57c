<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <WebView
        android:id="@+id/webView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@+id/statusTextView"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/statusTextView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#80000000"
        android:padding="8dp"
        android:textColor="@android:color/white"
        android:textSize="14sp"
        app:layout_constraintBottom_toBottomOf="parent" />

    <androidx.cardview.widget.CardView
        android:id="@+id/urlInputCard"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:visibility="gone"
        app:cardBackgroundColor="#FFFFFF"
        app:cardCornerRadius="8dp"
        app:cardElevation="4dp"
        app:layout_constraintBottom_toTopOf="@+id/statusTextView"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/urlEditText"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="输入URL"
                android:inputType="textUri"
                android:maxLines="1" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:orientation="horizontal">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/urlConfirmButton"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="4dp"
                    android:layout_weight="1"
                    android:text="确认" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/refreshButton"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="4dp"
                    android:layout_weight="1"
                    android:text="刷新" />

            </LinearLayout>
        </LinearLayout>
    </androidx.cardview.widget.CardView>

</androidx.constraintlayout.widget.ConstraintLayout> 