<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/bg_dark">

    <!-- WebView 容器 -->
    <androidx.cardview.widget.CardView
        android:id="@+id/webViewContainer"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_margin="0dp"
        app:cardCornerRadius="0dp"
        app:cardElevation="0dp">

        <WebView
            android:id="@+id/webView"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

    </androidx.cardview.widget.CardView>

    <!-- 主菜单按钮 -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/mainMenuButton"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="16dp"
        android:contentDescription="主菜单"
        app:backgroundTint="@color/md_theme_primary"
        app:srcCompat="@drawable/ic_menu"
        app:tint="@color/white" />

    <!-- 左侧按钮组 -->
    <include
        android:id="@+id/leftSideButtons"
        layout="@layout/left_side_buttons"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|start" />


    <!-- 子菜单按钮 - 初始化SDK -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/initSdkButton"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="224dp"
        android:contentDescription="初始化SDK"
        android:visibility="gone"
        app:backgroundTint="@color/service_button"
        app:srcCompat="@drawable/ic_play"
        app:tint="@color/white" />

    <!-- 子菜单按钮 - 查看测试计划 -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/viewPlanButton"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="288dp"
        android:contentDescription="查看测试计划"
        android:visibility="gone"
        app:backgroundTint="#009688"
        app:srcCompat="@drawable/ic_list"
        app:tint="@color/white" />

    <!-- 子菜单按钮 - 查看选中脚本 -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/viewScriptButton"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="352dp"
        android:contentDescription="查看选中脚本"
        android:visibility="gone"
        app:backgroundTint="#FF9800"
        app:srcCompat="@drawable/ic_list"
        app:tint="@color/white" />

    <!-- 子菜单按钮 - 复制设备ID -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/deviceIdButton"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="416dp"
        android:contentDescription="复制设备ID"
        android:visibility="gone"
        app:backgroundTint="#3F51B5"
        app:srcCompat="@drawable/ic_content_copy"
        app:tint="@color/white" />

    <!-- 子菜单按钮 - 横竖屏切换 (移动到右侧菜单) -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/screenRotationButton"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="480dp"
        android:contentDescription="横竖屏切换"
        android:visibility="gone"
        app:backgroundTint="#9C27B0"
        app:srcCompat="@drawable/ic_screen_rotation"
        app:tint="@color/white" />

    <!-- 左侧按钮已移动到 left_side_buttons.xml 中 -->

    <!-- URL输入对话框 -->
    <androidx.cardview.widget.CardView
        android:id="@+id/urlInputCard"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="top"
        android:layout_margin="16dp"
        android:visibility="gone"
        app:cardBackgroundColor="@color/card_bg"
        app:cardCornerRadius="8dp"
        app:cardElevation="4dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="8dp">

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/urlInputLayout"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:boxBackgroundColor="#333333"
                app:boxStrokeColor="@color/white"
                app:endIconMode="clear_text"
                app:endIconTint="@color/white"
                app:hintTextColor="@color/white">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/urlEditText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="输入URL"
                    android:imeOptions="actionGo"
                    android:inputType="textUri"
                    android:paddingBottom="8dp"
                    android:paddingTop="8dp"
                    android:singleLine="true"
                    android:textColor="@color/white"
                    android:textColorHint="@color/white"
                    android:textSize="12sp" />

            </com.google.android.material.textfield.TextInputLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:gravity="end"
                android:orientation="horizontal">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/refreshButton"
                    style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                    android:layout_width="wrap_content"
                    android:layout_height="36dp"
                    android:layout_marginEnd="8dp"
                    android:insetBottom="0dp"
                    android:insetTop="0dp"
                    android:minHeight="0dp"
                    android:minWidth="36dp"
                    android:paddingBottom="0dp"
                    android:paddingTop="0dp"
                    app:icon="@drawable/ic_refresh"
                    app:iconGravity="textStart"
                    app:iconPadding="0dp"
                    app:iconSize="14dp"
                    app:iconTint="@color/white"
                    app:strokeColor="@color/white" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/urlConfirmButton"
                    android:layout_width="wrap_content"
                    android:layout_height="36dp"
                    android:insetBottom="0dp"
                    android:insetTop="0dp"
                    android:minHeight="0dp"
                    android:paddingBottom="0dp"
                    android:paddingEnd="8dp"
                    android:paddingStart="8dp"
                    android:paddingTop="0dp"
                    android:text="确认"
                    android:textSize="10sp"
                    app:icon="@drawable/ic_check"
                    app:iconGravity="textStart"
                    app:iconPadding="2dp"
                    app:iconSize="14dp" />
            </LinearLayout>
        </LinearLayout>
    </androidx.cardview.widget.CardView>

    <!-- 菜单标签 -->
    <TextView
        android:id="@+id/scriptListLabel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_marginEnd="72dp"
        android:layout_marginBottom="96dp"
        android:background="@color/card_bg"
        android:paddingHorizontal="8dp"
        android:paddingVertical="4dp"
        android:text="本地脚本列表"
        android:textColor="@color/white"
        android:textSize="12sp"
        android:visibility="gone"
        app:cornerRadius="4dp" />

    <TextView
        android:id="@+id/recordLabel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_marginEnd="72dp"
        android:layout_marginBottom="160dp"
        android:background="@color/card_bg"
        android:paddingHorizontal="8dp"
        android:paddingVertical="4dp"
        android:text="录制/停止"
        android:textColor="@color/white"
        android:textSize="12sp"
        android:visibility="gone"
        app:cornerRadius="4dp" />

    <TextView
        android:id="@+id/initSdkLabel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_marginEnd="72dp"
        android:layout_marginBottom="224dp"
        android:background="@color/card_bg"
        android:paddingHorizontal="8dp"
        android:paddingVertical="4dp"
        android:text="初始化SDK"
        android:textColor="@color/white"
        android:textSize="12sp"
        android:visibility="gone"
        app:cornerRadius="4dp" />

    <TextView
        android:id="@+id/viewPlanLabel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_marginEnd="72dp"
        android:layout_marginBottom="288dp"
        android:background="@color/card_bg"
        android:paddingHorizontal="8dp"
        android:paddingVertical="4dp"
        android:text="查看测试计划"
        android:textColor="@color/white"
        android:textSize="12sp"
        android:visibility="gone"
        app:cornerRadius="4dp" />

    <TextView
        android:id="@+id/viewScriptLabel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_marginEnd="72dp"
        android:layout_marginBottom="352dp"
        android:background="@color/card_bg"
        android:paddingHorizontal="8dp"
        android:paddingVertical="4dp"
        android:text="查看选中脚本"
        android:textColor="@color/white"
        android:textSize="12sp"
        android:visibility="gone"
        app:cornerRadius="4dp" />

    <TextView
        android:id="@+id/deviceIdLabel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_marginEnd="72dp"
        android:layout_marginBottom="416dp"
        android:background="@color/card_bg"
        android:paddingHorizontal="8dp"
        android:paddingVertical="4dp"
        android:text="复制设备ID"
        android:textColor="@color/white"
        android:textSize="12sp"
        android:visibility="gone"
        app:cornerRadius="4dp" />

    <TextView
        android:id="@+id/screenRotationLabel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_marginEnd="72dp"
        android:layout_marginBottom="480dp"
        android:background="@color/card_bg"
        android:paddingHorizontal="8dp"
        android:paddingVertical="4dp"
        android:text="横竖屏切换"
        android:textColor="@color/white"
        android:textSize="12sp"
        android:visibility="gone"
        app:cornerRadius="4dp" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
