<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_gravity="bottom|start"
    android:layout_marginStart="16dp"
    android:layout_marginBottom="16dp"
    android:padding="12dp"
    android:orientation="horizontal">

    <!-- URL列表按钮 -->
    <com.google.android.material.button.MaterialButton
        android:id="@+id/urlListButton"
        style="@style/Widget.MaterialComponents.Button"
        android:layout_width="wrap_content"
        android:layout_height="32dp"
        android:layout_marginEnd="8dp"
        android:insetTop="0dp"
        android:insetBottom="0dp"
        android:minWidth="0dp"
        android:minHeight="0dp"
        android:paddingStart="8dp"
        android:paddingTop="8dp"
        android:paddingEnd="8dp"
        android:paddingBottom="8dp"
        android:text="URL列表"
        android:textColor="@color/white"
        android:textSize="12sp"
        app:backgroundTint="#FF9800"
        app:cornerRadius="14dp" />

    <!-- 脚本列表按钮 -->
    <com.google.android.material.button.MaterialButton
        android:id="@+id/scriptListButton"
        style="@style/Widget.MaterialComponents.Button"
        android:layout_width="wrap_content"
        android:layout_height="32dp"
        android:layout_marginEnd="8dp"
        android:insetTop="0dp"
        android:insetBottom="0dp"
        android:minWidth="0dp"
        android:minHeight="0dp"
        android:paddingStart="8dp"
        android:paddingTop="2dp"
        android:paddingEnd="8dp"
        android:paddingBottom="2dp"
        android:text="脚本列表"
        android:textColor="@color/white"
        android:textSize="12sp"
        app:backgroundTint="@color/script_button"
        app:cornerRadius="14dp" />

    <!-- 录制按钮 -->
    <com.google.android.material.button.MaterialButton
        android:id="@+id/recordButton"
        style="@style/Widget.MaterialComponents.Button"
        android:layout_width="wrap_content"
        android:layout_height="32dp"
        android:insetTop="0dp"
        android:insetBottom="0dp"
        android:minWidth="0dp"
        android:minHeight="0dp"
        android:paddingStart="8dp"
        android:paddingTop="2dp"
        android:paddingEnd="8dp"
        android:paddingBottom="2dp"
        android:text="录制"
        android:textColor="@color/white"
        android:textSize="12sp"
        app:backgroundTint="@color/record_button"
        app:cornerRadius="14dp" />

</LinearLayout>
