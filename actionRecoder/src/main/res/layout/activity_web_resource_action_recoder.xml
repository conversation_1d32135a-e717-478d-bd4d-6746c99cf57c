<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/bg_dark">

    <androidx.cardview.widget.CardView
        android:id="@+id/webViewContainer"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_margin="0dp"
        app:cardCornerRadius="0dp"
        app:cardElevation="0dp"
        app:layout_constraintBottom_toTopOf="@id/controlPanel"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <FrameLayout
            android:id="@+id/webView_container"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

    </androidx.cardview.widget.CardView>

    <com.google.android.material.card.MaterialCardView
        android:id="@+id/controlPanel"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="1dp"
        app:cardCornerRadius="8dp"
        app:cardElevation="2dp"
        app:cardBackgroundColor="@color/card_bg"
        app:layout_constraintBottom_toBottomOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="2dp">

            <!-- 第一行控制按钮 -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/recordButton"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_marginEnd="1dp"
                android:text="录制"
                android:textSize="9sp"
                android:minHeight="0dp"
                android:paddingTop="1dp"
                android:paddingBottom="1dp"
                android:paddingStart="4dp"
                android:paddingEnd="4dp"
                app:icon="@drawable/ic_record"
                app:iconGravity="textStart"
                app:iconSize="12dp"
                app:iconPadding="2dp"
                app:backgroundTint="@color/record_button"
                app:layout_constraintEnd_toStartOf="@id/scriptListButton"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/scriptListButton"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_marginHorizontal="1dp"
                android:text="本地脚本"
                android:textSize="9sp"
                android:minHeight="0dp"
                android:paddingTop="1dp"
                android:paddingBottom="1dp"
                android:paddingStart="4dp"
                android:paddingEnd="4dp"
                app:icon="@drawable/ic_list"
                app:iconGravity="textStart"
                app:iconSize="12dp"
                app:iconPadding="2dp"
                app:backgroundTint="@color/script_button"
                app:layout_constraintEnd_toStartOf="@id/serviceScriptButton"
                app:layout_constraintStart_toEndOf="@id/recordButton"
                app:layout_constraintTop_toTopOf="parent" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/serviceScriptButton"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_marginStart="1dp"
                android:layout_marginEnd="1dp"
                android:text="测试选项"
                android:textSize="9sp"
                android:minHeight="0dp"
                android:paddingTop="1dp"
                android:paddingBottom="1dp"
                android:paddingStart="4dp"
                android:paddingEnd="4dp"
                app:icon="@drawable/ic_play"
                app:iconGravity="textStart"
                app:iconSize="12dp"
                app:iconPadding="2dp"
                app:backgroundTint="@color/service_button"
                app:layout_constraintEnd_toStartOf="@id/deviceIdButton"
                app:layout_constraintStart_toEndOf="@id/scriptListButton"
                app:layout_constraintTop_toTopOf="parent" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/deviceIdButton"
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                android:text="ID"
                android:textSize="9sp"
                android:minHeight="0dp"
                android:paddingTop="1dp"
                android:paddingBottom="1dp"
                android:paddingStart="4dp"
                android:paddingEnd="4dp"
                app:icon="@drawable/ic_content_copy"
                app:iconGravity="textStart"
                app:iconSize="12dp"
                app:iconPadding="2dp"
                app:backgroundTint="#3F51B5"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/serviceScriptButton"
                app:layout_constraintTop_toTopOf="parent" />

            <!-- URL输入区域 -->
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/urlInputLayout"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="2dp"
                android:layout_marginEnd="2dp"
                app:hintTextColor="@color/white"
                app:boxStrokeColor="@color/white"
                app:boxBackgroundColor="#333333"
                app:endIconMode="clear_text"
                app:endIconTint="@color/white"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense"
                app:layout_constraintEnd_toStartOf="@id/refreshButton"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/recordButton">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/urlEditText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="输入URL"
                    android:textColorHint="@color/white"
                    android:textColor="@color/white"
                    android:inputType="textUri"
                    android:imeOptions="actionGo"
                    android:singleLine="true"
                    android:textSize="12sp"
                    android:paddingTop="8dp"
                    android:paddingBottom="8dp" />

            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.button.MaterialButton
                android:id="@+id/refreshButton"
                android:layout_width="36dp"
                android:layout_height="36dp"
                android:layout_marginEnd="2dp"
                android:minWidth="36dp"
                android:minHeight="0dp"
                android:insetTop="0dp"
                android:insetBottom="0dp"
                android:paddingTop="0dp"
                android:paddingBottom="0dp"
                app:icon="@drawable/ic_refresh"
                app:iconSize="14dp"
                app:iconGravity="textStart"
                app:iconPadding="0dp"
                style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                app:iconTint="@color/white"
                app:strokeColor="@color/white"
                app:layout_constraintBottom_toBottomOf="@id/urlInputLayout"
                app:layout_constraintEnd_toStartOf="@id/urlConfirmButton"
                app:layout_constraintTop_toTopOf="@id/urlInputLayout" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/urlConfirmButton"
                android:layout_width="wrap_content"
                android:layout_height="36dp"
                android:text="确认"
                android:textSize="10sp"
                android:minHeight="0dp"
                android:insetTop="0dp"
                android:insetBottom="0dp"
                android:paddingTop="0dp"
                android:paddingBottom="0dp"
                android:paddingStart="8dp"
                android:paddingEnd="8dp"
                app:icon="@drawable/ic_check"
                app:iconSize="14dp"
                app:iconPadding="2dp"
                app:iconGravity="textStart"
                app:layout_constraintBottom_toBottomOf="@id/urlInputLayout"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@id/urlInputLayout" />

            <!-- 测试按钮 -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/testButton"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_marginTop="2dp"
                android:text="测试Activity"
                android:textSize="10sp"
                android:minHeight="0dp"
                android:paddingTop="1dp"
                android:paddingBottom="1dp"
                android:paddingStart="4dp"
                android:paddingEnd="4dp"
                app:icon="@drawable/ic_play"
                app:iconGravity="textStart"
                app:iconSize="12dp"
                app:iconPadding="2dp"
                app:backgroundTint="#009688"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/urlInputLayout" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </com.google.android.material.card.MaterialCardView>

</androidx.constraintlayout.widget.ConstraintLayout>