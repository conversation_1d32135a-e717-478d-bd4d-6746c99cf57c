<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="XY坐标偏移"
        android:textSize="16sp"
        android:layout_marginBottom="8dp"/>

    <EditText
        android:id="@+id/xyOffsetEdit"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="输入坐标偏移值"
        android:inputType="numberSigned"
        android:layout_marginBottom="16dp"/>

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="时间偏移(毫秒)"
        android:textSize="16sp"
        android:layout_marginBottom="8dp"/>

    <EditText
        android:id="@+id/timeOffsetEdit"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="输入时间偏移值"
        android:inputType="numberSigned"
        android:layout_marginBottom="8dp"/>

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="说明：正值表示增加偏移，负值表示减少偏移"
        android:textSize="12sp"
        android:textColor="#666666"
        android:layout_marginTop="8dp"/>

</LinearLayout> 