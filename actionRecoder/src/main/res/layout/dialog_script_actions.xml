<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@drawable/dialog_background"
    android:padding="16dp">

    <TextView
        android:id="@+id/dialogTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="选择操作"
        android:textColor="#FFFFFF"
        android:textSize="18sp"
        android:textStyle="bold"
        android:paddingBottom="16dp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            android:id="@+id/actionView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="查看脚本"
            android:textColor="#FFFFFF"
            android:textSize="16sp"
            android:padding="12dp"
            android:background="?attr/selectableItemBackground" />

        <TextView
            android:id="@+id/actionPlayback"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="回放脚本"
            android:textColor="#FFFFFF"
            android:textSize="16sp"
            android:padding="12dp"
            android:background="?attr/selectableItemBackground" />

        <TextView
            android:id="@+id/actionShare"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="分享脚本"
            android:textColor="#FFFFFF"
            android:textSize="16sp"
            android:padding="12dp"
            android:background="?attr/selectableItemBackground" />

        <TextView
            android:id="@+id/actionCopy"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="复制脚本"
            android:textColor="#FFFFFF"
            android:textSize="16sp"
            android:padding="12dp"
            android:background="?attr/selectableItemBackground" />

        <TextView
            android:id="@+id/actionCopyFilename"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="复制文件名"
            android:textColor="#FFFFFF"
            android:textSize="16sp"
            android:padding="12dp"
            android:background="?attr/selectableItemBackground" />
    </LinearLayout>

    <Button
        android:id="@+id/btnCancel"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="取消"
        android:textColor="#2F81F7"
        android:background="@android:color/transparent"
        android:layout_marginTop="8dp" />
</LinearLayout>
