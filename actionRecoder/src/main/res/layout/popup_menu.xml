<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="6dp"
    android:background="@drawable/popup_menu_background">

    <include
        android:id="@+id/menuItemScreenRotation"
        layout="@layout/menu_item_button"
        android:layout_width="120dp"
        android:layout_height="wrap_content"
        android:layout_marginBottom="4dp" />

    <include
        android:id="@+id/menuItemInitSdk"
        layout="@layout/menu_item_button"
        android:layout_width="120dp"
        android:layout_height="wrap_content"
        android:layout_marginBottom="4dp" />

    <include
        android:id="@+id/menuItemViewPlan"
        layout="@layout/menu_item_button"
        android:layout_width="120dp"
        android:layout_height="wrap_content"
        android:layout_marginBottom="4dp" />

    <include
        android:id="@+id/menuItemViewScript"
        layout="@layout/menu_item_button"
        android:layout_width="120dp"
        android:layout_height="wrap_content"
        android:layout_marginBottom="4dp" />

    <include
        android:id="@+id/menuItemDeviceId"
        layout="@layout/menu_item_button"
        android:layout_width="120dp"
        android:layout_height="wrap_content" />

</LinearLayout>
