{"scripts": [{"url": "https://www.bilibili.com/", "weight": 1.0, "id": "script_01"}, {"url": "https://www.bilibili.com/", "weight": 1.0, "id": "script_02"}, {"url": "https://www.sina.com.cn/", "weight": 1.0, "id": "script_02"}, {"url": "https://www.sohu.com/", "weight": 11.0, "id": "script_03"}], "mock_config": {"uas": [{"ua": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "weight": 1.0}, {"ua": "Mozilla/5.0 (Linux; Android 11; SM-G9980) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36", "weight": 0.7}, {"ua": "Mozilla/5.0 (Linux; Android 12; Pixel 5) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "weight": 0.5}], "delay": 5, "interval": 60, "expire": 3600, "jitter": {"xy": {"percent": 0.1, "min_px": 1}, "time": {"percent": 0.5, "min_ms": 20}}, "debug": true}}