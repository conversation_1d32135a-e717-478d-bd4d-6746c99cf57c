package ai.ad.webview.plugin.actionrecoder

import ai.ad.webview.plugin.actionrecoder.repo.MockScriptRepoImpl
import ai.ad.webview.plugin.model.ScriptData
import ai.ad.webview.plugin.repository.ScriptRepo
import ai.ad.webview.plugin.scheduler.ScriptScheduler
import ai.ad.webview.plugin.utils.DeviceIdUtil
import ai.ad.webview.sdk.logger.WSKLog

import ai.ad.webview.plugin.webview.WSKDelegate
import ai.ad.webview.sdk.WSKSDK
import ai.ad.webview.sdk.actionrecoder.R
import android.annotation.SuppressLint
import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.content.Intent
import android.content.res.Resources
import android.graphics.Typeface
import android.os.Bundle
import android.text.method.ScrollingMovementMethod
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.webkit.WebView
import android.widget.LinearLayout
import android.widget.PopupWindow
import android.widget.ScrollView
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.cardview.widget.CardView
import androidx.core.content.ContextCompat
import androidx.core.content.edit
import com.google.android.material.button.MaterialButton
import com.google.android.material.floatingactionbutton.FloatingActionButton
import com.google.android.material.textfield.TextInputEditText
import java.io.File

class WSKActionRecordActivity : AppCompatActivity() {
    private var TAG = "WSKActionRecordActivity"

    // URL列表数据
    private data class UrlItem(
        val name: String,
        val accountId: String,
        val url: String,
        val clickLimit: Int
    )

    private val urlList = listOf(
        UrlItem("账号1", "PH010", "https://smallgames.cc/", 200),
        UrlItem("账号2", "TH008", "https://bestgamebox.com/", 200),
        UrlItem("账号3 ZH20150519", "ZH20150519", "https://gemora.top/", 200),
        UrlItem("账号3 ZH20150519", "ZH20150519", "https://popinfobox.top/", 200),
        UrlItem("账号3 ZH20150519", "ZH20150519", "https://luminhair.top/", 200)
    )

    private lateinit var webView: WebView
    private lateinit var scriptManager: ScriptManager
    private lateinit var webDelegate: WSKDelegate
    private lateinit var webViewRecorderManager: WebViewRecorderManager

    // 主菜单按钮
    private lateinit var mainMenuButton: FloatingActionButton

    // 左侧按钮
    private lateinit var recordButton: MaterialButton
    private lateinit var scriptListButton: MaterialButton
    private lateinit var urlListButton: MaterialButton

    // 弹出菜单按钮
    private lateinit var menuItemScreenRotation: MaterialButton
    private lateinit var menuItemInitSdk: MaterialButton
    private lateinit var menuItemViewPlan: MaterialButton
    private lateinit var menuItemViewScript: MaterialButton
    private lateinit var menuItemDeviceId: MaterialButton

    // 弹出菜单
    private lateinit var popupMenu: PopupWindow

    // 脚本调度器
    private lateinit var scriptScheduler: ScriptScheduler

    // 子菜单标签
    private lateinit var recordLabel: TextView
    private lateinit var scriptListLabel: TextView
    private lateinit var initSdkLabel: TextView
    private lateinit var viewPlanLabel: TextView
    private lateinit var viewScriptLabel: TextView
    private lateinit var deviceIdLabel: TextView
    private lateinit var screenRotationLabel: TextView

    // URL输入相关
    private lateinit var urlInputCard: CardView
    private lateinit var urlEditText: TextInputEditText
    private lateinit var urlConfirmButton: MaterialButton
    private lateinit var refreshButton: MaterialButton

    private var debug: Boolean = true // Default debug mode enabled
    private var isMenuOpen: Boolean = false
    private var isSdkInitialized: Boolean = false // 标记SDK是否已初始化
    private var isLandscape: Boolean = false // 标记当前是否为横屏

    @SuppressLint("ClickableViewAccessibility")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 设置全屏
        window.setFlags(
            WindowManager.LayoutParams.FLAG_FULLSCREEN,
            WindowManager.LayoutParams.FLAG_FULLSCREEN
        )

        setContentView(R.layout.activity_web_resource_action_recoder_new)

        // 初始化视图
        initializeViews()
        setupButtons()

        // 初始化脚本调度器
        initScriptScheduler()

        // 应用缩放比例
        webViewRecorderManager.updateWebViewScale(getSavedScale())

        // 读取保存的屏幕方向设置并应用
        isLandscape = getSavedScreenOrientation()
        applyScreenOrientation(isLandscape)

        // 检查本地脚本列表并更新UI状态
        updateScriptListState()
    }

    /**
     * 初始化脚本调度器
     */
    private fun initScriptScheduler() {
        try {
            WSKLog.d(TAG, "initScriptScheduler -> 初始化脚本调度器")

            // 获取脚本调度器实例
            scriptScheduler = ScriptScheduler.getInstance()

            // 初始化脚本调度器
            scriptScheduler.init(applicationContext)

            // 设置脚本任务监听器
            scriptScheduler.setTaskListener(object : ScriptScheduler.ScriptTaskListener {
                override fun onScriptReady(scriptData: ScriptData) {
                    WSKLog.d(TAG, "onScriptReady -> 脚本准备就绪，开始执行")
                    runOnUiThread {
                        // 使用 WebDelegate 执行脚本
                        webDelegate.executeScriptData(scriptData)
                        showToast("开始执行脚本: ${scriptData.metadata.id}")
                    }
                }

                override fun onScriptTimeout() {
                    WSKLog.d(TAG, "onScriptTimeout -> 脚本执行超时")
                    runOnUiThread {
                        showToast("脚本执行超时")
                    }
                }
            })

            WSKLog.d(TAG, "initScriptScheduler -> 脚本调度器初始化完成")
        } catch (e: Exception) {
            WSKLog.e(TAG, "initScriptScheduler -> 初始化脚本调度器失败: ${e.message}")
            showToast("初始化脚本调度器失败: ${e.message}")
        }
    }

    private fun initializeViews() {
        scriptManager = ScriptManager(this)
        webView = findViewById(R.id.webView)
        webDelegate = WSKDelegate(this, webView)
        webViewRecorderManager = WebViewRecorderManager(this, webView, webDelegate)

        // 初始化主菜单按钮
        mainMenuButton = findViewById(R.id.mainMenuButton)

        // 初始化左侧按钮
        val leftSideButtons = findViewById<View>(R.id.leftSideButtons)
        recordButton = leftSideButtons.findViewById(R.id.recordButton)
        scriptListButton = leftSideButtons.findViewById(R.id.scriptListButton)
        urlListButton = leftSideButtons.findViewById(R.id.urlListButton)

        // 初始化弹出菜单
        initPopupMenu()

        // 保留这些标签的引用，因为它们在其他地方被使用
        recordLabel = TextView(this)
        scriptListLabel = TextView(this)
        initSdkLabel = TextView(this)
        viewPlanLabel = TextView(this)
        viewScriptLabel = TextView(this)
        deviceIdLabel = TextView(this)
        screenRotationLabel = TextView(this)

        // 初始化URL输入相关组件
        urlInputCard = findViewById(R.id.urlInputCard)
        urlEditText = findViewById(R.id.urlEditText)
        urlConfirmButton = findViewById(R.id.urlConfirmButton)
        refreshButton = findViewById(R.id.refreshButton)

        // 设置初始URL
        urlEditText.setText(getSavedUrl())
    }

    /**
     * 初始化弹出菜单
     */
    private fun initPopupMenu() {
        // 创建弹出菜单视图
        val popupView = LayoutInflater.from(this).inflate(R.layout.popup_menu, null)

        // 初始化弹出菜单按钮
        menuItemScreenRotation = popupView.findViewById(R.id.menuItemScreenRotation)
        menuItemInitSdk = popupView.findViewById(R.id.menuItemInitSdk)
        menuItemViewPlan = popupView.findViewById(R.id.menuItemViewPlan)
        menuItemViewScript = popupView.findViewById(R.id.menuItemViewScript)
        menuItemDeviceId = popupView.findViewById(R.id.menuItemDeviceId)

        // 设置按钮文本和背景颜色
        menuItemScreenRotation.apply {
            text = "横竖屏切换"
            setBackgroundColor(ContextCompat.getColor(context, R.color.purple_500))
        }

        menuItemInitSdk.apply {
            text = "初始化SDK"
            setBackgroundColor(ContextCompat.getColor(context, R.color.service_button))
        }

        menuItemViewPlan.apply {
            text = "查看测试计划"
            setBackgroundColor(ContextCompat.getColor(context, R.color.teal_700))
        }

        menuItemViewScript.apply {
            text = "查看选中脚本"
            setBackgroundColor(ContextCompat.getColor(context, R.color.orange_700))
        }

        menuItemDeviceId.apply {
            text = "复制设备ID"
            setBackgroundColor(ContextCompat.getColor(context, R.color.indigo_500))
        }

        // 创建弹出菜单
        popupMenu = PopupWindow(
            popupView,
            ViewGroup.LayoutParams.WRAP_CONTENT,
            ViewGroup.LayoutParams.WRAP_CONTENT,
            true
        ).apply {
            elevation = 10f
            isOutsideTouchable = true
        }
    }

    private fun setupButtons() {
        // 设置主菜单按钮点击事件 - 显示弹出菜单
        mainMenuButton.setOnClickListener {
            showPopupMenu()
        }

        // 设置左侧按钮点击事件
        recordButton.setOnClickListener {
            handleRecordButtonClick()
        }

        scriptListButton.setOnClickListener {
            if (scriptListButton.isEnabled) {
                handleScriptListButtonClick()
            }
        }

        urlListButton.setOnClickListener {
            showUrlListDialog()
        }

        // 设置弹出菜单按钮点击事件
        menuItemScreenRotation.setOnClickListener {
            // 切换横竖屏状态
            isLandscape = !isLandscape

            // 应用新的屏幕方向
            applyScreenOrientation(isLandscape)

            // 保存屏幕方向设置
            saveScreenOrientation(isLandscape)

            // 显示提示
            showToast(if (isLandscape) "已切换到横屏模式" else "已切换到竖屏模式")

            // 关闭弹出菜单
            popupMenu.dismiss()
        }

        menuItemInitSdk.setOnClickListener {
            //            initializeSDK()
            popupMenu.dismiss()
        }

        menuItemViewPlan.setOnClickListener {
            showScriptPlanDialog()
            popupMenu.dismiss()
        }

        menuItemViewScript.setOnClickListener {
            showSelectedScriptDialog()
            popupMenu.dismiss()
        }

        menuItemDeviceId.setOnClickListener {
            copyDeviceIdToClipboard()
            popupMenu.dismiss()
        }

        // 设置URL相关按钮点击事件
        refreshButton.setOnClickListener {
            resetWebView()
            urlInputCard.visibility = View.GONE
        }

        urlConfirmButton.setOnClickListener {
            val url = urlEditText.text.toString()
            if (url.isNotEmpty()) {
                saveUrl(url)
                webView.loadUrl(url)
                showToast("URL已更新: $url")
                urlInputCard.visibility = View.GONE
            }
        }

        // 长按主菜单按钮显示URL输入框
        mainMenuButton.setOnLongClickListener {
            urlInputCard.visibility =
                if (urlInputCard.visibility == View.VISIBLE) View.GONE else View.VISIBLE
            true
        }
    }

    /**
     * 显示弹出菜单
     */
    private fun showPopupMenu() {
        try {
            // 确保弹出菜单的内容视图已经被测量
            val contentView = popupMenu.contentView
            contentView.measure(
                View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED),
                View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED)
            )

            // 计算弹出菜单的位置
            val location = IntArray(2)
            mainMenuButton.getLocationOnScreen(location)

            // 计算弹出菜单的宽度和高度
            val popupWidth = contentView.measuredWidth
            val popupHeight = contentView.measuredHeight

            // 计算弹出菜单的位置
            val x = location[0] - popupWidth + mainMenuButton.width
            val y = location[1] - popupHeight - 16

            // 在主菜单按钮上方显示弹出菜单
            popupMenu.showAtLocation(
                mainMenuButton,
                android.view.Gravity.NO_GRAVITY,
                x,
                y
            )

            // 添加动画效果
            contentView.alpha = 0f
            contentView.animate()
                .alpha(1f)
                .setDuration(200)
                .start()

            WSKLog.d(TAG, "显示弹出菜单: x=$x, y=$y, width=$popupWidth, height=$popupHeight")
        } catch (e: Exception) {
            WSKLog.e(TAG, "显示弹出菜单失败: ${e.message}")
            showToast("显示菜单失败: ${e.message}")
        }
    }

    /**
     * 显示URL列表对话框
     */
    private fun showUrlListDialog() {
        // 创建对话框布局
        val dialogView = LayoutInflater.from(this).inflate(R.layout.dialog_url_list, null)
        val container = dialogView.findViewById<LinearLayout>(R.id.urlListContainer)

        // 创建对话框
        val urlDialog = AlertDialog.Builder(this)
            .setTitle("选择URL")
            .setView(dialogView)
            .setNegativeButton("取消", null)
            .create()

        // 为每个URL创建一个项目
        urlList.forEach { urlItem ->
            val itemView = TextView(this).apply {
                text =
                    "${urlItem.name} ${urlItem.accountId}\nURL: ${urlItem.url}\n每日点击上限: ${urlItem.clickLimit}"
                setPadding(16.dp, 16.dp, 16.dp, 16.dp)
                setTextColor(ContextCompat.getColor(context, R.color.white))
                textSize = 16f
                setTypeface(null, Typeface.BOLD)
                background = ContextCompat.getDrawable(context, R.drawable.url_item_background)

                // 设置点击事件
                setOnClickListener {
                    // 先加载空白页面，然后再加载目标URL
                    webView.loadUrl("about:blank")

                    // 保存URL以便下次启动时加载
                    saveUrl(urlItem.url)

                    // 使用延迟加载目标URL，给空白页面加载一些时间
                    webView.postDelayed({
                        // 加载URL
                        webView.loadUrl(urlItem.url)

                        // 开始录制
                        if (!webViewRecorderManager.isRecording()) {
                            handleRecordButtonClick()
                        }
                    }, 100) // 100毫秒的延迟，可以根据需要调整

                    // 关闭对话框
                    urlDialog.dismiss()

                    // 显示加载提示
                    showToast("正在加载页面，请稍候...")
                }
            }

            // 添加到容器
            container.addView(itemView)

            // 添加分隔线
            if (urlItem != urlList.last()) {
                val divider = View(this).apply {
                    layoutParams = LinearLayout.LayoutParams(
                        LinearLayout.LayoutParams.MATCH_PARENT,
                        1.dp
                    )
                    setBackgroundColor(ContextCompat.getColor(context, R.color.hint_color))
                }
                container.addView(divider)
            }
        }

        // 显示对话框
        urlDialog.show()
    }

    /**
     * 复制设备ID到剪贴板
     */
    private fun copyDeviceIdToClipboard() {
        try {
            // 获取剪贴板管理器
            val clipboard = getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
            val deviceId = DeviceIdUtil.getDeviceId()
            // 创建ClipData对象
            val clip = ClipData.newPlainText("Device ID", deviceId)
            // 将ClipData对象设置到剪贴板
            clipboard.setPrimaryClip(clip)
            // 显示提示
            showToast("GAID已复制: $deviceId")
            WSKLog.d("WSKActionRecord", "已复制设备ID到剪贴板: $deviceId")
        } catch (e: Exception) {
            WSKLog.e("WSKActionRecord", "复制设备ID失败,$e")
            showToast("复制失败: ${e.message}")
        }
    }

    private fun handleRecordButtonClick() {
        if (!webViewRecorderManager.isRecording()) {
            startRecording()
        } else {
            stopRecording()
        }
    }

    private fun startRecording() {
        // 隐藏所有其他按钮，只保留录制按钮
        hideAllButtonsExceptRecord()

        // 确保录制按钮可见
        recordButton.visibility = View.VISIBLE

        // 开始录制，传入当前URL
        webViewRecorderManager.startRecording(webView.url ?: getSavedUrl())
        showToast("开始录制")
    }

    /**
     * 隐藏除了录制按钮外的所有按钮
     */
    private fun hideAllButtonsExceptRecord() {
        // 隐藏主菜单按钮
        mainMenuButton.visibility = View.GONE

        // 隐藏左侧按钮组中的其他按钮
        urlListButton.visibility = View.GONE
        scriptListButton.visibility = View.GONE

        // 隐藏URL输入卡片
        urlInputCard.visibility = View.GONE

        // 更新录制按钮的外观
        recordButton.apply {
            text = "停止录制"
            setBackgroundColor(ContextCompat.getColor(context, R.color.recording_red))
        }
    }

    private fun stopRecording() {
        // 恢复录制按钮外观
        recordButton.apply {
            text = "录制"
            setBackgroundColor(ContextCompat.getColor(context, R.color.recording_stop))
        }

        // 恢复所有按钮的显示
        showAllButtons()

        // 停止录制并显示脚本详情对话框
        val scriptData = webViewRecorderManager.stopRecording()
        showScriptDetailsDialog(scriptData)
    }

    /**
     * 恢复所有按钮的显示
     */
    private fun showAllButtons() {
        // 显示主菜单按钮
        mainMenuButton.visibility = View.VISIBLE

        // 显示左侧按钮组中的所有按钮
        urlListButton.visibility = View.VISIBLE
        scriptListButton.visibility = View.VISIBLE

        // 更新脚本列表按钮状态
        updateScriptListState()
    }

    private fun showScriptDetailsDialog(scriptData: ScriptData) {
        val dialogBuilder = AlertDialog.Builder(this)
        dialogBuilder.setTitle("脚本详情")

        // 统计点击和滑动事件
        val clicks = scriptData.events.count { it is ScriptData.Event.Click }
        val swipes = (scriptData.events.size - clicks) / 3 // 假设每个滑动包含3个事件(d,m,u)

        val message = """
            URL: ${scriptData.metadata.url}
            事件总数: ${scriptData.events.size}
            点击事件数: $clicks
            滑动事件数: $swipes
            录制时长: ${(System.currentTimeMillis() - webViewRecorderManager.getRecordStartTime()) / 1000}秒
            脚本ID: ${scriptData.metadata.id}

            脚本内容:
            ${scriptData.toJSONObject().toString(4)}
        """.trimIndent()

        // 创建ScrollView和TextView
        val scrollView = ScrollView(this)
        val textView = TextView(this).apply {
            text = message
            textSize = 12f
            setPadding(16.dp, 16.dp, 16.dp, 16.dp)
            isVerticalScrollBarEnabled = true
            movementMethod = ScrollingMovementMethod()
        }
        scrollView.addView(textView)

        dialogBuilder.setView(scrollView)
        dialogBuilder.setPositiveButton("保存") { dialog, _ ->
            val file = scriptManager.saveScript(scriptData)
            showToast("脚本已保存: ${file.name}")
            dialog.dismiss()
            // 保存后更新脚本列表状态
            updateScriptListState()
        }
        dialogBuilder.setNegativeButton("关闭") { dialog, _ ->
            dialog.dismiss()
        }

        // 创建并显示对话框
        val dialog = dialogBuilder.create()
        dialog.show()

        // 设置对话框大小
        dialog.window?.setLayout(
            (resources.displayMetrics.widthPixels * 0.9).toInt(),
            (resources.displayMetrics.heightPixels * 0.7).toInt()
        )
    }

    private fun resetWebView() {
        webViewRecorderManager.resetWebView()
        webView.postDelayed({
            webView.loadUrl(getSavedUrl())
        }, 100)
        showToast("正在重新加载页面...")
    }

    private fun handleScriptListButtonClick() {
        // 启动脚本列表活动
        val intent = Intent(this, ScriptListActivity::class.java)
        startActivityForResult(intent, REQUEST_SCRIPT)
    }

    private fun updateScriptListState() {
        val scriptList = scriptManager.getScriptList()
        scriptListButton.isEnabled = scriptList.isNotEmpty()
        if (!scriptList.isNotEmpty()) {
            scriptListButton.alpha = 0.5f
        } else {
            scriptListButton.alpha = 1.0f
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        if (requestCode == REQUEST_SCRIPT && resultCode == RESULT_OK && data != null) {
            // 获取选中的脚本内容、ID和文件路径
            val scriptContent = data.getStringExtra("script_content") ?: ""
            val scriptId = data.getStringExtra("script_id") ?: ""
            val scriptFilePath = data.getStringExtra("script_file_path") ?: ""

            // 保存当前选中的脚本文件路径，用于分享功能
            if (scriptFilePath.isNotEmpty()) {
                getSharedPreferences("WSKRecorder", MODE_PRIVATE).edit()
                    .putString("selected_script_path", scriptFilePath)
                    .apply()
                WSKLog.d(TAG, "onActivityResult -> 保存当前选中脚本路径: $scriptFilePath")
            }

            if (scriptContent.isNotEmpty()) {
                try {
                    // 显示屏幕方向选择对话框
                    showScreenOrientationDialog(scriptContent, scriptId)
                } catch (e: Exception) {
                    WSKLog.e(TAG, "onActivityResult -> 启动回放Activity失败: ${e.message}")
                    showToast("启动回放Activity失败: ${e.message}")
                }
            }
        }
    }

    /**
     * 显示屏幕方向选择对话框
     */
    private fun showScreenOrientationDialog(scriptContent: String, scriptId: String) {
        val items = arrayOf("竖屏", "横屏")
        AlertDialog.Builder(this)
            .setTitle("选择屏幕方向")
            .setItems(items) { _, which ->
                val isLandscape = which == 1
                // 启动回放Activity
                WebViewPlaybackActivity.start(this, scriptContent, scriptId, isLandscape)
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun showToast(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
    }

    private fun getSavedUrl(): String {
        val prefs = getSharedPreferences("WSKRecorder", MODE_PRIVATE)
        return prefs.getString("selected_url", "https://www.battleboxhz.com/")
            ?: "https://www.battleboxhz.com/"
    }

    private fun saveUrl(url: String) {
        val prefs = getSharedPreferences("WSKRecorder", MODE_PRIVATE)
        prefs.edit().putString("selected_url", url).apply()
    }

    private fun updateWebViewScale(scale: Float) {
        webViewRecorderManager.updateWebViewScale(scale)
    }

    private fun getSavedScale(): Float {
        val prefs = getSharedPreferences("WSKRecorder", MODE_PRIVATE)
        return prefs.getFloat("webview_scale", 1.0f)
    }

    private fun saveScale(scale: Float) {
        val prefs = getSharedPreferences("WSKRecorder", MODE_PRIVATE)
        prefs.edit() { putFloat("webview_scale", scale) }
    }

    /**
     * 显示测试计划对话框
     */
    private fun showScriptPlanDialog() {
        val scriptPlan = ScriptRepo.getScriptPlan()
        val jsonString = scriptPlan.toJSONObject().toString(4) // 格式化JSON

        showFormattedJsonDialog("测试计划", jsonString)
    }

    /**
     * 显示当前选中脚本对话框
     */
    private fun showSelectedScriptDialog() {
        val scriptContent = MockScriptRepoImpl.mTestScript
        if (scriptContent.isEmpty()) {
            showToast("当前没有选中的脚本")
            return
        }

        // 创建一个对话框，添加分享按钮
        val textView = TextView(this).apply {
            text = scriptContent
            textSize = 12f // 小字体
            setPadding(16.dp, 16.dp, 16.dp, 16.dp)
            isVerticalScrollBarEnabled = true
            movementMethod = ScrollingMovementMethod() // 允许滚动
        }

        // 创建对话框
        val dialog = AlertDialog.Builder(this)
            .setTitle("当前选中脚本")
            .setView(textView)
            .setPositiveButton("复制") { _, _ ->
                // 复制到剪贴板
                val clipboard = getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
                val clip = ClipData.newPlainText("脚本内容", scriptContent)
                clipboard.setPrimaryClip(clip)
                showToast("已复制到剪贴板")
            }
            .setNeutralButton("分享") { _, _ ->
                // 获取当前选中的脚本文件路径
                val scriptFilePath = getSharedPreferences("WSKRecorder", MODE_PRIVATE)
                    .getString("selected_script_path", "")

                if (!scriptFilePath.isNullOrEmpty()) {
                    val scriptFile = File(scriptFilePath)
                    if (scriptFile.exists()) {
                        scriptManager.shareScript(scriptFile)
                    } else {
                        showToast("脚本文件不存在")
                    }
                } else {
                    showToast("没有选中的脚本文件")
                }
            }
            .setNegativeButton("关闭", null)
            .create()

        // 设置对话框大小
        dialog.show()
        dialog.window?.setLayout(
            (resources.displayMetrics.widthPixels * 0.9).toInt(),
            (resources.displayMetrics.heightPixels * 0.7).toInt()
        )
    }

    /**
     * 显示格式化的JSON对话框
     */
    private fun showFormattedJsonDialog(title: String, jsonString: String) {
        // 创建一个TextView用于显示JSON
        val textView = TextView(this).apply {
            text = jsonString
            textSize = 12f // 小字体
            setPadding(16.dp, 16.dp, 16.dp, 16.dp)
            isVerticalScrollBarEnabled = true
            movementMethod = ScrollingMovementMethod() // 允许滚动
        }

        // 创建对话框
        val dialog = AlertDialog.Builder(this)
            .setTitle(title)
            .setView(textView)
            .setPositiveButton("复制") { _, _ ->
                // 复制到剪贴板
                val clipboard = getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
                val clip = ClipData.newPlainText(title, jsonString)
                clipboard.setPrimaryClip(clip)
                showToast("已复制到剪贴板")
            }
            .setNegativeButton("关闭", null)
            .create()

        // 设置对话框大小
        dialog.show()
        dialog.window?.setLayout(
            (resources.displayMetrics.widthPixels * 0.9).toInt(),
            (resources.displayMetrics.heightPixels * 0.7).toInt()
        )
    }

    override fun onDestroy() {
        super.onDestroy()

        try {
            WSKLog.d(TAG, "onDestroy -> 释放资源")

            // 停止脚本调度器
            if (::scriptScheduler.isInitialized) {
                scriptScheduler.stop()
            }

            // 释放 WebDelegate 资源
            if (::webDelegate.isInitialized) {
                webDelegate.release()
            }

            // 使用新的接口，传入WSKActivity类
            WSKSDK.detach(this)

            WSKLog.d(TAG, "onDestroy -> 资源释放完成")
        } catch (e: Exception) {
            WSKLog.e(TAG, "onDestroy -> 释放资源时发生异常: ${e.message}")
        }
    }

    /**
     * 处理返回键
     * 1. 如果WebView可以返回，则执行WebView的返回操作
     * 2. 如果WebView不能返回，则显示URL选择界面
     */
    @Deprecated("Deprecated in Java")
    override fun onBackPressed() {
        if (webView.canGoBack()) {
            // WebView可以返回，执行返回操作
            webView.goBack()
            WSKLog.d(TAG, "onBackPressed -> WebView返回上一页")
        } else {
            // WebView不能返回，显示URL选择界面
            showUrlListDialog()
            WSKLog.d(TAG, "onBackPressed -> 显示URL选择界面")
        }
    }

    /**
     * 应用屏幕方向设置
     * @param landscape 是否为横屏
     */
    private fun applyScreenOrientation(landscape: Boolean) {
        requestedOrientation = if (landscape) {
            android.content.pm.ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
        } else {
            android.content.pm.ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
        }
    }

    /**
     * 保存屏幕方向设置
     * @param landscape 是否为横屏
     */
    private fun saveScreenOrientation(landscape: Boolean) {
        val prefs = getSharedPreferences("WSKRecorder", MODE_PRIVATE)
        prefs.edit().putBoolean("is_landscape", landscape).apply()
        WSKLog.d(TAG, "saveScreenOrientation -> $landscape")
    }

    /**
     * 获取保存的屏幕方向设置
     * @return 是否为横屏
     */
    private fun getSavedScreenOrientation(): Boolean {
        val prefs = getSharedPreferences("WSKRecorder", MODE_PRIVATE)
        val isLandscape = prefs.getBoolean("is_landscape", false)
        WSKLog.d(TAG, "getSavedScreenOrientation -> $isLandscape")
        return isLandscape
    }

    companion object {
        private const val REQUEST_SCRIPT: Int = 1001
        private val Int.dp: Int
            get() = (this * Resources.getSystem().displayMetrics.density).toInt()
    }
}
