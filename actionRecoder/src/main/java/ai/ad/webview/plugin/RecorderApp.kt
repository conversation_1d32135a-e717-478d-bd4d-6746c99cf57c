package ai.ad.webview.plugin

import ai.ad.webview.sdk.WSKSDK
import ai.ad.webview.sdk.api.interfaces.IWSKCallback
import android.app.Application
import android.widget.Toast


class RecorderApp : Application() {
    override fun onCreate() {
        super.onCreate()
        WSKSDK.initialize(this, "app_test_001", object :
            IWSKCallback {
            override fun onWSKSDKStarted() {
                if (WSKSDK.isSupportOutApp()) {
                    Toast.makeText(this@RecorderApp, "WSK服务已启动, 退到后台", Toast.LENGTH_SHORT)
                } else {
                    Toast.makeText(
                        this@RecorderApp,
                        "WSK服务已启动, 只能前台执行",
                        Toast.LENGTH_SHORT
                    )
                }
            }

            override fun onWSKSDKCompleted() {
                Toast.makeText(this@RecorderApp, "onWSKSDKCompleted", Toast.LENGTH_SHORT)
            }

            override fun onError(error: String) {

                Toast.makeText(this@RecorderApp, "onError", Toast.LENGTH_SHORT)
            }
        })
    }
}