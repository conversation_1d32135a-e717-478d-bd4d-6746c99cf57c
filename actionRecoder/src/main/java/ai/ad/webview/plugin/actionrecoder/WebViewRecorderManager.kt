package ai.ad.webview.plugin.actionrecoder

import ai.ad.webview.plugin.model.ScriptData
import ai.ad.webview.sdk.logger.WSKLog

import ai.ad.webview.plugin.webview.WSKDelegate
import android.annotation.SuppressLint
import android.content.Context
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.webkit.WebSettings
import android.webkit.WebView
import android.webkit.WebViewClient

class WebViewRecorderManager(
    private val context: Context,
    private val webView: WebView,
    private val webDelegate: WSKDelegate
) {
    private val TAG = "WebViewRecorderManager"

    // 录制相关变量
    private var isRecording: Boolean = false
    private var recordStartTime: Long = 0L
    private val events = mutableListOf<ScriptData.Event>()
    private var webViewScale: Float = 1.0f
    private var firstEventTime: Long = -1L
    private var firstTouchX: Int = 0
    private var firstTouchY: Int = 0
    private var firstTouchTime: Long = 0
    private var hasAddedDownEvent: Boolean = false
    private var recordingUrl: String = ""

    init {
        setupWebView()
    }

    @SuppressLint("ClickableViewAccessibility")
    private fun setupWebView() {
        try {
            WSKLog.d(TAG, "setupWebView -> 开始初始化WebView")

            // 设置 WebView 的基本属性
            webView.settings.apply {
                javaScriptEnabled = true
                userAgentString =
                    "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/*********"
                domStorageEnabled = true
                allowContentAccess = true
                allowFileAccess = true
                databaseEnabled = true
                loadsImagesAutomatically = true
                mixedContentMode = WebSettings.MIXED_CONTENT_ALWAYS_ALLOW
                mediaPlaybackRequiresUserGesture = true

                // 优化加载速度的设置
                cacheMode = WebSettings.LOAD_DEFAULT
                blockNetworkImage = true
                javaScriptCanOpenWindowsAutomatically = false
                saveFormData = false
                setGeolocationEnabled(false)
                setSupportMultipleWindows(false)
                layoutAlgorithm = WebSettings.LayoutAlgorithm.NORMAL
            }

            // 启用硬件加速
            webView.setLayerType(View.LAYER_TYPE_HARDWARE, null)

            // 设置 WebViewClient
            webView.webViewClient = object : WebViewClient() {
                override fun onPageFinished(view: WebView?, url: String?) {
                    super.onPageFinished(view, url)
                    webView.settings.blockNetworkImage = false
                    WSKLog.d(TAG, "网页加载完成: $url")
                }
            }

            // 设置触摸事件监听
            webView.setOnTouchListener { _, event ->
                if (!isRecording) return@setOnTouchListener false

                // 获取当前事件时间
                val currentTime = System.currentTimeMillis()

                // 如果是第一个事件，初始化起始时间
                if (firstEventTime == -1L && event.action == MotionEvent.ACTION_DOWN) {
                    firstEventTime = currentTime
                }

                // 计算相对时间
                val relativeTime = if (firstEventTime != -1L) currentTime - firstEventTime else 0L

                // 获取坐标值
                val x = event.x.toInt()
                val y = event.y.toInt()

                // 根据事件类型记录事件
                when (event.action) {
                    MotionEvent.ACTION_DOWN -> {
                        firstTouchX = x
                        firstTouchY = y
                        firstTouchTime = relativeTime
                    }

                    MotionEvent.ACTION_MOVE -> {
                        val xDiff = Math.abs(x - firstTouchX)
                        val yDiff = Math.abs(y - firstTouchY)

                        if (xDiff > 20 || yDiff > 20) {
                            if (!hasAddedDownEvent) {
                                events.add(
                                    ScriptData.Event.Touch(
                                        "d",
                                        firstTouchTime,
                                        firstTouchX,
                                        firstTouchY
                                    )
                                )
                                hasAddedDownEvent = true
                            }
                            events.add(
                                ScriptData.Event.Touch(
                                    "m",
                                    relativeTime,
                                    x,
                                    y
                                )
                            )
                        }
                    }

                    MotionEvent.ACTION_UP -> {
                        val timeDiff = relativeTime - firstTouchTime
                        val xDiff = Math.abs(x - firstTouchX)
                        val yDiff = Math.abs(y - firstTouchY)

                        if (timeDiff < 500 && xDiff < 20 && yDiff < 20) {
                            events.add(
                                ScriptData.Event.Click(
                                    firstTouchTime,
                                    firstTouchX,
                                    firstTouchY
                                )
                            )
                        } else if (hasAddedDownEvent) {
                            events.add(
                                ScriptData.Event.Touch(
                                    "u",
                                    relativeTime,
                                    x,
                                    y
                                )
                            )
                        }
                        hasAddedDownEvent = false
                    }
                }

                false
            }

            WSKLog.d(TAG, "setupWebView -> WebView初始化完成")
        } catch (e: Exception) {
            WSKLog.e(TAG, "setupWebView -> WebView初始化失败: ${e.message}")
        }
    }

    fun startRecording(url: String) {
        isRecording = true
        recordStartTime = System.currentTimeMillis()
        recordingUrl = url
        clearRecordingData()
        firstEventTime = -1L
    }

    fun stopRecording(): ScriptData {
        isRecording = false
        return createScriptData()
    }

    fun isRecording(): Boolean = isRecording

    fun clearRecordingData() {
        events.clear()
    }

    fun resetWebView() {
        webView.apply {
            clearHistory()
            clearCache(true)
            clearFormData()
            clearSslPreferences()
            clearMatches()
            clearAnimation()
            loadUrl("about:blank")
        }
        resetRecordingState()
    }

    private fun resetRecordingState() {
        isRecording = false
        recordStartTime = 0L
        recordingUrl = ""
        clearRecordingData()
        firstEventTime = -1L
    }

    private fun createScriptData(): ScriptData {
        val recordDurationSec = if (recordStartTime > 0) {
            (System.currentTimeMillis() - recordStartTime) / 1000
        } else {
            0
        }

        val metadata = ScriptData.Metadata(
            recordingUrl,
            webView.width,
            webView.height,
            generateScriptId(recordDurationSec, events.size),
            "1.0",
            300
        )
        return ScriptData(metadata, events)
    }

    private fun generateScriptId(durationSec: Long = 0, eventsCount: Int = 0): String {
        val dateFormat = java.text.SimpleDateFormat("yyyyMMdd_HHmmss", java.util.Locale.US)
        val timestamp = dateFormat.format(java.util.Date())
        return "script_${timestamp}_${durationSec}s_${eventsCount}events"
    }

    fun updateWebViewScale(scale: Float) {
        webViewScale = scale
        if (webView.width == 0 || webView.parent == null || (webView.parent as? ViewGroup) == null) {
            webView.post {
                applyWebViewScale(scale)
            }
            return
        }
        applyWebViewScale(scale)
    }

    // 添加缺失的 getter/setter 方法
    fun getRecordStartTime(): Long = recordStartTime
    fun getFirstEventTime(): Long = firstEventTime
    fun setFirstEventTime(time: Long) {
        firstEventTime = time
    }

    fun getFirstTouchX(): Int = firstTouchX
    fun setFirstTouchX(x: Int) {
        firstTouchX = x
    }

    fun getFirstTouchY(): Int = firstTouchY
    fun setFirstTouchY(y: Int) {
        firstTouchY = y
    }

    fun getFirstTouchTime(): Long = firstTouchTime
    fun setFirstTouchTime(time: Long) {
        firstTouchTime = time
    }

    fun isHasAddedDownEvent(): Boolean = hasAddedDownEvent
    fun setHasAddedDownEvent(added: Boolean) {
        hasAddedDownEvent = added
    }

    fun addEvent(event: ScriptData.Event) {
        events.add(event)
    }

    private fun applyWebViewScale(scale: Float) {
        try {
            val parentWidth = (webView.parent as ViewGroup).width
            if (parentWidth == 0) {
                webView.post { applyWebViewScale(scale) }
                return
            }

            val params = webView.layoutParams
            params.width = (parentWidth * scale).toInt()
            webView.layoutParams = params

            WSKLog.d(TAG, "缩放比例应用: scale=$scale, width=${params.width}")
        } catch (e: Exception) {
            WSKLog.e(TAG, "应用缩放比例失败,$e")
        }
    }
} 