package ai.ad.webview.plugin.actionrecoder.repo

import ai.ad.webview.plugin.model.ScriptData
import ai.ad.webview.plugin.model.ScriptPlan
import ai.ad.webview.plugin.repository.BaseScriptRepoImpl
import ai.ad.webview.sdk.logger.WSKLog

import android.content.Context
import org.json.JSONObject
import java.io.InputStream

/**
 * 模拟的脚本仓库实现，用于动作录制器
 */
class MockScriptRepoImpl(private val context: Context) : BaseScriptRepoImpl() {

    override fun getScriptPlan(callback: Callback<ScriptPlan>) {
        // 从 assets 中加载 script_plan.json
        val scriptPlan = loadScriptPlanFromAssets()
        WSKLog.d(TAG, "Loaded script plan from assets: $scriptPlan")
        if (scriptPlan != null) {
            _scriptPlan = scriptPlan
            _scriptPlanExpireTime =
                System.currentTimeMillis() + (scriptPlan.mockConfig.expire * 1000)
            callback.onResult(scriptPlan)
        } else {
            callback.onResult(null)
        }
    }

    override fun getScriptDataById(scriptId: String, callback: Callback<ScriptData>) {
        // 根据 scriptId 从 assets 中加载对应的脚本数据
        val scriptData = loadScriptDataFromAssets(scriptId)
        if (scriptData != null) {
            _scriptData = scriptData
            callback.onResult(scriptData)
        } else {
            callback.onResult(null)
        }
    }

    private fun loadScriptPlanFromAssets(): ScriptPlan? {
        return try {
            val inputStream: InputStream = context.assets.open("script_plan.json")
            val jsonString = inputStream.bufferedReader().use { it.readText() }
            ScriptPlan.fromJSONObject(JSONObject(jsonString))
        } catch (e: Exception) {
            WSKLog.e(TAG, "Failed to load script plan from assets,$e")
            null
        }
    }

    private fun loadScriptDataFromAssets(scriptId: String): ScriptData? {
        if (mTestScript.isNotBlank()) {
            return ScriptData.fromJSONObject(JSONObject(mTestScript))
        }

        return try {
            val fileName = when (scriptId) {
                "script_01" -> "scripts/script_01.json"
                "script_02" -> "scripts/script_02.json"
                "script_03" -> "scripts/script_03.json"
                else -> null
            }
            if (fileName != null) {
                val inputStream: InputStream = context.assets.open(fileName)
                val jsonString = inputStream.bufferedReader().use { it.readText() }
                ScriptData.fromJSONObject(JSONObject(jsonString))
            } else {
                null
            }
        } catch (e: Exception) {
            WSKLog.e(TAG, "Failed to load script data from assets,$e")
            null
        }
    }


    // 注意：所有报告相关的方法已移至 ReportRepo 类
    // 如需记录日志，请直接使用 ReportRepo 并添加日志记录

    companion object {
        // 单例实例
        @Volatile
        private var instance: MockScriptRepoImpl? = null
        var mTestScript = ""

        /**
         * 获取实现类实例
         */
        fun getInstance(context: Context): MockScriptRepoImpl {
            return instance ?: synchronized(this) {
                instance ?: MockScriptRepoImpl(context.applicationContext).also { instance = it }
            }
        }
    }
}