package ai.ad.webview.plugin.actionrecoder

import ai.ad.webview.sdk.logger.WSKLog

import ai.ad.webview.sdk.actionrecoder.R
import android.os.Bundle
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity

/**
 * 测试Activity，用于显示生命周期状态
 */
class TestActivity : AppCompatActivity() {
    private val TAG = "TestActivity"
    private lateinit var lifecycleStatusTextView: TextView

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_test)

        lifecycleStatusTextView = findViewById(R.id.lifecycleStatusTextView)
        updateLifecycleStatus("onCreate")
        WSKLog.d(TAG, "onCreate")
    }

    override fun onStart() {
        super.onStart()
        updateLifecycleStatus("onStart")
        WSKLog.d(TAG, "onStart")
    }

    override fun onResume() {
        super.onResume()
        updateLifecycleStatus("onResume")
        WSKLog.d(TAG, "onResume")
    }

    override fun onPause() {
        super.onPause()
        updateLifecycleStatus("onPause")
        WSKLog.d(TAG, "onPause")
    }

    override fun onStop() {
        super.onStop()
        updateLifecycleStatus("onStop")
        WSKLog.d(TAG, "onStop")
    }

    override fun onDestroy() {
        super.onDestroy()
        updateLifecycleStatus("onDestroy")
        WSKLog.d(TAG, "onDestroy")
    }

    override fun onRestart() {
        super.onRestart()
        updateLifecycleStatus("onRestart")
        WSKLog.d(TAG, "onRestart")
    }

    /**
     * 更新生命周期状态文本
     */
    private fun updateLifecycleStatus(status: String) {
        lifecycleStatusTextView.text = "当前生命周期状态: $status"
    }
}
