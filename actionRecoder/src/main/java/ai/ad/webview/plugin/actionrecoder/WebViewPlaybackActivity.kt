package ai.ad.webview.plugin.actionrecoder

import ai.ad.webview.plugin.model.ScriptData
import ai.ad.webview.plugin.repository.ScriptRepo
import ai.ad.webview.sdk.logger.WSKLog

import ai.ad.webview.plugin.webview.WSKDelegate
import ai.ad.webview.sdk.WSKSDK
import ai.ad.webview.sdk.actionrecoder.R
import ai.ad.webview.sdk.api.interfaces.IWSKCallback
import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.webkit.WebView
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.cardview.widget.CardView
import com.google.android.material.button.MaterialButton
import com.google.android.material.textfield.TextInputEditText
import org.json.JSONObject

class WebViewPlaybackActivity : AppCompatActivity() {
    private val TAG = "WebViewPlaybackActivity"

    private lateinit var webView: WebView
    private lateinit var webDelegate: WSKDelegate
    private lateinit var scriptManager: ScriptManager

    // UI 组件
    private lateinit var urlInputCard: CardView
    private lateinit var urlEditText: TextInputEditText
    private lateinit var urlConfirmButton: MaterialButton
    private lateinit var refreshButton: MaterialButton
    private lateinit var statusTextView: TextView

    private var scriptData: ScriptData? = null

    companion object {
        private const val EXTRA_SCRIPT_CONTENT = "script_content"
        private const val EXTRA_SCRIPT_ID = "script_id"
        private const val EXTRA_SCREEN_ORIENTATION = "screen_orientation"

        fun start(context: Context, scriptContent: String, scriptId: String, isLandscape: Boolean) {
            val intent = Intent(context, WebViewPlaybackActivity::class.java).apply {
                putExtra(EXTRA_SCRIPT_CONTENT, scriptContent)
                putExtra(EXTRA_SCRIPT_ID, scriptId)
                putExtra(EXTRA_SCREEN_ORIENTATION, isLandscape)
            }
            context.startActivity(intent)
        }
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_webview_playback)

        // 设置屏幕方向
        val isLandscape = intent.getBooleanExtra(EXTRA_SCREEN_ORIENTATION, false)
        requestedOrientation = if (isLandscape) {
            android.content.pm.ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
        } else {
            android.content.pm.ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
        }

        // 初始化视图
        initializeViews()

        WSKSDK.initialize(this.application, "test_record", object : IWSKCallback {
            override fun onWSKSDKStarted() {
            }

            override fun onWSKSDKCompleted() {
            }

            override fun onError(error: String?) {
            }
        })
        ScriptRepo.updatePlanAndSelectScriptData(this) { succeed ->
            Toast.makeText(
                this@WebViewPlaybackActivity,
                "updatePlanAndSelectScriptData $succeed",
                Toast.LENGTH_SHORT
            )

            // 获取传递的脚本数据
            val scriptContent = intent.getStringExtra(EXTRA_SCRIPT_CONTENT)
            val scriptId = intent.getStringExtra(EXTRA_SCRIPT_ID)

            if (scriptContent != null) {
                try {
                    scriptData = ScriptData.fromJSONObject(JSONObject(scriptContent))
                    if (scriptData != null) {
                        startPlayback()
                    } else {
                        showToast("解析脚本数据失败")
                        finish()
                    }
                } catch (e: Exception) {
                    WSKLog.e(TAG, "解析脚本数据失败: ${e.message}")
                    showToast("解析脚本数据失败: ${e.message}")
                    finish()
                }
            } else {
                showToast("未找到脚本数据")
                finish()
            }
        }


    }

    private fun initializeViews() {
        scriptManager = ScriptManager(this)
        webView = findViewById(R.id.webView)
        webDelegate = WSKDelegate(this, webView)

        // 初始化UI组件
        urlInputCard = findViewById(R.id.urlInputCard)
        urlEditText = findViewById(R.id.urlEditText)
        urlConfirmButton = findViewById(R.id.urlConfirmButton)
        refreshButton = findViewById(R.id.refreshButton)
        statusTextView = findViewById(R.id.statusTextView)

        // 设置按钮点击事件
        setupButtons()
    }

    private fun setupButtons() {
        refreshButton.setOnClickListener {
            resetWebView()
            urlInputCard.visibility = View.GONE
        }

        urlConfirmButton.setOnClickListener {
            val url = urlEditText.text.toString()
            if (url.isNotEmpty()) {
                webView.loadUrl(url)
                showToast("URL已更新: $url")
                urlInputCard.visibility = View.GONE
            }
        }
    }

    private fun startPlayback() {
        scriptData?.let { data ->
            // 更新状态
            updateStatus("开始回放脚本: ${data.metadata.id}")

            // 设置脚本完成监听器
            webDelegate.setScriptCompletionListener(object : WSKDelegate.ScriptCompletionListener {
                override fun onScriptCompleted() {
                    WSKLog.d(TAG, "onScriptCompleted -> 脚本执行完成")
                    runOnUiThread {
                        updateStatus("脚本执行完成")
                        showToast("脚本执行完成")
                    }
                }

                override fun onScriptFailed(errorMessage: String) {
                    WSKLog.e(TAG, "onScriptFailed -> 脚本执行失败: $errorMessage")
                    runOnUiThread {
                        updateStatus("脚本执行失败: $errorMessage")
                        showToast("脚本执行失败: $errorMessage")
                    }
                }
            })

            // 执行脚本
            webDelegate.executeScriptData(data)
        }
    }

    private fun resetWebView() {
        webView.apply {
            clearHistory()
            clearCache(true)
            clearFormData()
            clearSslPreferences()
            clearMatches()
            clearAnimation()
            loadUrl("about:blank")
        }
        showToast("正在重新加载页面...")
    }

    private fun updateStatus(message: String) {
        statusTextView.text = message
    }

    private fun showToast(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
    }

//    override fun onDestroy() {
//        super.onDestroy()
//        try {
//            Logger.d(TAG, "onDestroy -> 释放资源")
//            if (::webDelegate.isInitialized) {
//                webDelegate.release()
//            }
//            Logger.d(TAG, "onDestroy -> 资源释放完成")
//        } catch (e: Exception) {
//            Logger.e(TAG, "onDestroy -> 释放资源时发生异常: ${e.message}")
//        }
//    }
} 