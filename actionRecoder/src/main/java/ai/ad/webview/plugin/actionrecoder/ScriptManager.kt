package ai.ad.webview.plugin.actionrecoder

import ai.ad.webview.plugin.model.ScriptData
import ai.ad.webview.sdk.logger.WSKLog

import android.content.Context
import android.content.Intent
import androidx.core.content.FileProvider
import org.json.JSONArray
import org.json.JSONObject
import java.io.File
import java.net.URL
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

class ScriptManager(private val context: Context) {

    private val scriptsDir: File
        get() {
            val dir = File(context.filesDir, "scripts_list")
            WSKLog.d(TAG, "Scripts directory path: ${dir.absolutePath}")
            WSKLog.d(TAG, "Scripts directory exists: ${dir.exists()}")
            if (!dir.exists()) {
                val created = dir.mkdirs()
                WSKLog.d(TAG, "Create scripts directory: $created")
            }
            return dir
        }

    fun saveScript(scriptData: ScriptData): File {
        val url = scriptData.metadata.url

        // 提取主机名（hostname）
        val hostname = try {
            val urlObj = URL(url)
            urlObj.host.replace("www.", "").split(".")[0]
        } catch (e: Exception) {
            WSKLog.e(TAG, "Failed to parse URL: $url, ${e.message}")
            url.replace(Regex("[^a-zA-Z0-9]"), "_").take(10)
        }

        // 计算录制时长（秒）
        val recordDurationSec = if (scriptData.events.isNotEmpty()) {
            val lastEventTime = scriptData.events.last().time
            (lastEventTime / 1000) + 1 // 转换为秒并确保至少为1秒
        } else {
            0
        }

        // 获取事件数量
        val eventsCount = scriptData.events.size

        // 获取当前时间，精确到分钟
        val currentTime = SimpleDateFormat("yyyyMMdd_HHmm", Locale.getDefault()).format(Date())

        // 创建新的文件名格式：hostname+当前时间+录制时长+事件数.json
        val fileName = "${hostname}_${currentTime}_${recordDurationSec}s_${eventsCount}events.json"
        val file = File(scriptsDir, fileName)
        WSKLog.d(TAG, "Saving script to: ${file.absolutePath}")

        try {
            val jsonContent = scriptData.toJSONObject().toString(2)
            file.writeText(jsonContent)
            WSKLog.d(TAG, "Script saved successfully, file size: ${file.length()} bytes")
        } catch (e: Exception) {
            WSKLog.e(TAG, "Failed to save script: ${e.message}")
        }
        return file
    }

    fun loadScript(file: File): ScriptData {
        WSKLog.d(TAG, "Loading script: ${file.absolutePath}")
        try {
            val jsonStr = file.readText()
            WSKLog.d(TAG, "Script content length: ${jsonStr.length}")
            return ScriptData.fromJSONObject(JSONObject(jsonStr))
        } catch (e: Exception) {
            WSKLog.e(TAG, "Failed to load script" + e.message)
            throw e
        }
    }

    fun getAllScripts(): List<File> {
        WSKLog.d(TAG, "Starting to get all scripts")
        val dir = scriptsDir
        WSKLog.d(TAG, "Scripts directory: ${dir.absolutePath}")

        val files = dir.listFiles()
        WSKLog.d(TAG, "Found files count: ${files?.size ?: 0}")

        val jsonFiles = files?.filter { it.extension == "json" }
        WSKLog.d(TAG, "JSON files count: ${jsonFiles?.size ?: 0}")

        val sortedFiles = jsonFiles?.sortedByDescending { it.lastModified() } ?: emptyList()
        WSKLog.d(TAG, "Sorted file list:")
        sortedFiles.forEach { file ->
            WSKLog.d(
                TAG,
                "- ${file.name} (${file.length()} bytes, last modified: ${Date(file.lastModified())})"
            )
        }

        return sortedFiles
    }

    fun getScriptList(): List<File> {
        return getAllScripts()
    }

    fun shareScript(file: File) {
        WSKLog.d(TAG, "Sharing script: ${file.absolutePath}")
        try {
            // 使用 FileProvider 创建内容 URI
            val fileUri = FileProvider.getUriForFile(
                context,
                "ai.ad.webview.sdk.actionrecoder.fileprovider",
                file
            )

            val intent = Intent(Intent.ACTION_SEND).apply {
                type = "application/json"
                putExtra(Intent.EXTRA_STREAM, fileUri)
                putExtra(Intent.EXTRA_SUBJECT, "分享录制脚本")
                putExtra(Intent.EXTRA_TEXT, "这是一个WebView录制脚本")
                // 授予临时读取权限
                addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
            }

            // 创建选择器并启动
            val chooserIntent = Intent.createChooser(intent, "分享脚本")
            chooserIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            context.startActivity(chooserIntent)

            WSKLog.d(TAG, "分享脚本成功启动")
        } catch (e: Exception) {
            WSKLog.e(TAG, "分享脚本失败: ${e.message}")
            // 尝试使用备用方法
            try {
                val backupIntent = Intent(Intent.ACTION_SEND).apply {
                    type = "text/plain"
                    putExtra(Intent.EXTRA_SUBJECT, "分享录制脚本")
                    putExtra(Intent.EXTRA_TEXT, file.readText())
                }
                backupIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                context.startActivity(Intent.createChooser(backupIntent, "分享脚本内容"))
                WSKLog.d(TAG, "使用备用方法分享脚本内容")
            } catch (e2: Exception) {
                WSKLog.e(TAG, "备用分享方法也失败: ${e2.message}")
            }
        }
    }

    /**
     * Find script file by URL
     */
    fun findScriptByUrl(url: String): File? {
        val scripts = getAllScripts()
        for (file in scripts) {
            try {
                val scriptData = loadScript(file)
                if (scriptData.metadata.url == url) {
                    return file
                }
            } catch (e: Exception) {
                WSKLog.e(TAG, "Failed to read script: ${file.name}" + e.message)
            }
        }
        return null
    }

    /**
     * Get all scripts with unique URLs
     */
    fun getScriptsByUrl(): Map<String, File> {
        val urlToFileMap = mutableMapOf<String, File>()
        getAllScripts().forEach { file ->
            try {
                val scriptData = loadScript(file)
                val url = scriptData.metadata.url
                // If there's already a script with the same URL, keep the newest one
                if (!urlToFileMap.containsKey(url) || file.lastModified() > urlToFileMap[url]!!.lastModified()) {
                    urlToFileMap[url] = file
                }
            } catch (e: Exception) {
                WSKLog.e(TAG, "Failed to read script: ${file.name}" + e.message)
            }
        }
        return urlToFileMap
    }

    /**
     * Get script URL
     */
    fun getScriptUrl(file: File): String {
        try {
            val scriptData = loadScript(file)
            return scriptData.metadata.url
        } catch (e: Exception) {
            WSKLog.e(TAG, "Failed to get script URL: ${file.name}" + e.message)
            return ""
        }
    }

    companion object {
        const val TAG = "ScriptManager"

        @Deprecated(
            "Use new ScriptData.fromJSONObject method",
            ReplaceWith("ScriptData.fromJSONObject(jsonObject)")
        )
        fun fromJSONObject(jsonObject: JSONObject): ScriptData {
            // Adapt old JSON format to new ScriptData structure

            // Parse metadata
            val metadataObj = jsonObject.optJSONObject("m") ?: jsonObject.optJSONObject("metadata")
            ?: JSONObject()

            // Handle URL, which may be in different locations
            val url = jsonObject.optString(
                "url",
                jsonObject.optString("u", metadataObj.optString("url", ""))
            )

            // Create new metadata
            val metadata = ScriptData.Metadata(
                url,
                metadataObj.optInt("w", 1080),
                metadataObj.optInt("h", 1920),
                "",
                metadataObj.optString("version", "1.0"),
                metadataObj.optInt("timeout", 300)
            )

            // Parse events array
            val eventsArray =
                jsonObject.optJSONArray("events") ?: jsonObject.optJSONArray("e") ?: JSONArray()
            val events = mutableListOf<ScriptData.Event>()

            for (i in 0 until eventsArray.length()) {
                val eventObj = eventsArray.getJSONObject(i)

                // Handle different event type formats
                val actionType = eventObj.optString("type", eventObj.optString("a", ""))
                val time = eventObj.optLong("time", eventObj.optLong("t", 0))
                val x = eventObj.optInt("x", 0)
                val y = eventObj.optInt("y", 0)

                val event = when {
                    actionType == "click" || actionType == "c" -> {
                        ScriptData.Event.Click(time, x, y)
                    }

                    actionType == "touch" || actionType == "d" || actionType == "m" || actionType == "u" -> {
                        val touchAction = eventObj.optString("action", actionType)
                        ScriptData.Event.Touch(
                            if (touchAction == "touch") "d" else touchAction, // Convert "touch" to "d"
                            time,
                            x,
                            y
                        )
                    }

                    else -> null
                }

                event?.let { events.add(it) }
            }

            return ScriptData(metadata, events)
        }
    }
}