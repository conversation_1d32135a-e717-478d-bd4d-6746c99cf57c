package ai.ad.webview.plugin.actionrecoder

import ai.ad.webview.sdk.actionrecoder.R
import android.app.Dialog
import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context.CLIPBOARD_SERVICE
import android.content.Intent
import android.graphics.Color
import android.graphics.Typeface
import android.graphics.drawable.GradientDrawable
import android.text.Spannable
import android.text.SpannableString
import android.text.style.BackgroundColorSpan
import android.text.style.ForegroundColorSpan
import android.text.style.StyleSpan
import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.widget.Button
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import java.io.File
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

class ScriptListActivity : AppCompatActivity() {
    private lateinit var scriptManager: ScriptManager
    private lateinit var recyclerView: RecyclerView
    private lateinit var adapter: ScriptAdapter

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        scriptManager = ScriptManager(this)

        // 创建主布局为ConstraintLayout
        val rootLayout =
            ConstraintLayout(this).apply {
                layoutParams =
                    ViewGroup.LayoutParams(
                        ViewGroup.LayoutParams.MATCH_PARENT,
                        ViewGroup.LayoutParams.MATCH_PARENT
                    )
                setBackgroundColor(Color.parseColor("#21262D")) // GitHub深色背景
            }

        // 标题文本
        val titleText =
            TextView(this).apply {
                id = View.generateViewId()
                layoutParams =
                    ConstraintLayout.LayoutParams(
                        ConstraintLayout.LayoutParams.MATCH_PARENT,
                        ConstraintLayout.LayoutParams.WRAP_CONTENT
                    )
                setPadding(24, 16, 24, 16)
                text = "本地脚本列表"
                textSize = 18f
                setTextColor(Color.WHITE)
                setBackgroundColor(Color.parseColor("#161B22"))
            }

        recyclerView =
            RecyclerView(this).apply {
                id = View.generateViewId()
                layoutParams =
                    ConstraintLayout.LayoutParams(
                        ConstraintLayout.LayoutParams.MATCH_PARENT,
                        0 // 高度由约束决定
                    )
                layoutManager = LinearLayoutManager(this@ScriptListActivity)
            }

        // 将控件添加到布局
        rootLayout.addView(titleText)
        rootLayout.addView(recyclerView)

        // 设置约束
        val titleParams = titleText.layoutParams as ConstraintLayout.LayoutParams
        titleParams.topToTop = ConstraintLayout.LayoutParams.PARENT_ID
        titleParams.startToStart = ConstraintLayout.LayoutParams.PARENT_ID
        titleParams.endToEnd = ConstraintLayout.LayoutParams.PARENT_ID
        titleText.layoutParams = titleParams

        val recyclerParams = recyclerView.layoutParams as ConstraintLayout.LayoutParams
        recyclerParams.topToBottom = titleText.id
        recyclerParams.startToStart = ConstraintLayout.LayoutParams.PARENT_ID
        recyclerParams.endToEnd = ConstraintLayout.LayoutParams.PARENT_ID
        recyclerParams.bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID
        recyclerView.layoutParams = recyclerParams

        adapter =
            ScriptAdapter(scriptManager.getScriptList()) { file, action ->
                when (action) {
                    ScriptAction.VIEW -> viewScript(file)
                    ScriptAction.PLAYBACK -> playbackScript(file)
                    ScriptAction.SHARE -> scriptManager.shareScript(file)
                }
            }

        recyclerView.adapter = adapter
        setContentView(rootLayout)
    }

    private fun viewScript(file: File) {
        val script = scriptManager.loadScript(file)
        val dialog =
            AlertDialog.Builder(this, R.style.AlertDialogTheme)
                .setTitle("脚本内容")
                .setMessage(script.toJSONObject().toString(2))
                .setPositiveButton("确定", null)
                .create()


        dialog.show()
    }

    private fun playbackScript(file: File) {
        try {
            val script = scriptManager.loadScript(file)

            // 保存当前选中的脚本文件路径，用于分享功能
            getSharedPreferences("WSKRecorder", MODE_PRIVATE).edit()
                .putString("selected_script_path", file.absolutePath)
                .apply()

            val resultIntent =
                Intent().apply {
                    putExtra("script_content", script.toJSONObject().toString())
                    putExtra("script_id", script.metadata.id)
                    putExtra("script_file_path", file.absolutePath)
                }
            setResult(RESULT_OK, resultIntent)
            finish()
        } catch (e: Exception) {
            Toast.makeText(this, "加载脚本失败: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }
}

enum class ScriptAction {
    VIEW,
    PLAYBACK,
    SHARE
}

private class ScriptAdapter(
    private val scripts: List<File>,
    private val onAction: (File, ScriptAction) -> Unit
) : RecyclerView.Adapter<ScriptAdapter.ViewHolder>() {

    class ViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        val titleText: TextView = (view as TextView)
        val actionView: View = view
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val textView =
            TextView(parent.context).apply {
                layoutParams =
                    ViewGroup.LayoutParams(
                        ViewGroup.LayoutParams.MATCH_PARENT,
                        ViewGroup.LayoutParams.WRAP_CONTENT
                    )
                setPadding(24, 24, 24, 24)
                textSize = 14f
                setTextColor(Color.WHITE)
                setLineSpacing(6f, 1f) // 增加行间距

                // 创建更明显的分隔线和背景
                val background =
                    GradientDrawable().apply {
                        setColor(Color.parseColor("#2D3239")) // 稍微亮一点的背景色
                        setStroke(1, Color.parseColor("#4D5D6D")) // 更明显的边框
                        cornerRadius = 8f // 添加圆角
                    }
                this.background = background

                // 设置边距，让项目之间有明显间隔
                val params =
                    ViewGroup.MarginLayoutParams(
                        ViewGroup.LayoutParams.MATCH_PARENT,
                        ViewGroup.LayoutParams.WRAP_CONTENT
                    )
                params.setMargins(8, 4, 8, 4)
                layoutParams = params
            }
        return ViewHolder(textView)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val file = scripts[position]
        val date = Date(file.lastModified())
        val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())

        // 获取脚本URL
        val scriptManager = ScriptManager(holder.actionView.context)
        val url = scriptManager.getScriptUrl(file)

        // 创建带有样式的文本
        val fullText = SpannableString("文件名: ${file.name}\nURL: ${url}\n录制时间: ${dateFormat.format(date)}")

        // 设置文件名部分的样式 - 确保正确计算长度
        val filenamePrefix = "文件名: "
        val filenameStart = fullText.indexOf(filenamePrefix) + filenamePrefix.length
        val filenameEnd = filenameStart + file.name.length

        // 设置文件名为粗体
        fullText.setSpan(
            StyleSpan(Typeface.BOLD),
            filenameStart, filenameEnd,
            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
        )

        // 设置文件名为亮色
        fullText.setSpan(
            ForegroundColorSpan(Color.parseColor("#2F81F7")),
            filenameStart, filenameEnd,
            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
        )

        // 添加背景高亮
        fullText.setSpan(
            BackgroundColorSpan(Color.parseColor("#1A2F81F7")), // 10% 透明度的蓝色背景
            filenameStart, filenameEnd,
            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
        )

        // 设置文本
        holder.titleText.text = fullText

        holder.actionView.setOnClickListener {
            // 使用自定义布局创建对话框
            val dialogView = LayoutInflater.from(holder.actionView.context)
                .inflate(R.layout.dialog_script_actions, null)

            // 创建对话框
            val dialog = Dialog(holder.actionView.context)
            dialog.requestWindowFeature(Window.FEATURE_NO_TITLE)
            dialog.setContentView(dialogView)

            // 设置对话框标题为文件名
            val dialogTitle = dialogView.findViewById<TextView>(R.id.dialogTitle)
            dialogTitle.text = "脚本操作: ${file.name}"
            dialog.window?.apply {
                // 设置对话框背景为透明，让自定义布局的背景显示出来
                setBackgroundDrawableResource(android.R.color.transparent)
                // 设置对话框宽度为屏幕宽度的90%
                val width = (holder.actionView.context.resources.displayMetrics.widthPixels * 0.9).toInt()
                setLayout(width, ViewGroup.LayoutParams.WRAP_CONTENT)
                // 设置动画
                setWindowAnimations(android.R.style.Animation_Dialog)
                // 设置对话框显示位置
                setGravity(android.view.Gravity.CENTER)
            }

            // 设置点击事件
            dialogView.findViewById<TextView>(R.id.actionView).setOnClickListener {
                onAction(file, ScriptAction.VIEW)
                dialog.dismiss()
            }

            dialogView.findViewById<TextView>(R.id.actionPlayback).setOnClickListener {
                onAction(file, ScriptAction.PLAYBACK)
                dialog.dismiss()
            }

            dialogView.findViewById<TextView>(R.id.actionShare).setOnClickListener {
                onAction(file, ScriptAction.SHARE)
                dialog.dismiss()
            }

            dialogView.findViewById<TextView>(R.id.actionCopy).setOnClickListener {
                try {
                    val script = scriptManager.loadScript(file)
                    val clipboard = holder.actionView.context.getSystemService(CLIPBOARD_SERVICE) as ClipboardManager
                    val clip = ClipData.newPlainText("script", script.toJSONObject().toString(2))
                    clipboard.setPrimaryClip(clip)
                    Toast.makeText(
                        holder.actionView.context,
                        "脚本已复制到剪贴板",
                        Toast.LENGTH_SHORT
                    ).show()
                } catch (e: Exception) {
                    Toast.makeText(
                        holder.actionView.context,
                        "复制失败: ${e.message}",
                        Toast.LENGTH_SHORT
                    ).show()
                }
                dialog.dismiss()
            }

            // 添加复制文件名的点击事件
            dialogView.findViewById<TextView>(R.id.actionCopyFilename).setOnClickListener {
                try {
                    val clipboard = holder.actionView.context.getSystemService(CLIPBOARD_SERVICE) as ClipboardManager
                    val clip = ClipData.newPlainText("filename", file.name)
                    clipboard.setPrimaryClip(clip)
                    Toast.makeText(
                        holder.actionView.context,
                        "文件名已复制到剪贴板",
                        Toast.LENGTH_SHORT
                    ).show()
                } catch (e: Exception) {
                    Toast.makeText(
                        holder.actionView.context,
                        "复制失败: ${e.message}",
                        Toast.LENGTH_SHORT
                    ).show()
                }
                dialog.dismiss()
            }

            // 设置取消按钮
            dialogView.findViewById<Button>(R.id.btnCancel).setOnClickListener {
                dialog.dismiss()
            }

            // 显示对话框
            dialog.show()
        }
    }

    override fun getItemCount() = scripts.size
}
