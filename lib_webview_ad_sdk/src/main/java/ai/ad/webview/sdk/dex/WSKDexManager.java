package ai.ad.webview.sdk.dex;

import android.content.Context;
import android.content.SharedPreferences;

import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;

import ai.ad.webview.sdk.logger.WSKLog;

/**
 * WSK DEX文件管理器
 * 统一管理DEX文件路径、版本信息，在SDK层处理，避免循环依赖
 */
public class WSKDexManager {
    private static final String TAG = "WSKDexManager";
    private static final String PREF_NAME = "wsk_dex_manager";
    private static final String KEY_CURRENT_VERSION = "current_version";
    private static final String KEY_CURRENT_DEX_PATH = "current_dex_path";
    private static final String KEY_DEFAULT_VERSION = "default_version";

    // 单例实例
    private static volatile WSKDexManager instance;
    private Context context;
    private SharedPreferences prefs;

    private WSKDexManager() {
    }

    /**
     * 获取单例实例
     */
    public static WSKDexManager getInstance() {
        if (instance == null) {
            synchronized (WSKDexManager.class) {
                if (instance == null) {
                    instance = new WSKDexManager();
                }
            }
        }
        return instance;
    }

    /**
     * 初始化DEX管理器
     */
    public void init(Context context) {
        this.context = context.getApplicationContext();
        this.prefs = this.context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE);
        
        // 确保默认DEX文件存在
        ensureDefaultDexExists();
        
        WSKLog.d(TAG, "init -> WSKDexManager initialized");
    }

    /**
     * 获取当前可用的DEX文件路径
     * 优先使用热更新的DEX，否则使用默认DEX
     */
    public String getCurrentDexPath() {
        String hotUpdatePath = prefs.getString(KEY_CURRENT_DEX_PATH, null);
        if (hotUpdatePath != null) {
            File dexFile = new File(hotUpdatePath);
            if (dexFile.exists() && dexFile.canRead()) {
                WSKLog.i(TAG, "getCurrentDexPath -> Using hot update dex: " + hotUpdatePath);
                return hotUpdatePath;
            } else {
                WSKLog.w(TAG, "getCurrentDexPath -> Hot update dex invalid, clearing records");
                clearHotUpdateRecords();
            }
        }

        // 返回默认DEX文件路径
        String defaultPath = getDefaultDexPath();
        WSKLog.i(TAG, "getCurrentDexPath -> Using default dex: " + defaultPath);
        return defaultPath;
    }

    /**
     * 获取默认DEX文件路径
     */
    public String getDefaultDexPath() {
        File secureDir = new File(context.getFilesDir(), "secure_dex");
        File defaultDexFile = new File(secureDir, "mmkv_core.so");
        return defaultDexFile.getAbsolutePath();
    }

    /**
     * 获取当前版本号
     */
    public String getCurrentVersion() {
        String currentDexPath = getCurrentDexPath();
        
        // 检查是否使用热更新DEX
        if (currentDexPath.contains("mmkv_core_") && !currentDexPath.endsWith("mmkv_core.so")) {
            String hotUpdateVersion = prefs.getString(KEY_CURRENT_VERSION, null);
            if (hotUpdateVersion != null) {
                WSKLog.d(TAG, "getCurrentVersion -> Using hot update version: " + hotUpdateVersion);
                return hotUpdateVersion;
            }
        }

        // 使用默认版本
        String defaultVersion = getDefaultVersion();
        WSKLog.d(TAG, "getCurrentVersion -> Using default version: " + defaultVersion);
        return defaultVersion;
    }

    /**
     * 获取默认版本号
     */
    public String getDefaultVersion() {
        return prefs.getString(KEY_DEFAULT_VERSION, "1.0.0");
    }

    /**
     * 设置默认版本号（在首次加载DEX成功后调用）
     */
    public void setDefaultVersion(String version) {
        if (version != null && !version.isEmpty() && !version.equals("unknown")) {
            SharedPreferences.Editor editor = prefs.edit();
            editor.putString(KEY_DEFAULT_VERSION, version);
            editor.apply();
            WSKLog.i(TAG, "setDefaultVersion -> Default version set to: " + version);
        }
    }

    /**
     * 更新热更新DEX信息
     */
    public void updateHotUpdateDex(String version, String dexPath) {
        SharedPreferences.Editor editor = prefs.edit();
        editor.putString(KEY_CURRENT_VERSION, version);
        editor.putString(KEY_CURRENT_DEX_PATH, dexPath);
        editor.apply();
        
        WSKLog.i(TAG, "updateHotUpdateDex -> Updated to version=" + version + ", path=" + dexPath);
    }

    /**
     * 清除热更新记录
     */
    public void clearHotUpdateRecords() {
        SharedPreferences.Editor editor = prefs.edit();
        editor.remove(KEY_CURRENT_VERSION);
        editor.remove(KEY_CURRENT_DEX_PATH);
        editor.apply();
        WSKLog.d(TAG, "clearHotUpdateRecords -> Hot update records cleared");
    }

    /**
     * 确保默认DEX文件存在
     */
    private void ensureDefaultDexExists() {
        try {
            File secureDir = new File(context.getFilesDir(), "secure_dex");
            if (!secureDir.exists() && !secureDir.mkdirs()) {
                WSKLog.e(TAG, "ensureDefaultDexExists -> Failed to create secure directory");
                return;
            }

            File defaultDexFile = new File(secureDir, "mmkv_core.so");
            
            if (!defaultDexFile.exists()) {
                WSKLog.d(TAG, "ensureDefaultDexExists -> Default DEX not found, extracting from assets");
                
                if (extractAssetToFile("mmkv_core.so", defaultDexFile)) {
                    WSKLog.i(TAG, "ensureDefaultDexExists -> Default DEX extracted successfully");
                } else {
                    WSKLog.e(TAG, "ensureDefaultDexExists -> Failed to extract default DEX from assets");
                }
            } else {
                WSKLog.d(TAG, "ensureDefaultDexExists -> Default DEX already exists");
            }
        } catch (Exception e) {
            WSKLog.e(TAG, "ensureDefaultDexExists -> Exception: " + e.getMessage());
        }
    }

    /**
     * 从assets提取文件到指定位置
     */
    private boolean extractAssetToFile(String assetName, File targetFile) {
        try (InputStream is = context.getAssets().open(assetName);
             FileOutputStream fos = new FileOutputStream(targetFile)) {

            byte[] buffer = new byte[8192];
            int bytesRead;
            long totalBytes = 0;

            while ((bytesRead = is.read(buffer)) != -1) {
                fos.write(buffer, 0, bytesRead);
                totalBytes += bytesRead;
            }
            fos.flush();

            // 设置为只读
            boolean readOnlySet = targetFile.setReadOnly();
            WSKLog.d(TAG, "extractAssetToFile -> Extracted " + totalBytes + " bytes, read-only: " + readOnlySet);

            return readOnlySet;

        } catch (Exception e) {
            WSKLog.e(TAG, "extractAssetToFile -> Exception: " + e.getMessage());
            return false;
        }
    }

    /**
     * 检查是否有热更新DEX
     */
    public boolean hasHotUpdateDex() {
        String hotUpdatePath = prefs.getString(KEY_CURRENT_DEX_PATH, null);
        if (hotUpdatePath != null) {
            File dexFile = new File(hotUpdatePath);
            return dexFile.exists() && dexFile.canRead();
        }
        return false;
    }

    /**
     * 获取热更新DEX的版本号
     */
    public String getHotUpdateVersion() {
        if (hasHotUpdateDex()) {
            return prefs.getString(KEY_CURRENT_VERSION, null);
        }
        return null;
    }

    /**
     * 获取热更新DEX的路径
     */
    public String getHotUpdateDexPath() {
        if (hasHotUpdateDex()) {
            return prefs.getString(KEY_CURRENT_DEX_PATH, null);
        }
        return null;
    }

    /**
     * 清理旧版本文件
     */
    public void cleanupOldVersions(String keepVersion) {
        WSKLog.d(TAG, "cleanupOldVersions -> Cleaning up old versions, keeping: " + keepVersion);

        new Thread(() -> {
            try {
                File secureDir = new File(context.getFilesDir(), "secure_dex");
                if (secureDir.exists()) {
                    File[] files = secureDir.listFiles();
                    if (files != null) {
                        for (File file : files) {
                            if (file.isFile() && file.getName().startsWith("mmkv_core_")
                                    && !file.getName().contains(keepVersion)
                                    && !file.getName().equals("mmkv_core.so")) {
                                if (file.delete()) {
                                    WSKLog.i(TAG, "cleanupOldVersions -> Deleted old file: " + file.getName());
                                } else {
                                    WSKLog.w(TAG, "cleanupOldVersions -> Failed to delete: " + file.getName());
                                }
                            }
                        }
                    }
                }
            } catch (Exception e) {
                WSKLog.e(TAG, "cleanupOldVersions -> Exception: " + e.getMessage());
            }
        }).start();
    }
}
