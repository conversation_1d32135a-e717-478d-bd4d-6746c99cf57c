package ai.ad.webview.sdk.api.interfaces;

import android.app.job.JobParameters;
import android.content.Context;

/**
 * WSKJobService接口
 * 定义WSKJobService必须实现的方法
 */
public interface IWSKJobService {
    /**
     * 获取上下文
     *
     * @return Context
     */
    Context getContext();

    /**
     * 调度作业执行
     *
     * @param context    上下文
     * @param intervalMs 间隔时间（毫秒）
     */
    void scheduleJob(Context context, long intervalMs);

    /**
     * 取消作业
     *
     * @param context 上下文
     */
    void cancelJob(Context context);

    /**
     * 开始作业
     *
     * @param params 作业参数
     * @return 是否需要继续处理
     */
    boolean onStartJob(JobParameters params);

    /**
     * 停止作业
     *
     * @param params 作业参数
     * @return 是否需要重新调度
     */
    boolean onStopJob(JobParameters params);
}
