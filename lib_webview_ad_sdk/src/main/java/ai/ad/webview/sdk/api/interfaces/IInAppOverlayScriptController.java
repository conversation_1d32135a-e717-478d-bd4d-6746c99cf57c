package ai.ad.webview.sdk.api.interfaces;

import android.webkit.WebView;

/**
 * 应用内悬浮WebView脚本控制器接口
 * 用于控制WebView中脚本的生命周期
 */
public interface IInAppOverlayScriptController {

    /**
     * 当悬浮窗WebView创建完成时调用
     * 可以在此方法中进行WebView的初始化配置
     *
     * @param webView WebView实例
     */
    void onWebViewCreated(WebView webView);

    /**
     * 当应用进入前台或悬浮窗启用时调用
     * 可以在此方法中恢复脚本执行
     */
    void onResume();

    /**
     * 当应用进入后台时调用
     * 可以在此方法中暂停脚本执行
     */
    void onPause();

    /**
     * 当悬浮窗被禁用时调用
     * 可以在此方法中释放资源
     */
    void onDestroy();
}
