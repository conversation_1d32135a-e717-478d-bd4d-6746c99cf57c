package ai.ad.webview.sdk.api.interfaces;

/**
 * 热更新回调接口
 */
public interface IUpdateCallback {
    /**
     * 发现可用更新
     * @param latestVersion 最新版本号
     * @param patchUrl 下载链接
     * @param patchMd5 文件MD5
     * @param patchSize 文件大小
     */
    void onUpdateAvailable(String latestVersion, String patchUrl, String patchMd5, long patchSize);

    /**
     * 无可用更新
     */
    void onNoUpdate();

    /**
     * 更新成功
     * @param newVersion 新版本号
     * @param newDexPath 新dex文件路径
     */
    void onUpdateSuccess(String newVersion, String newDexPath);

    /**
     * 更新失败
     * @param error 错误信息
     */
    void onUpdateFailed(String error);
}
