package ai.ad.webview.sdk.api.interfaces;

import android.app.Application;

/**
 * 应用内悬浮WebView管理器接口
 * 负责在应用处于前台时，展示一个透明的悬浮WebView（无需权限）
 */
public interface IInAppOverlayManager {

    /**
     * 初始化悬浮窗管理器
     * 必须在Application.onCreate()中调用一次
     *
     * @param application 应用Application实例
     */
    void initialize(Application application);

    /**
     * 启用悬浮窗
     * 若此时应用在前台，则立即展示透明悬浮WebView
     * 后续在应用切换前后台时，自动管理WebView显示/暂停
     */
    void enableOverlay();

    /**
     * 禁用悬浮窗
     * 完全关闭悬浮功能，移除WebView并释放所有资源
     * 后续应用即使切换回前台，也不会再显示，除非再次调用enableOverlay()
     */
    void disableOverlay();


    /**
     * 获取当前悬浮窗是否启用
     *
     * @return 悬浮窗是否启用
     */
    boolean isEnabled();
}
