package ai.ad.webview.sdk.api.interfaces;

import android.content.Context;
import android.content.Intent;

/**
 * WSKService接口
 * 定义WSKService必须实现的方法
 */
public interface IWSKService {
    /**
     * 获取上下文
     *
     * @return Context
     */
    Context getContext();

    /**
     * 处理启动命令
     *
     * @param intent  Intent
     * @param flags   标志
     * @param startId 启动ID
     * @return 结果
     */
    int onStartCommand(Intent intent, int flags, int startId);

    /**
     * 更新脚本计划并继续
     */
    void updateScriptPlanAndProceed();

    /**
     * 尝试创建悬浮窗或降级使用Activity
     */
    void tryCreateOverlayOrFallbackToActivity();

    /**
     * 降级使用WebActivity
     */
    void fallbackToWebActivity();

    /**
     * 安排下一次执行
     */
    void scheduleNextExecution();

    /**
     * 销毁服务
     */
    void destroy();
}
