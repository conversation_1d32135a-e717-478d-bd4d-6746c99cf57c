package ai.ad.webview.sdk.api.interfaces;

import android.app.Activity;
import android.content.Context;
import android.view.Window;

/**
 * WSKActivity接口
 * 定义WSKActivity必须实现的方法
 */
public interface IWSKActivity {

    /**
     * 配置窗口属性
     *
     * @param window 窗口对象
     */
    void configureWindow(Window window);

    /**
     * 获取上下文
     *
     * @return Context
     */
    Context getContext();


    /**
     * 关闭Activity
     */
    void onCreate(Activity activity);

    void onDestroy(Activity activity);

    void onResume(Activity activity);

    void onPause(Activity activity);
}
