package ai.ad.webview.sdk.update;

import android.app.Application;
import android.os.Handler;
import android.os.Looper;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

import ai.ad.webview.sdk.api.interfaces.IUpdateCallback;
import ai.ad.webview.sdk.api.interfaces.IWSKSDK;
import ai.ad.webview.sdk.api.interfaces.IWSKCallback;
import ai.ad.webview.sdk.dex.WSKDexManager;
import ai.ad.webview.sdk.dex.WSKResManager;
import ai.ad.webview.sdk.logger.WSKLog;

/**
 * WSK热更新管理器
 * 负责在SDK初始化时进行热更新检查，确保更新完成后再执行业务逻辑
 */
public class WSKUpdateManager {
    private static final String TAG = "WSKUpdateManager";
    private static final int UPDATE_TIMEOUT_SECONDS = 10; // 热更新超时时间：10秒

    /**
     * 热更新完成回调接口
     */
    public interface UpdateCompleteCallback {
        /**
         * 热更新完成，可以开始业务逻辑
         * @param instance 创建的SDK实例
         * @param isUpdated 是否进行了热更新
         */
        void onUpdateComplete(IWSKSDK instance, boolean isUpdated);

        /**
         * 热更新失败
         * @param error 错误信息
         */
        void onUpdateFailed(String error);
    }

    /**
     * 执行热更新并创建SDK实例
     * 
     * @param application 应用上下文
     * @param appId 应用ID
     * @param callback SDK回调
     * @param updateCallback 热更新完成回调
     */
    public static void initializeWithUpdate(Application application, String appId, 
                                          IWSKCallback callback, UpdateCompleteCallback updateCallback) {
        WSKLog.d(TAG, "initializeWithUpdate -> Starting initialization with hot update");

        try {
            // 1. 初始化WSKDexManager
            WSKDexManager.getInstance().init(application);

            // 2. 创建初始实例用于热更新检查
            IWSKSDK tempInstance = createTempInstance(application, appId, callback);
            if (tempInstance == null) {
                updateCallback.onUpdateFailed("Failed to create temporary instance");
                return;
            }

            // 3. 执行热更新检查（带超时）
            performUpdateWithTimeout(tempInstance, appId, new UpdateResult() {
                @Override
                public void onResult(boolean success, boolean isUpdated, String error) {
                    if (success) {
                        // 热更新完成，创建最终实例
                        IWSKSDK finalInstance = createFinalInstance(application, appId, callback);
                        if (finalInstance != null) {
                            updateCallback.onUpdateComplete(finalInstance, isUpdated);
                        } else {
                            updateCallback.onUpdateFailed("Failed to create final instance");
                        }
                    } else {
                        updateCallback.onUpdateFailed(error);
                    }
                }
            });

        } catch (Exception e) {
            String errorMsg = "Initialize with update failed: " + e.getMessage();
            WSKLog.e(TAG, errorMsg);
            updateCallback.onUpdateFailed(errorMsg);
        }
    }

    /**
     * 创建临时实例用于热更新检查
     */
    private static IWSKSDK createTempInstance(Application application, String appId, IWSKCallback callback) {
        try {
            WSKLog.d(TAG, "createTempInstance -> Creating temporary instance for update check");

            IWSKSDK instance = (IWSKSDK) WSKResManager.getInstance().createObject(
                    application,
                    "default", // 使用当前最新的DEX
                    "ai.ad.webview.plugin.ProxyWSKSDK");

            if (instance != null) {
                // 初始化临时实例（不传递外部callback，避免触发业务逻辑）
                instance.initialize(application, appId, null);

                // 设置默认版本号
                String version = instance.version();
                if (version != null && !version.equals("unknown")) {
                    WSKDexManager.getInstance().setDefaultVersion(version);
                }

                WSKLog.i(TAG, "createTempInstance -> Temporary instance created successfully");
            } else {
                WSKLog.e(TAG, "createTempInstance -> Failed to create temporary instance");
            }

            return instance;
        } catch (Exception e) {
            WSKLog.e(TAG, "createTempInstance -> Exception: " + e.getMessage());
            return null;
        }
    }

    /**
     * 创建最终实例用于业务逻辑
     */
    private static IWSKSDK createFinalInstance(Application application, String appId, IWSKCallback callback) {
        try {
            WSKLog.d(TAG, "createFinalInstance -> Creating final instance for business logic");

            IWSKSDK instance = (IWSKSDK) WSKResManager.getInstance().createObject(
                    application,
                    "default", // 使用最新的DEX（可能已经热更新）
                    "ai.ad.webview.plugin.ProxyWSKSDK");

            if (instance != null) {
                WSKLog.i(TAG, "createFinalInstance -> Final instance created successfully");
            } else {
                WSKLog.e(TAG, "createFinalInstance -> Failed to create final instance");
            }

            return instance;
        } catch (Exception e) {
            WSKLog.e(TAG, "createFinalInstance -> Exception: " + e.getMessage());
            return null;
        }
    }

    /**
     * 执行带超时的热更新检查
     * 确保 deviceId 已获取后再进行更新检查
     */
    private static void performUpdateWithTimeout(IWSKSDK instance, String appId, UpdateResult resultCallback) {
        WSKLog.d(TAG, "performUpdateWithTimeout -> Starting update check with " + UPDATE_TIMEOUT_SECONDS + "s timeout");

        final CountDownLatch latch = new CountDownLatch(1);
        final AtomicBoolean isCompleted = new AtomicBoolean(false);
        final AtomicBoolean isUpdated = new AtomicBoolean(false);
        final StringBuilder errorMsg = new StringBuilder();

        // 在后台线程执行热更新检查
        new Thread(() -> {
            try {
                // 1. 首先确保 deviceId 已经获取到
                ensureDeviceIdReady(instance, new DeviceIdCallback() {
                    @Override
                    public void onDeviceIdReady(String deviceId) {
                        WSKLog.d(TAG, "performUpdateWithTimeout -> DeviceId ready: " + deviceId);
                        // deviceId 准备好后，开始热更新检查
                        startUpdateCheck(instance, latch, isCompleted, isUpdated, errorMsg);
                    }

                    @Override
                    public void onDeviceIdFailed(String error) {
                        WSKLog.w(TAG, "performUpdateWithTimeout -> DeviceId failed: " + error);
                        // deviceId 获取失败，仍然尝试更新检查（使用默认值）
                        startUpdateCheck(instance, latch, isCompleted, isUpdated, errorMsg);
                    }
                });
            } catch (Exception e) {
                WSKLog.e(TAG, "performUpdateWithTimeout -> Exception during deviceId check: " + e.getMessage());
                if (isCompleted.compareAndSet(false, true)) {
                    errorMsg.append("DeviceId check exception: ").append(e.getMessage());
                    latch.countDown();
                }
            }
        }).start();

        // 等待热更新完成或超时
        new Thread(() -> {
            try {
                boolean finished = latch.await(UPDATE_TIMEOUT_SECONDS, TimeUnit.SECONDS);
                
                // 切换到主线程回调结果
                new Handler(Looper.getMainLooper()).post(() -> {
                    if (finished) {
                        if (errorMsg.length() > 0) {
                            WSKLog.w(TAG, "performUpdateWithTimeout -> Update completed with error: " + errorMsg.toString());
                            resultCallback.onResult(true, false, errorMsg.toString()); // 有错误但继续使用当前DEX
                        } else {
                            WSKLog.i(TAG, "performUpdateWithTimeout -> Update completed successfully, isUpdated: " + isUpdated.get());
                            resultCallback.onResult(true, isUpdated.get(), null);
                        }
                    } else {
                        WSKLog.w(TAG, "performUpdateWithTimeout -> Update timeout after " + UPDATE_TIMEOUT_SECONDS + " seconds");
                        resultCallback.onResult(true, false, "Update timeout"); // 超时但继续使用当前DEX
                    }
                });
            } catch (InterruptedException e) {
                WSKLog.e(TAG, "performUpdateWithTimeout -> Wait interrupted: " + e.getMessage());
                new Handler(Looper.getMainLooper()).post(() -> {
                    resultCallback.onResult(false, false, "Update wait interrupted: " + e.getMessage());
                });
            }
        }).start();
    }

    /**
     * 确保 deviceId 已准备好
     */
    private static void ensureDeviceIdReady(IWSKSDK instance, DeviceIdCallback callback) {
        WSKLog.d(TAG, "ensureDeviceIdReady -> Checking deviceId status");

        // 检查 deviceId 是否已经可用
        String deviceId = instance.getDeviceId();
        if (deviceId != null && !deviceId.equals("unknown") && !deviceId.trim().isEmpty()) {
            WSKLog.d(TAG, "ensureDeviceIdReady -> DeviceId already available: " + deviceId);
            callback.onDeviceIdReady(deviceId);
            return;
        }

        WSKLog.d(TAG, "ensureDeviceIdReady -> DeviceId not ready, waiting...");

        // deviceId 还未准备好，等待一段时间后重试
        final int maxRetries = 20; // 最多重试20次
        final int retryInterval = 200; // 每次间隔200ms

        new Thread(() -> {
            for (int i = 0; i < maxRetries; i++) {
                try {
                    Thread.sleep(retryInterval);
                    String currentDeviceId = instance.getDeviceId();
                    if (currentDeviceId != null && !currentDeviceId.equals("unknown") && !currentDeviceId.trim().isEmpty()) {
                        WSKLog.d(TAG, "ensureDeviceIdReady -> DeviceId ready after " + (i + 1) + " retries: " + currentDeviceId);
                        callback.onDeviceIdReady(currentDeviceId);
                        return;
                    }
                } catch (InterruptedException e) {
                    WSKLog.w(TAG, "ensureDeviceIdReady -> Wait interrupted: " + e.getMessage());
                    break;
                }
            }

            // 超时后仍未获取到有效的 deviceId
            String finalDeviceId = instance.getDeviceId();
            WSKLog.w(TAG, "ensureDeviceIdReady -> DeviceId timeout, using current value: " + finalDeviceId);
            if (finalDeviceId != null && !finalDeviceId.trim().isEmpty()) {
                callback.onDeviceIdReady(finalDeviceId);
            } else {
                callback.onDeviceIdFailed("DeviceId timeout after " + (maxRetries * retryInterval) + "ms");
            }
        }).start();
    }

    /**
     * 开始热更新检查
     */
    private static void startUpdateCheck(IWSKSDK instance, CountDownLatch latch,
                                       AtomicBoolean isCompleted, AtomicBoolean isUpdated,
                                       StringBuilder errorMsg) {
        try {
            WSKLog.d(TAG, "startUpdateCheck -> Starting update check");
            instance.checkUpdate(new IUpdateCallback() {
                @Override
                public void onUpdateAvailable(String latestVersion, String patchUrl, String patchMd5, long patchSize) {
                    WSKLog.i(TAG, "startUpdateCheck -> Update available: " + latestVersion);
                    // 更新会自动下载，等待完成
                }

                @Override
                public void onNoUpdate() {
                    WSKLog.d(TAG, "startUpdateCheck -> No update available");
                    if (isCompleted.compareAndSet(false, true)) {
                        isUpdated.set(false);
                        latch.countDown();
                    }
                }

                @Override
                public void onUpdateSuccess(String newVersion, String newDexPath) {
                    WSKLog.i(TAG, "startUpdateCheck -> Update successful: " + newVersion);
                    if (isCompleted.compareAndSet(false, true)) {
                        isUpdated.set(true);
                        latch.countDown();
                    }
                }

                @Override
                public void onUpdateFailed(String error) {
                    WSKLog.w(TAG, "startUpdateCheck -> Update failed: " + error);
                    if (isCompleted.compareAndSet(false, true)) {
                        isUpdated.set(false);
                        errorMsg.append("Update failed: ").append(error);
                        latch.countDown();
                    }
                }
            });
        } catch (Exception e) {
            WSKLog.e(TAG, "startUpdateCheck -> Exception during update check: " + e.getMessage());
            if (isCompleted.compareAndSet(false, true)) {
                errorMsg.append("Update check exception: ").append(e.getMessage());
                latch.countDown();
            }
        }
    }

    /**
     * DeviceId 回调接口
     */
    private interface DeviceIdCallback {
        void onDeviceIdReady(String deviceId);
        void onDeviceIdFailed(String error);
    }

    /**
     * 内部更新结果回调接口
     */
    private interface UpdateResult {
        void onResult(boolean success, boolean isUpdated, String error);
    }
}
