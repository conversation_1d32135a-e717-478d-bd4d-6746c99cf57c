package ai.ad.webview.sdk;

import android.app.Activity;
import android.app.Application;
import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import java.util.UUID;

import ai.ad.webview.sdk.api.interfaces.IUpdateCallback;
import ai.ad.webview.sdk.api.interfaces.IWSKActivity;
import ai.ad.webview.sdk.api.interfaces.IWSKCallback;
import ai.ad.webview.sdk.api.interfaces.IWSKSDK;
import ai.ad.webview.sdk.api.interfaces.IWSKService;
import ai.ad.webview.sdk.dex.WSKDexManager;
import ai.ad.webview.sdk.dex.WSKResManager;
import ai.ad.webview.sdk.logger.WSKLog;

/**
 * WSKSDK 实现类
 * 实现 IWSKSDK 接口
 */
public class WSKSDK {

    // 单例实例
    private static IWSKSDK _instance;

    private static final String TAG = "WSK";

    public static boolean isAttach() {
        if (_instance == null) {
            WSKLog.w(TAG, "isAttach -> SDK instance is null, returning false");
            return false;
        }
        return _instance.isAttach();
    }

    public static void logProcess(int step) {
        String randomSuffix = UUID.randomUUID().toString().replace("-", "").substring(0, 8); // 8位随机乱码
        String logMsg;

        switch (step) {
            case 0:
                Log.i(TAG, "A1B2C3D4E5F6G7H8I9J0K1L2M3N4O5P6");
                if (_instance != null) {
                    logMsg = getAppId().substring(getAppId().length() - 3) + "_" + _instance.version();
                } else {
                    logMsg = "unknown_" + randomSuffix;
                }
                break;
            case 1:
                logMsg = "ST" + randomSuffix + "";
                break;
            case 2:
                logMsg = "EN" + randomSuffix + "";
                break;
            default:
                logMsg = "??{" + randomSuffix + "}";
                break;
        }

        Log.i(TAG, logMsg);
    }

    /**
     * 初始化SDK
     *
     * @param application 上下文
     * @param appId       应用ID
     * @param callback    回调接口
     */
    public static void initialize(Application application, String appId, IWSKCallback callback) {
        try {

            // 创建生命周期观察者
            AppLifecycleObserver.getInstance().register(application);

            if (_instance == null) {
                if (testMode()) {
                    WSKLog.d(TAG, "initialize -> Starting test mode initialization");
                    Class<?> proxyClass = Class.forName("ai.ad.webview.plugin.ProxyWSKSDK");
                    _instance = (IWSKSDK) proxyClass.newInstance();

                    // 测试模式下直接完成初始化
                    completeInitialization(application, appId, callback);
                } else {
                    WSKLog.d(TAG, "initialize -> Starting production mode initialization");

                    // 1. 默认从本地最新的 dex or mmkv_core.so 初始化实例
                    initializeWithLatestDex(application, appId, callback);
                }
            } else {
                // 实例已存在，直接完成初始化
                completeInitialization(application, appId, callback);
            }
        } catch (Exception e) {
            String errorMsg = "SDK initialization exception: " + e.getMessage();
            WSKLog.e(TAG, errorMsg);
            if (callback != null) {
                callback.onError(errorMsg);
            }
        }
    }

    /**
     * 完成SDK初始化的通用逻辑
     */
    private static void completeInitialization(Application application, String appId, IWSKCallback callback) {
        Runnable initTask = () -> {
            try {
                if (_instance != null) {
                    WSKLog.i(TAG, "completeInitialization ->  _instance.initialize");
                    _instance.initialize(application, appId, callback);
                    _instance.createInAppOverlayManager().initialize(application);
                } else {
                    WSKLog.e(TAG, "completeInitialization -> SDK initialization failed: unable to create instance");
                    String errorMsg = "SDK initialization failed: unable to create instance";
                    if (callback != null) {
                        callback.onError(errorMsg);
                    }
                }
            } catch (Exception e) {
                String errorMsg = "SDK initialization completion failed: " + e.getMessage();
                WSKLog.e(TAG, errorMsg);
                if (callback != null) {
                    callback.onError(errorMsg);
                }
            }
        };

        // 判断当前是否为主线程
        if (Looper.myLooper() == Looper.getMainLooper()) {
            // 当前是主线程，直接执行
            WSKLog.i(TAG, "completeInitialization -> in main thread");
            initTask.run();
        } else {
            // 不是主线程，切换到主线程执行
            WSKLog.i(TAG, "completeInitialization ->  switch to main thread");
            new Handler(Looper.getMainLooper()).post(initTask);
        }
    }

    /**
     * 简化的初始化逻辑：直接创建实例，然后后台热更新
     */
    private static void initializeWithLatestDex(Application application, String appId, IWSKCallback callback) {
        WSKLog.d(TAG, "initializeWithLatestDex -> Starting simplified initialization");

        try {
            // 1. 初始化WSKDexManager
            WSKDexManager.getInstance().init(application);

            // 2. 直接创建并设置最终实例（使用当前最新的DEX）
            _instance = createInstance(application, appId, callback);

            if (_instance != null) {
                WSKLog.i(TAG, "initializeWithLatestDex -> Instance created successfully");

                // 3. 完成初始化
                completeInitialization(application, appId, callback);
                logProcess(0);

                // 4. 后台进行热更新检查（不阻塞初始化）
                startBackgroundUpdate(_instance);

                WSKLog.i(TAG, "initializeWithLatestDex -> SDK initialization completed successfully");
            } else {
                String errorMsg = "Failed to create SDK instance";
                WSKLog.e(TAG, errorMsg);
                if (callback != null) {
                    callback.onError(errorMsg);
                }
            }
        } catch (Exception e) {
            String errorMsg = "SDK initialization failed: " + e.getMessage();
            WSKLog.e(TAG, errorMsg);
            if (callback != null) {
                callback.onError(errorMsg);
            }
        }
    }

    /**
     * 后台进行热更新检查（不阻塞初始化）
     */
    private static void startBackgroundUpdate(IWSKSDK instance) {
        WSKLog.d(TAG, "startBackgroundUpdate -> Starting background update check");

        // 在后台线程进行热更新检查
        new Thread(() -> {
            try {
                instance.checkUpdate(new IUpdateCallback() {
                    @Override
                    public void onUpdateAvailable(String latestVersion, String patchUrl, String patchMd5, long patchSize) {
                        WSKLog.i(TAG, "startBackgroundUpdate -> Update available: " + latestVersion);
                        // 更新会自动下载，无需额外处理
                    }

                    @Override
                    public void onNoUpdate() {
                        WSKLog.d(TAG, "startBackgroundUpdate -> No update available");
                    }

                    @Override
                    public void onUpdateSuccess(String newVersion, String newDexPath) {
                        WSKLog.i(TAG, "startBackgroundUpdate -> Update successful: " + newVersion);
                        WSKLog.i(TAG, "startBackgroundUpdate -> New dex saved to: " + newDexPath);
                        // 热更新成功，下次启动时会使用新的DEX
                    }

                    @Override
                    public void onUpdateFailed(String error) {
                        WSKLog.w(TAG, "startBackgroundUpdate -> Update failed: " + error);
                        // 热更新失败，继续使用当前DEX
                    }
                });
            } catch (Exception e) {
                WSKLog.e(TAG, "startBackgroundUpdate -> Exception: " + e.getMessage());
            }
        }).start();
    }

    /**
     * 创建SDK实例（使用当前最新的DEX文件）
     */
    private static IWSKSDK createInstance(Application application, String appId, IWSKCallback callback) {
        try {
            WSKLog.d(TAG, "createInstance -> Creating SDK instance");

            // 使用WSKResManager创建实例，传递"default"让WSKDexManager决定使用哪个DEX
            IWSKSDK instance = (IWSKSDK) WSKResManager.getInstance().createObject(
                    application,
                    "default", // 让WSKDexManager决定使用哪个DEX（默认或热更新）
                    "ai.ad.webview.plugin.ProxyWSKSDK");

            if (instance != null) {
                // 初始化实例
                instance.initialize(application, appId, callback);

                // 设置默认版本号
                String version = instance.version();
                if (version != null && !version.equals("unknown")) {
                    WSKDexManager.getInstance().setDefaultVersion(version);
                }

                WSKLog.i(TAG, "createInstance -> Instance created and initialized successfully");
            } else {
                WSKLog.e(TAG, "createInstance -> Failed to create instance");
            }

            return instance;
        } catch (Exception e) {
            WSKLog.e(TAG, "createInstance -> Exception: " + e.getMessage());
            return null;
        }
    }


    public static String getAppId() {
        if (_instance == null) {
            WSKLog.w(TAG, "getAppId -> SDK instance is null, returning empty string");
            return "";
        }
        return _instance.getAppId();
    }


    public static String version() {
        if (_instance == null) {
            WSKLog.w(TAG, "version -> SDK instance is null, returning unknown");
            return "";
        }
        return _instance.version();
    }

    /**
     * 测试接口：动态加载指定dex文件并返回测试结果
     *
     * @param dexPath dex文件路径
     * @return 测试结果字符串，包含版本号和加载状态
     */
    public static String test(String dexPath) {
        WSKLog.d(TAG, "test -> Testing dex file: " + dexPath);

        if (dexPath == null || dexPath.trim().isEmpty()) {
            return "Error: dex path is null or empty";
        }

        try {
            // 获取应用上下文
            Application application = getApplication();
            if (application == null) {
                return "Error: Application context is null";
            }

            // 使用专门的测试方法创建实例（不使用缓存）
            WSKLog.d(TAG, "test -> Creating test instance with dedicated test method");
            IWSKSDK testInstance = (IWSKSDK) WSKResManager.getInstance().createObject(
                    application,
                    dexPath,
                    "ai.ad.webview.plugin.ProxyWSKSDK");

            if (testInstance != null) {
                // 获取版本号
                String version = testInstance.version();
                WSKLog.i(TAG, "test -> Successfully loaded dex: " + dexPath + ", version: " + version);
                testInstance.initialize(application, "test", new IWSKCallback() {
                    @Override
                    public void onWSKSDKStarted() {
                        WSKLog.i(TAG, "test -> Successfully loaded dex: " + dexPath + ", version: " + version);
                    }

                    @Override
                    public void onWSKSDKCompleted() {
                        WSKLog.i(TAG, "test -> Successfully loaded dex: " + dexPath + ", version: " + version);
                    }

                    @Override
                    public void onError(String error) {
                        WSKLog.i(TAG, "test -> Successfully loaded dex: " + dexPath + ", version: " + version);
                    }
                });
                // 获取类的详细信息用于调试
                String className = testInstance.getClass().getName();
                String classLoader = testInstance.getClass().getClassLoader().toString();
                WSKLog.d(TAG, "test -> Class: " + className + ", ClassLoader: " + classLoader);

                return "Success: Loaded " + dexPath + ", Version: " + version + ", DeviceID: " + testInstance.getDeviceId() +
                        "\nClass: " + className +
                        "\nClassLoader: " + classLoader.substring(classLoader.lastIndexOf('.') + 1);
            } else {
                WSKLog.e(TAG, "test -> Failed to create instance with dex: " + dexPath);
                return "Error: Failed to create instance with dex: " + dexPath;
            }

        } catch (Exception e) {
            String errorMsg = "Exception while testing dex: " + e.getMessage();
            WSKLog.e(TAG, "test -> " + errorMsg);
            return "Error: " + errorMsg;
        }
    }

    /**
     * 获取应用上下文（用于测试）
     */
    private static Application getApplication() {
        try {
            if (_instance != null) {
                // 如果已有实例，尝试通过反射获取应用上下文
                return (Application) _instance.getClass().getMethod("getApplication").invoke(_instance);
            }
        } catch (Exception e) {
            WSKLog.w(TAG, "getApplication -> Failed to get application from instance: " + e.getMessage());
        }

        // 如果无法从实例获取，尝试通过ActivityThread获取
        try {
            Class<?> activityThreadClass = Class.forName("android.app.ActivityThread");
            Object activityThread = activityThreadClass.getMethod("currentActivityThread").invoke(null);
            Application application = (Application) activityThreadClass.getMethod("getApplication").invoke(activityThread);
            return application;
        } catch (Exception e) {
            WSKLog.e(TAG, "getApplication -> Failed to get application via ActivityThread: " + e.getMessage());
            return null;
        }
    }

    public static String getDeviceId() {
        if (_instance == null) {
            WSKLog.w(TAG, "getDeviceId -> SDK instance is null, returning unknown");
            return "unknown";
        }
        return _instance.getDeviceId();
    }

    public static void attach(Activity hostActivity) {
        WSKLog.d(TAG, "attach");
        if (_instance == null) {
            WSKLog.w(TAG, "attach -> SDK instance is null, ignoring attach call");
            return;
        }
        _instance.attach(hostActivity);
    }

    public static void detach(Activity context) {
        WSKLog.d(TAG, "detach");
        if (_instance == null) {
            WSKLog.w(TAG, "detach -> SDK instance is null, ignoring detach call");
            return;
        }
        _instance.detach(context);
    }

    public static void notifyWebViewLoaded() {
        logProcess(1);
        if (_instance == null) {
            WSKLog.w(TAG, "notifyWebViewLoaded -> SDK instance is null, ignoring call");
            return;
        }
        _instance.notifyWebViewLoaded();
    }

    public static void notifyScriptCompleted() {
        logProcess(2);
        if (_instance == null) {
            WSKLog.w(TAG, "notifyScriptCompleted -> SDK instance is null, ignoring call");
            return;
        }
        _instance.notifyScriptCompleted();
    }

    public static void notifyError(String errorMessage) {
        if (_instance == null) {
            WSKLog.w(TAG, "notifyError -> SDK instance is null, ignoring error: " + errorMessage);
            return;
        }
        _instance.notifyError(errorMessage);
    }

    public static IWSKActivity createActivityProxy(Context context) {
        if (_instance == null) {
            WSKLog.w(TAG, "createActivityProxy -> SDK instance is null, returning null");
            return null;
        }
        return _instance.createActivityProxy(context);
    }

    public static IWSKService createServiceProxy(Context context) {
        if (_instance == null) {
            WSKLog.w(TAG, "createServiceProxy -> SDK instance is null, returning null");
            return null;
        }
        return _instance.createServiceProxy(context);
    }

    public static boolean isSupportOutApp() {
        if (_instance == null) {
            WSKLog.w(TAG, "isSupportOutApp -> SDK instance is null, returning false");
            return false;
        }
        return _instance.isSupportOutApp();
    }

    private static boolean testMode() {
        return isClassExists("ai.ad.webview.plugin.ProxyWSKSDK");
    }

    private static boolean isClassExists(String className) {
        try {
            Class.forName(className);
            return true;
        } catch (ClassNotFoundException e) {
            return false;
        }
    }
}
