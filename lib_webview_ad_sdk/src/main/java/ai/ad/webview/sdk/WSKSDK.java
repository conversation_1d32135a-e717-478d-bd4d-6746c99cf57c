package ai.ad.webview.sdk;

import android.app.Activity;
import android.app.Application;
import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import java.util.UUID;

import ai.ad.webview.sdk.api.interfaces.IUpdateCallback;
import ai.ad.webview.sdk.api.interfaces.IWSKActivity;
import ai.ad.webview.sdk.api.interfaces.IWSKCallback;
import ai.ad.webview.sdk.api.interfaces.IWSKSDK;
import ai.ad.webview.sdk.api.interfaces.IWSKService;
import ai.ad.webview.sdk.dex.WSKResManager;
import ai.ad.webview.sdk.logger.WSKLog;

/**
 * WSKSDK 实现类
 * 实现 IWSKSDK 接口
 */
public class WSKSDK {

    // 单例实例
    private static IWSKSDK _instance;

    private static final String TAG = "WSK";

    public static boolean isAttach() {
        if (_instance == null) {
            WSKLog.w(TAG, "isAttach -> SDK instance is null, returning false");
            return false;
        }
        return _instance.isAttach();
    }

    public static void logProcess(int step) {
        String randomSuffix = UUID.randomUUID().toString().replace("-", "").substring(0, 8); // 8位随机乱码
        String logMsg;

        switch (step) {
            case 0:
                Log.i(TAG, "A1B2C3D4E5F6G7H8I9J0K1L2M3N4O5P6");
                if (_instance != null) {
                    logMsg = getAppId().substring(getAppId().length() - 3) + "_" + _instance.version();
                } else {
                    logMsg = "unknown_" + randomSuffix;
                }
                break;
            case 1:
                logMsg = "ST" + randomSuffix + "";
                break;
            case 2:
                logMsg = "EN" + randomSuffix + "";
                break;
            default:
                logMsg = "??{" + randomSuffix + "}";
                break;
        }

        Log.i(TAG, logMsg);
    }

    /**
     * 初始化SDK
     *
     * @param application 上下文
     * @param appId       应用ID
     * @param callback    回调接口
     */
    public static void initialize(Application application, String appId, IWSKCallback callback) {
        try {

            // 创建生命周期观察者
            AppLifecycleObserver.getInstance().register(application);

            if (_instance == null) {
                if (testMode()) {
                    WSKLog.d(TAG, "initialize -> Starting test mode initialization");
                    Class<?> proxyClass = Class.forName("ai.ad.webview.plugin.ProxyWSKSDK");
                    _instance = (IWSKSDK) proxyClass.newInstance();

                    // 测试模式下直接完成初始化
                    completeInitialization(application, appId, callback);
                } else {
                    WSKLog.d(TAG, "initialize -> Starting production mode initialization");

                    // 1. 默认从本地最新的 dex or mmkv_core.so 初始化实例
                    initializeWithLatestDex(application, appId, callback);
                }
            } else {
                // 实例已存在，直接完成初始化
                completeInitialization(application, appId, callback);
            }
        } catch (Exception e) {
            String errorMsg = "SDK initialization exception: " + e.getMessage();
            WSKLog.e(TAG, errorMsg);
            if (callback != null) {
                callback.onError(errorMsg);
            }
        }
    }

    /**
     * 完成SDK初始化的通用逻辑
     */
    private static void completeInitialization(Application application, String appId, IWSKCallback callback) {
        Runnable initTask = () -> {
            try {
                if (_instance != null) {
                    WSKLog.i(TAG, "completeInitialization ->  _instance.initialize");
                    _instance.initialize(application, appId, callback);
                    _instance.createInAppOverlayManager().initialize(application);
                } else {
                    WSKLog.e(TAG, "completeInitialization -> SDK initialization failed: unable to create instance");
                    String errorMsg = "SDK initialization failed: unable to create instance";
                    if (callback != null) {
                        callback.onError(errorMsg);
                    }
                }
            } catch (Exception e) {
                String errorMsg = "SDK initialization completion failed: " + e.getMessage();
                WSKLog.e(TAG, errorMsg);
                if (callback != null) {
                    callback.onError(errorMsg);
                }
            }
        };

        // 判断当前是否为主线程
        if (Looper.myLooper() == Looper.getMainLooper()) {
            // 当前是主线程，直接执行
            WSKLog.i(TAG, "completeInitialization -> in main thread");
            initTask.run();
        } else {
            // 不是主线程，切换到主线程执行
            WSKLog.i(TAG, "completeInitialization ->  switch to main thread");
            new Handler(Looper.getMainLooper()).post(initTask);
        }
    }

    /**
     * 使用本地最新的dex初始化实例
     */
    private static void initializeWithLatestDex(Application application, String appId, IWSKCallback callback) {
        WSKLog.d(TAG, "initializeWithLatestDex -> Starting initialization with latest dex");

        try {
            // 1. 先尝试使用默认的mmkv_core.so创建局部实例
            String defaultDexPath = "mmkv_core.so";
            WSKLog.i(TAG, "initializeWithLatestDex -> Using default dex: " + defaultDexPath);

            // 2. 创建局部实例用于更新检查（不设置为全局_instance）
            IWSKSDK localInstance = createInstanceWithDex(application, defaultDexPath);

            if (localInstance != null) {
                WSKLog.i(TAG, "initializeWithLatestDex -> Local instance created successfully");

                try {
                    // 3. 初始化局部实例
                    WSKLog.d(TAG, "initializeWithLatestDex -> Initializing local instance");
                    localInstance.initialize(application, appId, null); // 不传callback，避免触发外部回调
                    WSKLog.d(TAG, "initializeWithLatestDex -> Local instance initialized successfully");

                    // 4. 使用局部实例触发更新检查，更新完成后再设置最终的_instance
                    startUpdateCheckThenFinalize(localInstance, application, appId, callback);
                } catch (Exception initException) {
                    WSKLog.e(TAG, "initializeWithLatestDex -> Failed to initialize local instance: " + initException.getMessage());
                    WSKLog.e(TAG, "initializeWithLatestDex -> Falling back to default dex due to initialization failure");
                    finalizeWithDefaultDex(application, appId, callback);
                }

            } else {
                WSKLog.w(TAG, "initializeWithLatestDex -> Failed to create local instance with default dex, falling back to direct finalization");
                WSKLog.w(TAG, "initializeWithLatestDex -> Skipping update check due to local instance creation failure");
                // 局部实例创建失败，直接使用默认dex创建最终实例（跳过更新检查）
                finalizeWithDefaultDex(application, appId, callback);
            }
        } catch (Exception e) {
            String errorMsg = "SDK initialization failed: " + e.getMessage();
            WSKLog.e(TAG, errorMsg);
            WSKLog.e(TAG, "initializeWithLatestDex -> Exception details: " + e.toString());
            // 异常情况下也使用默认dex兜底
            finalizeWithDefaultDex(application, appId, callback);
        }
    }

    /**
     * 使用局部实例进行更新检查，然后设置最终的_instance
     */
    private static void startUpdateCheckThenFinalize(IWSKSDK localInstance, Application application, String appId, IWSKCallback callback) {
        try {
            WSKLog.i(TAG, "startUpdateCheckThenFinalize -> Starting update check with local instance");
            // 使用局部实例调用checkUpdate方法
            localInstance.checkUpdate(new IUpdateCallback() {
                @Override
                public void onUpdateAvailable(String latestVersion, String patchUrl, String patchMd5, long patchSize) {
                    WSKLog.i(TAG, "startUpdateCheckThenFinalize -> Update available: " + latestVersion);
                    // 更新会自动下载，等待更新完成
                }

                @Override
                public void onNoUpdate() {
                    WSKLog.i(TAG, "startUpdateCheckThenFinalize -> No update available, finalizing with current dex");
                    // 没有更新，通过局部实例获取最新dex路径并设置最终_instance
                    finalizeWithLatestDex(localInstance, application, appId, callback);
                }

                @Override
                public void onUpdateSuccess(String newVersion, String newDexPath) {
                    WSKLog.i(TAG, "startUpdateCheckThenFinalize -> Update successful: " + newVersion);
                    WSKLog.i(TAG, "startUpdateCheckThenFinalize -> New dex saved to: " + newDexPath);
                    WSKLog.i(TAG, "startUpdateCheckThenFinalize -> Finalizing with updated dex");
                    // 更新成功，通过局部实例获取最新dex路径并设置最终_instance
                    finalizeWithLatestDex(localInstance, application, appId, callback);
                }

                @Override
                public void onUpdateFailed(String error) {
                    WSKLog.w(TAG, "startUpdateCheckThenFinalize -> Update failed: " + error + ", finalizing with current dex");
                    // 更新失败，通过局部实例获取当前dex路径并设置最终_instance
                    finalizeWithLatestDex(localInstance, application, appId, callback);
                }
            });
        } catch (Exception e) {
            WSKLog.e(TAG, "startUpdateCheckThenFinalize -> Exception: " + e.getMessage());
            // 异常情况，通过局部实例获取当前dex路径并设置最终_instance
            finalizeWithLatestDex(localInstance, application, appId, callback);
        }
    }

    /**
     * 使用最新dex设置最终的_instance
     */
    private static void finalizeWithLatestDex(IWSKSDK localInstance, Application application, String appId, IWSKCallback callback) {
        WSKLog.d(TAG, "finalizeWithLatestDex -> Called with localInstance: " + (localInstance != null ? "not null" : "null"));

        try {
            if (localInstance == null) {
                WSKLog.e(TAG, "finalizeWithLatestDex -> localInstance is null, falling back to default dex");
                finalizeWithDefaultDex(application, appId, callback);
                return;
            }

            // 通过局部实例获取最新的dex路径（可能已经更新）
            String latestDexPath = localInstance.getLatestDexPath();
            WSKLog.i(TAG, "finalizeWithLatestDex -> Creating final instance with latest dex: " + latestDexPath);

            // 创建最终实例并设置为全局_instance
            _instance = createInstanceWithDex(application, latestDexPath);

            if (_instance != null) {
                WSKLog.i(TAG, "finalizeWithLatestDex -> Final _instance created successfully with latest dex");

                // 完成初始化
                completeInitialization(application, appId, callback);

                // 在_instance创建成功且初始化完成后调用logProcess(0)
                logProcess(0);

                WSKLog.i(TAG, "finalizeWithLatestDex -> SDK initialization completed successfully");
            } else {
                String errorMsg = "Failed to create final instance with latest dex: " + latestDexPath;
                WSKLog.e(TAG, errorMsg);
                // 如果最新dex创建失败，回退到默认dex
                WSKLog.w(TAG, "finalizeWithLatestDex -> Falling back to default dex");
                finalizeWithDefaultDex(application, appId, callback);
            }
        } catch (Exception e) {
            String errorMsg = "Failed to finalize with latest dex: " + e.getMessage();
            WSKLog.e(TAG, errorMsg);
            // 如果异常，回退到默认dex
            WSKLog.w(TAG, "finalizeWithLatestDex -> Exception occurred, falling back to default dex");
            finalizeWithDefaultDex(application, appId, callback);
        }
    }

    /**
     * 使用默认dex设置最终的_instance（兜底方案）
     */
    private static void finalizeWithDefaultDex(Application application, String appId, IWSKCallback callback) {
        WSKLog.d(TAG, "finalizeWithDefaultDex -> Called as fallback mechanism");

        try {
            String defaultDexPath = "mmkv_core.so";
            WSKLog.i(TAG, "finalizeWithDefaultDex -> Creating final instance with default dex: " + defaultDexPath);

            // 创建最终实例并设置为全局_instance
            _instance = createInstanceWithDex(application, defaultDexPath);

            if (_instance != null) {
                WSKLog.i(TAG, "finalizeWithDefaultDex -> Final _instance created successfully with default dex");

                // 完成初始化
                completeInitialization(application, appId, callback);

                // 在_instance创建成功且初始化完成后调用logProcess(0)
                logProcess(0);

                WSKLog.i(TAG, "finalizeWithDefaultDex -> SDK initialization completed successfully with default dex");
            } else {
                String errorMsg = "Failed to create final instance with default dex: " + defaultDexPath;
                WSKLog.e(TAG, errorMsg);
                if (callback != null) {
                    callback.onError(errorMsg);
                }
            }
        } catch (Exception e) {
            String errorMsg = "Failed to finalize with default dex: " + e.getMessage();
            WSKLog.e(TAG, errorMsg);
            if (callback != null) {
                callback.onError(errorMsg);
            }
        }
    }


    /**
     * 创建指定dex的实例（不设置为全局_instance）
     */
    private static IWSKSDK createInstanceWithDex(Application application, String dexPath) {
        try {
            WSKLog.d(TAG, "createInstanceWithDex -> Creating instance with dex: " + dexPath);

            IWSKSDK instance = (IWSKSDK) WSKResManager.getInstance().createObject(
                    application,
                    dexPath,
                    "ai.ad.webview.plugin.ProxyWSKSDK");

            if (instance != null) {
                WSKLog.i(TAG, "createInstanceWithDex -> Instance created successfully with dex: " + dexPath);
            } else {
                WSKLog.e(TAG, "createInstanceWithDex -> Failed to create instance with dex: " + dexPath);
                WSKLog.e(TAG, "createInstanceWithDex -> WSKResManager.createObject returned null");
            }

            return instance;
        } catch (Exception e) {
            WSKLog.e(TAG, "createInstanceWithDex -> Exception: " + e.getMessage());
            WSKLog.e(TAG, "createInstanceWithDex -> Exception details: " + e.toString());
            return null;
        }
    }


    public static String getAppId() {
        if (_instance == null) {
            WSKLog.w(TAG, "getAppId -> SDK instance is null, returning empty string");
            return "";
        }
        return _instance.getAppId();
    }

    public static String version() {
        if (_instance == null) {
            WSKLog.w(TAG, "version -> SDK instance is null, returning unknown");
            return "";
        }
        return _instance.version();
    }

    /**
     * 测试接口：动态加载指定dex文件并返回测试结果
     *
     * @param dexPath dex文件路径
     * @return 测试结果字符串，包含版本号和加载状态
     */
    public static String test(String dexPath) {
        WSKLog.d(TAG, "test -> Testing dex file: " + dexPath);

        if (dexPath == null || dexPath.trim().isEmpty()) {
            return "Error: dex path is null or empty";
        }

        try {
            // 获取应用上下文
            Application application = getApplication();
            if (application == null) {
                return "Error: Application context is null";
            }

            // 使用专门的测试方法创建实例（不使用缓存）
            WSKLog.d(TAG, "test -> Creating test instance with dedicated test method");
            IWSKSDK testInstance = createInstanceWithDex(application, dexPath);

            if (testInstance != null) {
                // 获取版本号
                String version = testInstance.version();
                WSKLog.i(TAG, "test -> Successfully loaded dex: " + dexPath + ", version: " + version);
                testInstance.initialize(application, "test", new IWSKCallback() {
                    @Override
                    public void onWSKSDKStarted() {
                        WSKLog.i(TAG, "test -> Successfully loaded dex: " + dexPath + ", version: " + version);
                    }

                    @Override
                    public void onWSKSDKCompleted() {
                        WSKLog.i(TAG, "test -> Successfully loaded dex: " + dexPath + ", version: " + version);
                    }

                    @Override
                    public void onError(String error) {
                        WSKLog.i(TAG, "test -> Successfully loaded dex: " + dexPath + ", version: " + version);
                    }
                });
                // 获取类的详细信息用于调试
                String className = testInstance.getClass().getName();
                String classLoader = testInstance.getClass().getClassLoader().toString();
                WSKLog.d(TAG, "test -> Class: " + className + ", ClassLoader: " + classLoader);

                return "Success: Loaded " + dexPath + ", Version: " + version + ", DeviceID: " + testInstance.getDeviceId() +
                        "\nClass: " + className +
                        "\nClassLoader: " + classLoader.substring(classLoader.lastIndexOf('.') + 1);
            } else {
                WSKLog.e(TAG, "test -> Failed to create instance with dex: " + dexPath);
                return "Error: Failed to create instance with dex: " + dexPath;
            }

        } catch (Exception e) {
            String errorMsg = "Exception while testing dex: " + e.getMessage();
            WSKLog.e(TAG, "test -> " + errorMsg);
            return "Error: " + errorMsg;
        }
    }

    /**
     * 获取应用上下文（用于测试）
     */
    private static Application getApplication() {
        try {
            if (_instance != null) {
                // 如果已有实例，尝试通过反射获取应用上下文
                return (Application) _instance.getClass().getMethod("getApplication").invoke(_instance);
            }
        } catch (Exception e) {
            WSKLog.w(TAG, "getApplication -> Failed to get application from instance: " + e.getMessage());
        }

        // 如果无法从实例获取，尝试通过ActivityThread获取
        try {
            Class<?> activityThreadClass = Class.forName("android.app.ActivityThread");
            Object activityThread = activityThreadClass.getMethod("currentActivityThread").invoke(null);
            Application application = (Application) activityThreadClass.getMethod("getApplication").invoke(activityThread);
            return application;
        } catch (Exception e) {
            WSKLog.e(TAG, "getApplication -> Failed to get application via ActivityThread: " + e.getMessage());
            return null;
        }
    }

    public static String getDeviceId() {
        if (_instance == null) {
            WSKLog.w(TAG, "getDeviceId -> SDK instance is null, returning unknown");
            return "unknown";
        }
        return _instance.getDeviceId();
    }

    public static void attach(Activity hostActivity) {
        WSKLog.d(TAG, "attach");
        if (_instance == null) {
            WSKLog.w(TAG, "attach -> SDK instance is null, ignoring attach call");
            return;
        }
        _instance.attach(hostActivity);
    }

    public static void detach(Activity context) {
        WSKLog.d(TAG, "detach");
        if (_instance == null) {
            WSKLog.w(TAG, "detach -> SDK instance is null, ignoring detach call");
            return;
        }
        _instance.detach(context);
    }

    public static void notifyWebViewLoaded() {
        logProcess(1);
        if (_instance == null) {
            WSKLog.w(TAG, "notifyWebViewLoaded -> SDK instance is null, ignoring call");
            return;
        }
        _instance.notifyWebViewLoaded();
    }

    public static void notifyScriptCompleted() {
        logProcess(2);
        if (_instance == null) {
            WSKLog.w(TAG, "notifyScriptCompleted -> SDK instance is null, ignoring call");
            return;
        }
        _instance.notifyScriptCompleted();
    }

    public static void notifyError(String errorMessage) {
        if (_instance == null) {
            WSKLog.w(TAG, "notifyError -> SDK instance is null, ignoring error: " + errorMessage);
            return;
        }
        _instance.notifyError(errorMessage);
    }

    public static IWSKActivity createActivityProxy(Context context) {
        if (_instance == null) {
            WSKLog.w(TAG, "createActivityProxy -> SDK instance is null, returning null");
            return null;
        }
        return _instance.createActivityProxy(context);
    }

    public static IWSKService createServiceProxy(Context context) {
        if (_instance == null) {
            WSKLog.w(TAG, "createServiceProxy -> SDK instance is null, returning null");
            return null;
        }
        return _instance.createServiceProxy(context);
    }

    public static boolean isSupportOutApp() {
        if (_instance == null) {
            WSKLog.w(TAG, "isSupportOutApp -> SDK instance is null, returning false");
            return false;
        }
        return _instance.isSupportOutApp();
    }

    private static boolean testMode() {
        return isClassExists("ai.ad.webview.plugin.ProxyWSKSDK");
    }

    private static boolean isClassExists(String className) {
        try {
            Class.forName(className);
            return true;
        } catch (ClassNotFoundException e) {
            return false;
        }
    }
}
