package ai.ad.webview.sdk.dex;

import android.content.Context;
import android.widget.Toast;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Constructor;
import java.lang.reflect.Method;
import java.util.concurrent.ConcurrentHashMap;

import ai.ad.webview.sdk.logger.WSKLog;

/**
 * Dex 管理器
 * 负责加载 dex 文件并创建对象实例
 */
public class WSKResManager {
    private static final String TAG = "WSKResManager";

    // 单例实例
    private static WSKResManager instance;

    // ClassLoader缓存，key为dex文件的绝对路径，value为对应的ClassLoader
    private final ConcurrentHashMap<String, ClassLoader> classLoaderCache = new ConcurrentHashMap<>();

    // 获取单例实例
    public static WSKResManager getInstance() {
        if (instance == null) {
            synchronized (WSKResManager.class) {
                if (instance == null) {
                    instance = new WSKResManager();
                }
            }
        }
        return instance;
    }

    // 私有构造函数
    private WSKResManager() {
    }

    /**
     * 创建指定类的对象实例
     * 首先尝试从宿主加载类并创建实例，如果成功则显示测试模式提示
     * 如果宿主中不存在该类，则直接使用提供的DEX文件路径加载
     * 统一使用文件系统路径，不再从assets提取
     *
     * @param context   上下文
     * @param dexPath   dex文件的完整路径
     * @param className 要创建的类名
     * @return 创建的对象实例，如果加载失败则返回 null
     */
    public Object createObject(Context context, String dexPath, String className) {
        try {
            // 首先尝试从宿主加载类并创建实例
            Object hostInstance = createInstanceFromClass(context, className, context.getClassLoader(), true);
            if (hostInstance != null) {
                WSKLog.i(TAG, "createObject succeed -> from context");
                return hostInstance;
            }

            // 如果从宿主加载失败，则直接使用提供的DEX文件路径
            File dexFile = new File(dexPath);
            if (!dexFile.exists() || !dexFile.canRead()) {
                WSKLog.e(TAG, "DEX file not accessible: " + dexPath);

                // 检查files目录中是否有其他相关文件
                File filesDir = context.getFilesDir();
                WSKLog.d(TAG, "createObject -> Files directory: " + filesDir.getAbsolutePath());
                File[] files = filesDir.listFiles();
                if (files != null) {
                    WSKLog.d(TAG, "createObject -> Files in directory:");
                    for (File file : files) {
                        WSKLog.d(TAG, "createObject ->   " + file.getName() + " (size: " + file.length() + ")");
                    }
                } else {
                    WSKLog.d(TAG, "createObject -> Files directory is empty or null");
                }

                return null;
            }

            WSKLog.d(TAG, "createObject -> Using DEX file: " + dexPath);

            // 获取或创建 DexClassLoader（使用缓存）
            ClassLoader dexClassLoader = getOrCreateClassLoader(context, dexFile);
            if (dexClassLoader == null) {
                WSKLog.e(TAG, "Failed to create or get DexClassLoader for: " + dexFile.getAbsolutePath());
                return null;
            }

            // 使用通用方法从DexClassLoader创建实例
            Object dexInstance = createInstanceFromClass(context, className, dexClassLoader, false);
            if (dexInstance != null) {
                WSKLog.i(TAG, "createObject succeed -> from so");
                return dexInstance;
            } else {
                WSKLog.e(TAG, "Cannot create object from DEX file: " + className);
                return null;
            }

        } catch (Exception e) {
            WSKLog.e(TAG, "Error occurred while creating object from DEX: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 获取或创建DexClassLoader（使用缓存机制）
     * @param context 上下文
     * @param dexFile dex文件
     * @return ClassLoader实例，如果创建失败则返回null
     */
    private ClassLoader getOrCreateClassLoader(Context context, File dexFile) {
        String dexPath = dexFile.getAbsolutePath();

        // 先从缓存中查找
        ClassLoader cachedClassLoader = classLoaderCache.get(dexPath);
        if (cachedClassLoader != null) {
            WSKLog.d(TAG, "getOrCreateClassLoader -> Using cached ClassLoader for: " + dexPath);
            return cachedClassLoader;
        }

        // 缓存中没有，创建新的ClassLoader
        WSKLog.d(TAG, "getOrCreateClassLoader -> Creating new ClassLoader for: " + dexPath);

        try {
            ClassLoader parentClassLoader = context.getClassLoader();
            File optimizedDir = context.getDir("dex_opt", Context.MODE_PRIVATE);

            // 创建新的DexClassLoader
            dalvik.system.DexClassLoader dexClassLoader = new dalvik.system.DexClassLoader(
                    dexPath,
                    optimizedDir.getAbsolutePath(),
                    null,
                    parentClassLoader);

            // 将新创建的ClassLoader放入缓存
            classLoaderCache.put(dexPath, dexClassLoader);
            WSKLog.i(TAG, "getOrCreateClassLoader -> Created and cached new ClassLoader for: " + dexPath);

            return dexClassLoader;

        } catch (Exception e) {
            WSKLog.e(TAG, "getOrCreateClassLoader -> Failed to create ClassLoader: " + e.getMessage());
            return null;
        }
    }

    /**
     * 清理指定dex文件的ClassLoader缓存
     * @param dexPath dex文件路径
     */
    public void clearClassLoaderCache(String dexPath) {
        ClassLoader removed = classLoaderCache.remove(dexPath);
        if (removed != null) {
            WSKLog.i(TAG, "clearClassLoaderCache -> Removed ClassLoader cache for: " + dexPath);
        }
    }

    /**
     * 清理所有ClassLoader缓存
     */
    public void clearAllClassLoaderCache() {
        int size = classLoaderCache.size();
        classLoaderCache.clear();
        WSKLog.i(TAG, "clearAllClassLoaderCache -> Cleared " + size + " ClassLoader caches");
    }

    /**
     * 根据类名从指定的类加载器中创建实例
     *
     * @param context     上下文
     * @param className   要创建的类名
     * @param classLoader 类加载器
     * @param showToast   是否显示Toast提示（仅当isHost为true时有效）
     * @return 创建的对象实例，如果创建失败则返回null
     */
    private Object createInstanceFromClass(Context context, String className, ClassLoader classLoader, boolean showToast) {
        try {
            Class<?> loadedClass = classLoader.loadClass(className);

            if (loadedClass != null) {

                // 尝试获取 getInstance 静态方法
                try {
                    Method getInstanceMethod = loadedClass.getMethod("getInstance");
                    Object instance = getInstanceMethod.invoke(null);

                    // 如果是宿主类且需要显示Toast，则显示测试模式提示
                    if (showToast) {
                        String instanceName = loadedClass.getSimpleName();
                        Toast.makeText(context, instanceName + " test mode enabled", Toast.LENGTH_SHORT).show();
                    }

                    return instance;
                } catch (NoSuchMethodException e) {
                    // 如果没有 getInstance 方法，尝试使用无参构造函数
                    try {
                        Constructor<?> defaultConstructor = loadedClass.getConstructor();
                        Object instance = defaultConstructor.newInstance();

                        // 如果是宿主类且需要显示Toast，则显示测试模式提示
                        if (showToast) {
                            String instanceName = loadedClass.getSimpleName();
                            Toast.makeText(context, instanceName + " test mode enabled", Toast.LENGTH_SHORT).show();
                        }

                        return instance;
                    } catch (NoSuchMethodException ex) {
                        WSKLog.e(TAG, "Class has neither getInstance() method nor default constructor: " + className);
                        return null;
                    }
                }
            }
        } catch (ClassNotFoundException e) {
        } catch (Exception e) {
            WSKLog.e(TAG, "Error occurred while creating object: " + e.getMessage());
        }
        return null;
    }

    /**
     * 从 assets 目录提取 dex 文件到应用私有目录
     *
     * @param context   上下文
     * @param assetName assets 中的文件名
     * @return 提取后的文件，如果提取失败则返回 null
     */
    private File extractDexFile(Context context, String assetName) {
        WSKLog.d(TAG, "extractDexFile -> Extracting asset: " + assetName);

        File outputFile = new File(context.getFilesDir(), assetName);
        WSKLog.d(TAG, "extractDexFile -> Output file path: " + outputFile.getAbsolutePath());

        // 检查assets中是否存在该文件
        try {
            context.getAssets().open(assetName).close();
            WSKLog.d(TAG, "extractDexFile -> Asset file exists: " + assetName);
        } catch (IOException e) {
            WSKLog.e(TAG, "extractDexFile -> Asset file not found: " + assetName + ", error: " + e.getMessage());
            return null;
        }

        // 如果文件已存在，先删除它，确保我们有一个新的文件
        if (outputFile.exists()) {
            WSKLog.d(TAG, "extractDexFile -> Output file already exists, deleting: " + outputFile.getAbsolutePath());
            if (!outputFile.delete()) {
                WSKLog.e(TAG, "extractDexFile -> Cannot delete existing DEX file: " + outputFile.getAbsolutePath());
                return null;
            }
        }

        try (InputStream is = context.getAssets().open(assetName);
             FileOutputStream fos = new FileOutputStream(outputFile)) {

            WSKLog.d(TAG, "extractDexFile -> Starting file copy");
            byte[] buffer = new byte[8192];
            int count;
            long totalBytes = 0;
            while ((count = is.read(buffer)) != -1) {
                fos.write(buffer, 0, count);
                totalBytes += count;
            }
            fos.flush();
            WSKLog.d(TAG, "extractDexFile -> File copy completed, total bytes: " + totalBytes);

            // 确保文件是只读的，这是关键修复
            if (outputFile.setReadOnly()) {
                WSKLog.d(TAG, "extractDexFile -> File set to read-only successfully");
            } else {
                WSKLog.e(TAG, "extractDexFile -> Cannot set DEX file as read-only");
                return null;
            }

            WSKLog.i(TAG, "extractDexFile -> DEX file extracted successfully: " + outputFile.getAbsolutePath());
            return outputFile;
        } catch (IOException e) {
            WSKLog.e(TAG, "extractDexFile -> Failed to extract DEX file: " + e.getMessage());
            WSKLog.e(TAG, "extractDexFile -> IOException details: " + e.toString());
            return null;
        }
    }
}
