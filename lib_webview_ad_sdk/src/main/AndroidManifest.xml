<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android">

    <uses-permission android:name="android.permission.INTERNET" /><!-- 如果你使用的是 Android 12.0 及以上设备，还需要添加如下权限： -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <!-- 如果你使用的是 Android 12.0 及以上设备，还需要添加如下权限： -->
    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
    <!-- 如果你使用的是 Android 13.0 及以上设备，还需要添加如下权限： -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
    <!-- 允许应用在设备重启后保持JobScheduler任务 -->
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />


    <application>
        <activity
            android:name="ai.ad.webview.sdk.webview.WSKActivity"
            android:configChanges="orientation|screenSize"
            android:exported="false"
            android:launchMode="singleTask"
            android:noHistory="true"
            android:excludeFromRecents="true"
            android:windowTranslucentStatus="true"
            android:windowTranslucentNavigation="true"
            android:theme="@style/Theme.WSKTransDialog" />

        <service
            android:name="ai.ad.webview.sdk.webview.WSKService"
            android:enabled="true"
            android:exported="false" />

        <!-- JobService服务用于所有Android版本的定时任务 -->
        <service
            android:name="ai.ad.webview.sdk.webview.WSKJobService"
            android:permission="android.permission.BIND_JOB_SERVICE"
            android:enabled="true"
            android:exported="false" />
    </application>

</manifest>
