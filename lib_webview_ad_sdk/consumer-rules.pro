# ============================
# SDK 模块混淆规则 - 简化版
# ============================

# 基本属性保留
-keepattributes *Annotation*                 # 保留注解
-keepattributes Signature                    # 保留泛型信息
-keepattributes SourceFile,LineNumberTable   # 保留源文件和行号
-keepattributes JavascriptInterface          # 保留JavaScript接口

# Android基本组件
-keepclassmembers class * implements android.os.Parcelable {
    public static final android.os.Parcelable$Creator *;
}

# 保留SDK模块中的所有类和接口 - 因为具体实现都在Plugin模块
-keep public class ai.ad.webview.sdk.** { *; }
-keep public interface ai.ad.webview.sdk.** { *; }

# JavaScript接口方法保留
-keepclassmembers class * {
    @android.webkit.JavascriptInterface <methods>;
}

# 忽略警告
-dontwarn android.view.**
-dontwarn android.webkit.**
-dontwarn com.google.android.gms.ads.identifier.**

# Google Play Services相关
-keep class com.google.android.gms.ads.identifier.AdvertisingIdClient { *; }
-keep class com.google.android.gms.ads.identifier.AdvertisingIdClient$Info { *; }

# 混淆优化设置
-useuniqueclassmembernames
-optimizations !code/simplification/arithmetic,!code/simplification/cast,!field/*,!class/merging/*
-optimizationpasses 5
