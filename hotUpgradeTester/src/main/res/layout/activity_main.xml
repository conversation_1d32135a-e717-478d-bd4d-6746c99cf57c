<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp"
    android:background="#FFFFFF">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Hot Upgrade Tester"
        android:textSize="20sp"
        android:textStyle="bold"
        android:textColor="#333333"
        android:gravity="center"
        android:layout_marginBottom="16dp" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Available DEX Files in Assets:"
        android:textSize="16sp"
        android:textColor="#666666"
        android:layout_marginBottom="8dp" />

    <ListView
        android:id="@+id/listview_dex_files"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:divider="#DDDDDD"
        android:dividerHeight="1dp"
        android:background="#FAFAFA" />

    <TextView
        android:id="@+id/textview_result"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Select a DEX file to test..."
        android:textSize="14sp"
        android:textColor="#333333"
        android:background="#F0F0F0"
        android:padding="12dp"
        android:layout_marginTop="16dp"
        android:minHeight="80dp"
        android:gravity="top" />

</LinearLayout>