# WebView Ad SDK 更新日志

## v1.1.0 (2025-05-11)

### 架构重构

1. 将 SDK 完全重构为 Java 实现，移除 Kotlin 依赖
2. 实现插件架构，采用代理模式设计
    - 新增 WSKSDKRepo 用于获取 SDK 实例
    - 所有代码通过 WSKSDKRepo.getInstance().createSDK() 获取 SDK 实例
    - 实现 IWSKSDK 接口与 ProxyWSKSDK 代理类
    - 支持两种模式：如果已集成 AAR，则直接从项目中加载 SDK 实例
3. 优化 DEX 动态加载机制
    - 改进 WSKResManager 实现通用的 createObject 方法
    - 解决 DEX 文件加载时的安全异常问题
    - 修复 DEX 文件权限问题，确保设置为只读模式

### 依赖优化

1. 删除所有 androidx 依赖，提高兼容性
    - 移除 support-v4、appcompat-v7、support-core-utils、customtabs、design 等依赖
2. 资源目录优化：只保留必要的 Theme.TransparentDialog 样式
3. 优化 Google Play Services 广告标识符相关依赖
    - 将 play-services-ads-identifier 设置为 compileOnly 依赖

### 混淆与安全

1. 完善 ProGuard 规则配置
    - 添加 Google Play Services 广告标识符相关规则
    - 使用 -repackageclasses 'wsk.plugin' 指令重命名包名
    - 移除自定义混淆字典，简化混淆配置
2. 修复混淆后可能出现的 NoSuchMethodError 异常
    - 特别保留 DeviceIdUtil 类及其所有内部类和方法
    - 保留所有实现 Runnable 接口的类
3. 确保 API 模块中的接口和类不被混淆
    - 更新 consumer-rules.pro 和 proguard-rules.pro 文件

### 设备标识

1. 优化 DeviceIdUtil，在 SDK 初始化时预加载 GAID
    - 添加 preloadGAID() 方法，提前获取设备 ID
2. 修复设备 ID 获取时的竞态条件问题
    - 添加 waitForDeviceId() 方法和 DeviceIdReadyCallback 接口
    - 确保服务只在设备 ID 成功获取后启动
3. 等待设备 ID 成功获取（非空）后再启动服务
    - 修改 getDeviceId() 方法，当 ID 未获取时返回空字符串而非随机 UUID

### 其他改进

1. 修复 JobScheduler 任务调度相关问题
    - 优化 ProxyWSKJobService 实现
    - 在服务启动时记录 SDK 版本号和 AppID
2. 优化服务启动和停止流程
    - 改进 ProxyWSKService 的生命周期管理
    - 优化悬浮窗创建和降级使用 WebActivity 的逻辑
3. 简化 Demo SDK 实现
    - 更新 MainActivity 示例代码，提供更清晰的使用说明
    - 修复文档和示例中的权限说明，标记必选权限

## v1.0.0 (2025-03-26)

### 初始版本功能

1. 基础 WebView 广告 SDK 框架
    - 支持 WSKActivity 透明 Activity 方案
    - 支持 WSKService 后台服务方案
    - 支持 JobScheduler 定时任务调度

### 脚本系统

1. 实现脚本计划获取与执行
    - 支持从服务器获取脚本计划
    - 支持本地缓存脚本计划
    - 实现脚本执行引擎

### 事件录制与回放

1. 实现 ActionRecoder 模块
    - 支持用户操作事件录制
    - 支持录制脚本回放
    - 提供脚本管理界面

### 悬浮窗支持

1. 实现 WSKOverlay 悬浮窗方案
    - 支持 Android 各版本悬浮窗权限适配
    - 支持悬浮窗降级使用 Activity 方案

### 设备信息

1. 实现设备信息收集
    - 支持获取设备 ID (GAID)
    - 支持获取设备基本信息

### 日志与统计

1. 实现基础日志系统
    - 支持调试日志输出
    - 支持事件上报