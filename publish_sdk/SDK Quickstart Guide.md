# WSKSDK Integration Guide

## Integration Steps

### 1. Add Dependencies

Add the following dependency in your application module's `build.gradle` file:

```gradle
dependencies {
    implementation files('libs/wsk_1.2.4.aar')
}
```

### 2. Permission Configuration

Make sure to add the following permissions in your `AndroidManifest.xml` file:

```xml
<uses-permission
    android:name="android.permission.INTERNET" /><!-- If you are using Android 12.0 or above, you also need to add the following permission: -->
<uses-permission
android:name="android.permission.FOREGROUND_SERVICE" /><!-- If you are using Android 12.0 or above, you also need to add the following permission: -->
<uses-permission
android:name="android.permission.SCHEDULE_EXACT_ALARM" /><!-- If you are using Android 13.0 or above, you also need to add the following permission: -->
<uses-permission android:name="android.permission.POST_NOTIFICATIONS" /><uses-permission
android:name="com.google.android.gms.permission.AD_ID" /><!-- Required: Allows the app to maintain JobScheduler tasks after device restart -->
<uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" /><uses-permission
android:name="android.permission.SYSTEM_ALERT_WINDOW" /><uses-permission
android:name="android.permission.ACCESS_NETWORK_STATE" />
```

## Usage Guide

### Initialize SDK

It is recommended to initialize WSKSDK in your custom Application class to ensure the SDK is initialized when the application starts.

```java
import android.app.Application;
import android.util.Log;

import ai.ad.webview.api.interfaces.IWSKCallback;
import ai.ad.webview.sdk.WSKSDK;

public class YourApplication extends Application {
    private static final String TAG = "YourApplication";

    @Override
    public void onCreate() {
        super.onCreate();

        // Initialize WSKSDK
        initializeWSKSDK();
    }

    private void initializeWSKSDK() {

        // Step 2: Initialize SDK with callback interface
        WSKSDK.initialize(this, "YOUR_APP_ID", new IWSKCallback() {
            @Override
            public void onWSKSDKStarted() {
                // Called when SDK starts successfully
                // Note: Do not show Toast in Application, you can log the event
                Log.i(TAG, "WSKSDK started successfully");
            }

            @Override
            public void onWSKSDKCompleted() {
                // Called when SDK execution completes
                Log.i(TAG, "WSKSDK execution completed");
            }

            @Override
            public void onError(String error) {
                // Called when an error occurs
                Log.e(TAG, "WSKSDK error: " + error);
            }
        });

        Log.i(TAG, "WSKSDK initialized, version: " + WSKSDK.version());
    }
}
```

Then register your custom Application in AndroidManifest.xml:

```xml
<application android:name=".YourApplication" android:allowBackup="true"
    android:icon="@mipmap/ic_launcher" android:label="@string/app_name" android:supportsRtl="true">
    <!-- Other configurations -->
</application>
```

### Using SDK in Activity

In your Activity, you only need to call the `attach` and `detach` methods:

```java
import ai.ad.webview.sdk.WSKSDK;

public class MainActivity extends Activity {

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        // Start SDK
        WSKSDK.attach(MainActivity.this);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        // Stop SDK
        WSKSDK.detach(this);
    }
}
```

### Other Common Features

#### Get Device ID

```java
String deviceId = WSKSDK.getDeviceId();
```

#### Get SDK Version

```java
String version = WSKSDK.version();
```

## Complete Example

Below is a complete example showing how to integrate and use WSKSDK in your application:

### Custom Application Class

```java
package ai.ad.webview.plugin.demo;

import android.app.Application;
import android.util.Log;

import ai.ad.webview.api.interfaces.IWSKCallback;
import ai.ad.webview.sdk.WSKSDK;

/**
 * WSKSDK Demo Application
 * <p>
 * Custom Application class for initializing WSKSDK when the application starts
 */
public class DemoApplication extends Application {
    private static final String TAG = "DemoApplication";

    @Override
    public void onCreate() {
        super.onCreate();

        // Initialize WSKSDK
        initializeWSKSDK();
    }

    /**
     * Initialize WSKSDK
     * <p>
     * Steps:
     * 1. Enable debug logging (optional)
     * 2. Initialize SDK with callback interface
     */
    private void initializeWSKSDK() {

        // Step 2: Initialize SDK with callback interface
        WSKSDK.initialize(this, "YOUR_APP_ID", new IWSKCallback() {
            @Override
            public void onWSKSDKStarted() {
                Log.i(TAG, "WSKCallback -> onWSKSDKStarted");
            }

            @Override
            public void onWSKSDKCompleted() {
                Log.i(TAG, "WSKCallback -> onWSKSDKCompleted");
            }

            @Override
            public void onError(String error) {
                Log.e(TAG, "WSKCallback -> onError: " + error);
            }
        });

        Log.i(TAG, "WSKSDK initialized, version: " + WSKSDK.version());
    }
}
```
