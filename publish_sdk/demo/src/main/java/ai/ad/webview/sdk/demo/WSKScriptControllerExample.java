package ai.ad.webview.sdk.demo;

import android.app.Application;
import android.webkit.WebView;

import ai.ad.webview.plugin.overlay.WSKScriptController;
import ai.ad.webview.sdk.WSKSDK;
import ai.ad.webview.sdk.api.interfaces.IInAppOverlayScriptController;

/**
 * WSKScriptController示例
 * 展示如何使用WSKScriptController执行脚本
 */
public class WSKScriptControllerExample {

    /**
     * 在Application中初始化
     *
     * @param application 应用Application实例
     */
    public static void initializeInApplication(Application application) {
        // 初始化WSKSDK
        // 注意：这里只是示例，实际使用时应该在合适的地方初始化SDK
        WSKSDK.initialize(application, "app_test_001", null);

        // 初始化应用内悬浮窗管理器
        WSKSDK.initializeInAppOverlay(application);

        // 设置WSKScriptController
        // WSKScriptController会使用ScriptScheduler来定时拉取和执行脚本
        WSKSDK.setInAppOverlayScriptController(new WSKScriptController(application));
    }

    /**
     * 启用应用内悬浮窗并执行脚本
     * 可以在任何需要的地方调用
     */
    public static void enableOverlayAndRunScript() {
        WSKSDK.enableInAppOverlay();
    }

    /**
     * 禁用应用内悬浮窗
     * 可以在任何需要的地方调用
     */
    public static void disableOverlay() {
        WSKSDK.disableInAppOverlay();
    }

    /**
     * 使用说明
     */
    public static void usage() {
        System.out.println("WSKScriptController使用说明：");
        System.out.println("1. 在Application中初始化SDK和悬浮窗管理器");
        System.out.println("2. 创建WSKScriptController实例");
        System.out.println("3. 设置WSKScriptController到悬浮窗管理器");
        System.out.println("4. 启用悬浮窗，WSKScriptController会自动启动ScriptScheduler");
        System.out.println("5. ScriptScheduler会定时拉取脚本计划并执行脚本");
        System.out.println("6. 禁用悬浮窗，WSKScriptController会自动停止ScriptScheduler并释放资源");

        System.out.println("\n示例代码：");
        System.out.println("// 在Application中初始化");
        System.out.println("WSKSDK.initialize(application, \"your_app_id\", callback);");
        System.out.println("WSKSDK.initializeInAppOverlay(application);");
        System.out.println("WSKSDK.setInAppOverlayScriptController(new WSKScriptController(application));");

        System.out.println("\n// 启用悬浮窗并执行脚本");
        System.out.println("WSKSDK.enableInAppOverlay();");

        System.out.println("\n// 禁用悬浮窗");
        System.out.println("WSKSDK.disableInAppOverlay();");
    }

    /**
     * WSKScriptController工作原理
     */
    public static void howItWorks() {
        System.out.println("WSKScriptController工作原理：");
        System.out.println("1. WSKScriptController实现了IInAppOverlayScriptController接口");
        System.out.println("2. 当悬浮窗WebView创建完成时，WSKScriptController会初始化ScriptScheduler");
        System.out.println("3. ScriptScheduler是一个单例，负责定时拉取脚本计划并执行脚本");
        System.out.println("4. ScriptScheduler会每隔10秒检查一次是否有新的脚本需要执行");
        System.out.println("5. 当应用进入前台时，WSKScriptController会启动ScriptScheduler");
        System.out.println("6. 当应用进入后台时，WSKScriptController会停止ScriptScheduler");
        System.out.println("7. 当悬浮窗被禁用时，WSKScriptController会停止ScriptScheduler并释放资源");
    }
}
