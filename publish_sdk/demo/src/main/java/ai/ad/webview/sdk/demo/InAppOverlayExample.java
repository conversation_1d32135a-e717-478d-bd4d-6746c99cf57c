package ai.ad.webview.sdk.demo;

import android.app.Application;
import android.webkit.WebView;

import ai.ad.webview.sdk.WSKSDK;
import ai.ad.webview.sdk.api.interfaces.IInAppOverlayScriptController;

/**
 * 应用内悬浮WebView示例
 * 展示如何使用InAppOverlayManager
 */
public class InAppOverlayExample {

    /**
     * 在Application中初始化
     *
     * @param application 应用Application实例
     */
    public static void initializeInApplication(Application application) {
        // 初始化WSKSDK
        // 注意：这里只是示例，实际使用时应该在合适的地方初始化SDK
        WSKSDK.initialize(application, "app_test_001", null);

        // 初始化应用内悬浮窗管理器
        WSKSDK.initializeInAppOverlay(application);

        // 设置脚本控制器（可选）
        WSKSDK.setInAppOverlayScriptController(new MyScriptController());
    }

    /**
     * 启用应用内悬浮窗
     * 可以在任何需要的地方调用
     */
    public static void enableOverlay() {
        WSKSDK.enableInAppOverlay();
    }

    /**
     * 禁用应用内悬浮窗
     * 可以在任何需要的地方调用
     */
    public static void disableOverlay() {
        WSKSDK.disableInAppOverlay();
    }

    /**
     * 自定义脚本控制器实现
     * 用于控制WebView中脚本的生命周期
     */
    private static class MyScriptController implements IInAppOverlayScriptController {

        @Override
        public void onWebViewCreated(WebView webView) {
            // WebView创建完成，可以进行初始化配置
            // 例如：加载URL、注入JavaScript等
            webView.loadUrl("https://example.com");
        }

        @Override
        public void onResume() {
            // 应用进入前台或悬浮窗启用时调用
            // 可以在此恢复脚本执行
            System.out.println("脚本恢复执行");
        }

        @Override
        public void onPause() {
            // 应用进入后台时调用
            // 可以在此暂停脚本执行
            System.out.println("脚本暂停执行");
        }

        @Override
        public void onDestroy() {
            // 悬浮窗被禁用时调用
            // 可以在此释放资源
            System.out.println("脚本资源释放");
        }
    }
}
