buildscript {
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:8.2.2'
    }
}

plugins {
    id 'com.android.application'
}

android {
    namespace 'ai.ad.webview.sdk.demo'
    compileSdk 35

    defaultConfig {
        applicationId "ai.ad.webview.sdk.demo"
        targetSdk 35
        minSdk 21
        versionCode 1
        versionName "1.0"
    }

    signingConfigs {
        release {
            storeFile file('debugkey.jks')
            storePassword 'debugkey'
            keyAlias 'debugkey'
            keyPassword 'debugkey'
        }
    }


    buildTypes {
        debug {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro', 'consumer-rules.pro'
            consumerProguardFiles 'consumer-rules.pro'
            signingConfig signingConfigs.release
        }
        release {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro', 'consumer-rules.pro'
            consumerProguardFiles 'consumer-rules.pro'
            signingConfig signingConfigs.release
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

dependencies {
    // 可选
    implementation 'com.google.android.gms:play-services-ads-identifier:18.2.0'
    // 必选
    implementation files('libs/wsk_1.2.3.aar')
}