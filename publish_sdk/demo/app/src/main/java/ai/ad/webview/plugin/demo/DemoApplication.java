package ai.ad.webview.plugin.demo;

import android.app.Application;
import android.util.Log;
import android.widget.Toast;

import ai.ad.webview.sdk.WSKSDK;
import ai.ad.webview.sdk.api.interfaces.IWSKCallback;

/**
 * WSKSDK Demo Application
 * <p>
 * 自定义Application类，用于在应用启动时初始化WSKSDK
 */
public class DemoApplication extends Application {
    private static final String TAG = "DemoApplication";

    @Override
    public void onCreate() {
        super.onCreate();

        // 初始化WSKSDK
        initializeWSKSDK();
    }

    /**
     * 初始化WSKSDK
     * <p>
     * 步骤：
     * 1. 启用调试日志（可选）
     * 2. 初始化SDK，传入回调接口
     */
    private void initializeWSKSDK() {

        // 步骤 2: 初始化 SDK，传入回调接口
        WSKSDK.initialize(this, "APP20250511153045_Z8JX2L", new IWSKCallback() {
            @Override
            public void onWSKSDKStarted() {
            }

            @Override
            public void onWSKSDKCompleted() {
            }

            @Override
            public void onError(String error) {
            }
        });
    }
}
