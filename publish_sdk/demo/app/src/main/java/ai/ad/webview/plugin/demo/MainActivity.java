package ai.ad.webview.plugin.demo;

import android.app.Activity;
import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Context;
import android.os.Bundle;
import android.util.Log;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;


import ai.ad.webview.sdk.WSKSDK;
import ai.ad.webview.sdk.demo.R;

/**
 * WSKSDK 使用示例
 * <p>
 * WSKSDK 是一个用于在 Android 应用中加载和执行 Web 资源的 SDK。
 * 本示例展示了 SDK 的基本用法，包括初始化、启动、停止等操作。
 */
public class MainActivity extends Activity {
    private String TAG = "MainActivity";
    private Button copyDeviceIdButton;
    private TextView versionTextView;

    /**
     * WSKSDK 使用示例
     * <p>
     * 注意：SDK已在Application中初始化，此处只需要调用attach方法
     */
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        copyDeviceIdButton = findViewById(R.id.copyDeviceIdButton);
        versionTextView = findViewById(R.id.versionTextView);

        // SDK已在Application中初始化，此处不需要再次初始化

        // 启动 SDK
        WSKSDK.attach(MainActivity.this);

        // 其他接口
        // 显示 SDK 版本号
        String version = WSKSDK.version();
        versionTextView.setText("SDK Version: " + version);

        // 设置复制设备 ID 按钮点击监听器
        copyDeviceIdButton.setOnClickListener(v -> {
            // 获取设备 ID
            String deviceId = WSKSDK.getDeviceId();

            // 获取系统剪贴板管理器
            ClipboardManager clipboard = (ClipboardManager) getSystemService(Context.CLIPBOARD_SERVICE);

            // 将设备 ID 复制到剪贴板
            ClipData clip = ClipData.newPlainText("Device ID", deviceId);
            clipboard.setPrimaryClip(clip);

            // 显示复制成功的提示
            Toast.makeText(MainActivity.this, "device ID " + deviceId , Toast.LENGTH_SHORT).show();
        });
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        // 停止 SDK
        WSKSDK.detach(this);
    }
}