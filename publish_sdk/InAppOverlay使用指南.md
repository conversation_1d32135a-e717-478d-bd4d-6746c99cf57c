# InAppOverlayManager 使用指南

## 一、概述

InAppOverlayManager 是一个全局管理器，负责在应用处于前台时，展示一个透明的悬浮
WebView（无需权限），用于调试、脚本运行等场景。

该悬浮窗：

- 跨 Activity 显示
- 随应用进入前台自动展示，后台自动暂停
- 不依赖任何权限（不使用 SYSTEM_ALERT_WINDOW）
- 兼容 Android 6（API 23）到 Android 16（API 34+）

## 二、集成步骤

### 1. 在 Application 中初始化

```java
public class MyApplication extends Application {
    @Override
    public void onCreate() {
        super.onCreate();
        
        // 初始化 WSKSDK
        WSKSDK.initialize(this, "your_app_id", callback);
        
        // 初始化应用内悬浮窗管理器
        WSKSDK.initializeInAppOverlay(this);
    }
}
```

### 2. 创建脚本控制器（可选）

```java
public class MyScriptController implements IInAppOverlayScriptController {
    
    @Override
    public void onWebViewCreated(WebView webView) {
        // WebView 创建完成，可以进行初始化配置
        // 例如：加载 URL、注入 JavaScript 等
        webView.loadUrl("https://example.com");
    }
    
    @Override
    public void onResume() {
        // 应用进入前台或悬浮窗启用时调用
        // 可以在此恢复脚本执行
    }
    
    @Override
    public void onPause() {
        // 应用进入后台时调用
        // 可以在此暂停脚本执行
    }
    
    @Override
    public void onDestroy() {
        // 悬浮窗被禁用时调用
        // 可以在此释放资源
    }
}
```

### 3. 设置脚本控制器（可选）

```java
// 设置脚本控制器
WSKSDK.setInAppOverlayScriptController(new MyScriptController());
```

### 4. 启用悬浮窗

```java
// 启用悬浮窗
WSKSDK.enableInAppOverlay();
```

### 5. 禁用悬浮窗

```java
// 禁用悬浮窗
WSKSDK.disableInAppOverlay();
```

## 三、生命周期行为说明

InAppOverlayManager 内部会注册以下生命周期监听器：

1. Application.ActivityLifecycleCallbacks（用于监听当前前台 Activity）

自动行为如下：

| 应用状态       | 自动行为                          |
|------------|-------------------------------|
| 进入前台       | 创建并显示悬浮 WebView，调用 onResume() |
| 切换到后台      | 隐藏 WebView，调用 onPause()       |
| 调用 disable | 永久移除 WebView，调用 onDestroy()   |

## 四、完整示例

```java
// 在 Application 中初始化
public class MyApplication extends Application {
    @Override
    public void onCreate() {
        super.onCreate();
        
        // 初始化 WSKSDK
        WSKSDK.initialize(this, "your_app_id", callback);
        
        // 初始化应用内悬浮窗管理器
        WSKSDK.initializeInAppOverlay(this);
        
        // 设置脚本控制器
        WSKSDK.setInAppOverlayScriptController(new MyScriptController());
    }
}

// 在需要的地方启用悬浮窗
WSKSDK.enableInAppOverlay();

// 在需要的地方禁用悬浮窗
WSKSDK.disableInAppOverlay();
```

## 五、注意事项

1. 必须在 Application.onCreate() 中调用 initializeInAppOverlay() 方法进行初始化。
2. 悬浮窗仅在应用处于前台时显示，应用进入后台时会自动隐藏。
3. 禁用悬浮窗后，需要再次调用 enableInAppOverlay() 才能重新启用。
4. 脚本控制器是可选的，如果不设置，悬浮窗仍然可以正常工作，但无法控制 WebView 中的脚本行为。
5. 悬浮窗使用 TYPE_APPLICATION 类型，不需要任何权限，但仅在应用内可见。

## 六、兼容性说明

| 特性                     | 支持情况   |
|------------------------|--------|
| Android 6 - Android 16 | ✅ 支持   |
| 权限需求                   | ❌ 无需权限 |
| 使用 TYPE_APPLICATION    | ✅ 安全方案 |
| 使用 ApplicationContext  | ✅ 防止泄漏 |
| 多窗口/多 Activity 兼容      | ✅ 自动适配 |
