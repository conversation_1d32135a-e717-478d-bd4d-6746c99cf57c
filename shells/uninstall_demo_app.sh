#!/bin/bash

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# 应用包名
PACKAGE_NAME="ai.ad.webview.sdk.demo"

echo -e "${YELLOW}正在检查 adb 是否可用...${NC}"

# 检查 adb 是否可用
if ! command -v adb &> /dev/null; then
    echo -e "${RED}错误: adb 命令未找到${NC}"
    echo -e "${YELLOW}请确保 Android SDK 平台工具已安装并添加到 PATH 环境变量中${NC}"
    exit 1
fi

# 检查设备连接状态
echo -e "${YELLOW}正在检查设备连接状态...${NC}"
DEVICES=$(adb devices | grep -v "List" | grep -v "^$" | wc -l)

if [ "$DEVICES" -eq 0 ]; then
    echo -e "${RED}错误: 未检测到已连接的设备${NC}"
    echo -e "${YELLOW}请确保至少有一个设备或模拟器已连接${NC}"
    exit 1
fi

# 如果连接了多个设备，显示设备列表
if [ "$DEVICES" -gt 1 ]; then
    echo -e "${YELLOW}检测到多个设备:${NC}"
    adb devices -l
    echo -e "${YELLOW}请使用 -s 选项指定设备，例如:${NC}"
    echo -e "${GREEN}adb -s device_id uninstall $PACKAGE_NAME${NC}"
    echo -e "${YELLOW}或者设置 ANDROID_SERIAL 环境变量${NC}"
    
    # 询问是否继续
    read -p "是否继续卸载所有已连接设备上的应用? (y/n): " CONTINUE
    if [[ ! "$CONTINUE" =~ ^[Yy]$ ]]; then
        echo -e "${YELLOW}操作已取消${NC}"
        exit 0
    fi
fi

# 卸载应用
echo -e "${YELLOW}正在卸载应用: $PACKAGE_NAME${NC}"

# 获取设备列表
DEVICE_LIST=$(adb devices | grep -v "List" | grep -v "^$" | awk '{print $1}')

# 对每个设备执行卸载操作
for DEVICE in $DEVICE_LIST; do
    echo -e "${YELLOW}在设备 $DEVICE 上卸载应用...${NC}"
    RESULT=$(adb -s $DEVICE uninstall $PACKAGE_NAME 2>&1)
    
    if [[ "$RESULT" == *"Success"* ]]; then
        echo -e "${GREEN}应用已成功从设备 $DEVICE 卸载${NC}"
    else
        # 检查应用是否已安装
        PACKAGE_CHECK=$(adb -s $DEVICE shell pm list packages | grep $PACKAGE_NAME)
        if [ -z "$PACKAGE_CHECK" ]; then
            echo -e "${YELLOW}设备 $DEVICE 上未安装该应用${NC}"
        else
            echo -e "${RED}卸载失败: $RESULT${NC}"
        fi
    fi
done

echo -e "${GREEN}卸载操作完成${NC}"
exit 0
