#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Script Name: post_test_scripts.py
Description: Send test scripts to the server
Usage:
    python post_test_scripts.py                  # Send all scripts
    python post_test_scripts.py -f script_01.json # Send a single script
    python post_test_scripts.py -a               # Send all scripts
    python post_test_scripts.py -d /path/to/dir  # Specify script directory
    python post_test_scripts.py -t your_token    # Specify authentication token
"""

import argparse
import json
import os
import sys
import time
import urllib.request
import urllib.parse
import urllib.error

# 配置信息
API_URL = "https://api.h5-game.com/api/admin/scripts"
AUTH_TOKEN = "45008f48-d7fe-40ea-98c2-c43f88e3a0b2"  # 从请求头中提取的 token
INPUT_DIR = ".output_test_scripts"  # 输入目录

def get_headers():
    """
    获取请求头

    Returns:
        dict: 请求头字典
    """
    return {
        "Accept": "application/json, text/plain, */*",
        "Accept-Language": "zh-CN,zh;q=0.9",
        "Authorization": f"Bearer {AUTH_TOKEN}",
        "Content-Type": "application/x-www-form-urlencoded",
        "Origin": "https://www.h5g.co",
        "Referer": "https://www.h5g.co/",
        "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "X-Requested-With": "XMLHttpRequest"
    }

def get_script_name_from_filename(filename):
    """
    从文件名提取脚本名称

    Args:
        filename: 文件名

    Returns:
        str: 脚本名称
    """
    # 移除 .json 后缀
    return os.path.splitext(os.path.basename(filename))[0]

def post_script(file_path):
    """
    将脚本文件发送到服务器

    Args:
        file_path: 脚本文件路径

    Returns:
        dict: 服务器响应
    """
    # 读取脚本文件内容
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()

    # 获取脚本名称
    script_name = get_script_name_from_filename(file_path)

    # 准备请求数据 (application/x-www-form-urlencoded 格式)
    data = {
        "name": script_name,
        "content": content,
        "frozen": "0"  # 字符串格式，适合 form-urlencoded
    }

    # 将数据编码为 URL 编码格式
    data_encoded = urllib.parse.urlencode(data).encode('utf-8')

    # Print request information (for debugging)
    print(f"Sending script: {script_name}")
    print(f"Request URL: {API_URL}")
    print(f"Request headers: {json.dumps(get_headers(), indent=2, ensure_ascii=False)}")
    print(f"Request data: name={script_name}, content=<JSON content>, frozen=0")

    # 创建请求对象
    req = urllib.request.Request(
        url=API_URL,
        data=data_encoded,
        headers=get_headers(),
        method="POST"
    )

    # 发送请求
    try:
        with urllib.request.urlopen(req) as response:
            # 读取响应内容
            response_data = response.read().decode('utf-8')

            # Print response status and content (for debugging)
            print(f"Response status code: {response.status}")
            print(f"Response content: {response_data[:200]}..." if len(response_data) > 200 else f"Response content: {response_data}")

            # 解析 JSON 响应
            return json.loads(response_data)
    except urllib.error.HTTPError as e:
        print(f"Error sending script {script_name}: HTTP error {e.code}")
        try:
            error_response = e.read().decode('utf-8')
            print(f"Error response: {error_response}")
            return json.loads(error_response)
        except:
            return None
    except urllib.error.URLError as e:
        print(f"Error sending script {script_name}: URL error {e.reason}")
        return None
    except Exception as e:
        print(f"Error sending script {script_name}: {e}")
        return None

def parse_arguments():
    """
    Parse command line arguments

    Returns:
        argparse.Namespace: Parsed arguments
    """
    parser = argparse.ArgumentParser(description='Send test scripts to the server')
    parser.add_argument('-f', '--file', help='Specify a single script file name (without path)')
    parser.add_argument('-a', '--all', action='store_true', help='Send all script files')
    parser.add_argument('-d', '--directory', default=INPUT_DIR, help=f'Specify script directory (default: {INPUT_DIR})')
    parser.add_argument('-t', '--token', default=AUTH_TOKEN, help='Specify authentication token')

    return parser.parse_args()

def main():
    """
    Main function
    """
    # 解析命令行参数
    args = parse_arguments()

    # 更新配置
    global AUTH_TOKEN
    if args.token:
        AUTH_TOKEN = args.token

    input_dir = args.directory

    # Check if input directory exists
    if not os.path.exists(input_dir):
        print(f"Error: Directory {input_dir} does not exist")
        sys.exit(1)

    # Determine which files to process
    if args.file:
        # Process a single specified file
        json_files = [args.file]
        if not os.path.exists(os.path.join(input_dir, args.file)):
            print(f"Error: File {args.file} does not exist in directory {input_dir}")
            sys.exit(1)
    elif args.all or (not args.file and not args.all):
        # Process all files (if neither --file nor --all is specified, process all files by default)
        json_files = [f for f in os.listdir(input_dir) if f.endswith('.json')]
        if not json_files:
            print(f"Error: No JSON files found in directory {input_dir}")
            sys.exit(1)
    else:
        print("Error: Please specify --file or --all parameter")
        sys.exit(1)

    # Send each script file
    success_count = 0
    for json_file in json_files:
        file_path = os.path.join(input_dir, json_file)
        response = post_script(file_path)

        if response and response.get("code") == 0:
            success_count += 1
            print(f"Successfully sent script: {json_file}")
            print(f"Server response: {json.dumps(response, indent=2, ensure_ascii=False)}")
        else:
            print(f"Failed to send script {json_file}")
            if response:
                print(f"Server response: {json.dumps(response, indent=2, ensure_ascii=False)}")

        # Add delay to avoid too frequent requests
        time.sleep(1)

    print(f"\nSummary: Successfully sent {success_count}/{len(json_files)} scripts")

if __name__ == "__main__":
    main()
