#!/bin/bash

# 脚本用于构建 actionRecoder 项目的 release APK 并将其移动到 publish_sdk/recoder_apks 目录
# 作者: Shawn
# 日期: 2023-05-15

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的信息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 获取当前日期时间
DATE_TIME=$(date +"%Y%m%d_%H%M")
print_info "构建时间: $DATE_TIME"

# 设置APK文件名
APK_NAME="recoder_${DATE_TIME}.apk"
print_info "APK文件名: $APK_NAME"

# 设置输出目录
OUTPUT_DIR="/Users/<USER>/Projects/android_webview_ad_sdk/publish_sdk/recoder_apks"
APK_OUTPUT_PATH="actionRecoder/build/outputs/apk/release/actionRecoder-release.apk"

# 确保输出目录存在
mkdir -p "$OUTPUT_DIR"
print_info "输出目录: $OUTPUT_DIR"

# 设置工作目录为项目根目录
SCRIPT_DIR="$(dirname "$0")"
cd "$SCRIPT_DIR/.."
print_info "当前工作目录: $(pwd)"

# 检查 gradlew 是否存在
if [ ! -f "./gradlew" ]; then
    print_error "gradlew 文件不存在"
    exit 1
fi

# 确保 gradlew 有执行权限
chmod +x ./gradlew

# 清理之前的构建
print_info "清理之前的构建..."
./gradlew clean
if [ $? -ne 0 ]; then
    print_error "清理构建失败!"
    exit 1
fi

# 构建发布版APK
print_info "构建 actionRecoder 项目的 release APK..."
./gradlew :actionRecoder:assembleRelease
if [ $? -ne 0 ]; then
    print_error "构建失败!"
    exit 1
fi

print_success "构建成功!"

# 检查APK文件是否存在
if [ ! -f "$APK_OUTPUT_PATH" ]; then
    print_error "APK文件不存在: $APK_OUTPUT_PATH"
    
    # 尝试查找其他可能的路径
    print_warning "尝试查找其他可能的APK文件路径..."
    POSSIBLE_PATHS=(
        "./actionRecoder/build/outputs/apk/release/actionRecoder-release-unsigned.apk"
        "./actionRecoder/build/outputs/apk/release/actionRecoder-release-signed.apk"
    )
    
    for path in "${POSSIBLE_PATHS[@]}"; do
        if [ -f "$path" ]; then
            print_info "找到APK文件: $path"
            APK_OUTPUT_PATH="$path"
            break
        fi
    done
    
    # 如果仍然找不到，尝试查找任何APK文件
    if [ ! -f "$APK_OUTPUT_PATH" ]; then
        print_warning "尝试查找任何APK文件..."
        POSSIBLE_APKS=$(find ./actionRecoder/build/outputs/apk -name "*.apk")
        if [ -z "$POSSIBLE_APKS" ]; then
            print_error "找不到任何APK文件"
            exit 1
        fi
        
        # 使用找到的第一个APK文件
        APK_OUTPUT_PATH=$(echo "$POSSIBLE_APKS" | head -n 1)
        print_info "找到APK文件: $APK_OUTPUT_PATH"
    fi
fi

# 复制APK文件到输出目录
print_info "复制APK文件到输出目录: $OUTPUT_DIR/$APK_NAME"
cp "$APK_OUTPUT_PATH" "$OUTPUT_DIR/$APK_NAME"
if [ $? -ne 0 ]; then
    print_error "复制到输出目录失败!"
    exit 1
fi

print_success "所有操作已完成!"
print_success "APK文件已生成: $APK_NAME"
print_success "构建时间: $DATE_TIME"
print_info "APK位置: $OUTPUT_DIR/$APK_NAME"

# 显示APK文件信息
print_info "APK文件信息:"
ls -lh "$OUTPUT_DIR/$APK_NAME"

exit 0
