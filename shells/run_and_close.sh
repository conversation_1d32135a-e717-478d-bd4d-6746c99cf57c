#!/bin/bash

# 包装脚本，执行原始脚本并在完成后自动关闭终端

# 设置颜色输出
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}开始执行发布流程...${NC}"
echo -e "${YELLOW}此脚本将在执行完成后自动关闭终端${NC}"

# 执行原始脚本
/Users/<USER>/Projects/android_webview_ad_sdk/shells/0_publish_all.sh

# 获取原始脚本的退出状态
EXIT_CODE=$?

if [ $EXIT_CODE -eq 0 ]; then
    echo -e "${GREEN}发布流程执行成功，3秒后自动关闭终端...${NC}"
else
    echo -e "${YELLOW}发布流程执行失败，退出代码: $EXIT_CODE，3秒后自动关闭终端...${NC}"
fi

# 等待3秒，让用户有时间看到最终结果
sleep 3

# 退出终端
exit 0
