#!/bin/bash

# 固定目标目录
TARGET_DIRS=(
  "/Users/<USER>/Projects/android_webview_ad_sdk/lib_webview_ad_sdk"
  "/Users/<USER>/Projects/android_webview_ad_sdk/lib_webview_ad_plugin"
)

for DIR in "${TARGET_DIRS[@]}"; do
  echo "📁 Scanning: $DIR"

  # 查找 .java、.kt、.xml 文件
  find "$DIR" -type f \( -name "*.java" -o -name "*.kt" -o -name "*.xml" \) | while read -r file; do
    echo "🔧 Processing: $file"

    tmpfile=$(mktemp)

    # 使用 Perl 查找并替换含中文的字符串
    perl -CSD -pe '
      s/"([^"\n]*[\x{4e00}-\x{9fff}]+[^"\n]*)"/"todo_trans$1"/g
    ' "$file" > "$tmpfile" && mv "$tmpfile" "$file"
  done
done

echo "✅ All done. Chinese strings in quotes are now prefixed with 'todo'."