#!/bin/bash

# 设置工作目录为项目根目录
SCRIPT_DIR="$(dirname "$0")"
cd "$SCRIPT_DIR/.."

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# 显示当前工作目录
echo -e "${GREEN}当前工作目录: $(pwd)${NC}"

# 设置 Demo 目录路径
DEMO_DIR="./publish_sdk/demo"
if [ ! -d "$DEMO_DIR" ]; then
    echo -e "${RED}Demo 目录不存在: $DEMO_DIR${NC}"
    exit 1
fi

# 使用 gradlew 编译构建 release 版本的 app 并运行
echo -e "${YELLOW}正在使用 gradlew 编译构建 release 版本的 app 并运行...${NC}"

# 进入 demo 目录
cd "$DEMO_DIR"
echo -e "${GREEN}已进入 Demo 目录: $(pwd)${NC}"

# 检查 gradlew 是否存在
if [ ! -f "./gradlew" ]; then
    echo -e "${RED}gradlew 文件不存在${NC}"
    exit 1
fi

# 确保 gradlew 有执行权限
chmod +x ./gradlew

# 清理项目
echo -e "${YELLOW}清理项目...${NC}"
./gradlew clean
if [ $? -ne 0 ]; then
    echo -e "${RED}清理项目失败${NC}"
    exit 1
fi

# 构建 release 版本
echo -e "${YELLOW}构建 release 版本...${NC}"
./gradlew assembleRelease
if [ $? -ne 0 ]; then
    echo -e "${RED}构建 release 版本失败${NC}"
    exit 1
fi

# 获取生成的 APK 路径
RELEASE_APK_PATH="./app/build/outputs/apk/release/app-release.apk"
if [ ! -f "$RELEASE_APK_PATH" ]; then
    echo -e "${YELLOW}找不到已签名的 release APK，尝试查找其他可能的 APK 文件...${NC}"

    # 尝试其他可能的路径
    POSSIBLE_PATHS=(
        "./app/build/outputs/apk/release/app-release-unsigned.apk"
        "./app/build/outputs/apk/release/app-release-signed.apk"
    )

    for path in "${POSSIBLE_PATHS[@]}"; do
        if [ -f "$path" ]; then
            RELEASE_APK_PATH="$path"
            echo -e "${GREEN}找到 APK 文件: $RELEASE_APK_PATH${NC}"
            break
        fi
    done

    # 如果仍然找不到，尝试查找任何 APK 文件
    if [ ! -f "$RELEASE_APK_PATH" ]; then
        echo -e "${YELLOW}尝试查找任何 APK 文件...${NC}"
        POSSIBLE_APKS=$(find ./app/build/outputs/apk -name "*.apk")
        if [ -z "$POSSIBLE_APKS" ]; then
            echo -e "${RED}找不到任何 APK 文件${NC}"
            exit 1
        fi

        # 使用找到的第一个 APK 文件
        RELEASE_APK_PATH=$(echo "$POSSIBLE_APKS" | head -n 1)
        echo -e "${GREEN}找到 APK 文件: $RELEASE_APK_PATH${NC}"
    fi
fi

# 使用 adb 安装 APK
echo -e "${YELLOW}使用 adb 安装 release APK: $RELEASE_APK_PATH${NC}"

# 检查 APK 路径是否包含 "unsigned"
if [[ "$RELEASE_APK_PATH" == *"unsigned"* ]]; then
    echo -e "${YELLOW}检测到未签名的 APK，使用 -t 参数安装...${NC}"
    adb install -r -t "$RELEASE_APK_PATH"
else
    echo -e "${YELLOW}检测到已签名的 APK，正常安装...${NC}"
    adb install -r "$RELEASE_APK_PATH"
fi

if [ $? -ne 0 ]; then
    echo -e "${RED}安装 APK 失败，尝试使用其他参数...${NC}"
    # 尝试使用另一种方式安装
    if [[ "$RELEASE_APK_PATH" == *"unsigned"* ]]; then
        adb install -r "$RELEASE_APK_PATH"
    else
        adb install -r -t "$RELEASE_APK_PATH"
    fi

    if [ $? -ne 0 ]; then
        echo -e "${RED}安装 APK 失败${NC}"
        exit 1
    fi
fi
echo -e "${GREEN}成功安装 release APK${NC}"

# 启动应用
echo -e "${YELLOW}启动应用...${NC}"

# 直接使用指定的命令启动应用
echo -e "${YELLOW}使用指定命令启动应用...${NC}"
adb shell am start -n ai.ad.webview.sdk.demo/ai.ad.webview.plugin.demo.MainActivity

if [ $? -ne 0 ]; then
    echo -e "${RED}启动应用失败${NC}"
    echo -e "${YELLOW}请手动启动应用${NC}"
else
    echo -e "${GREEN}启动命令已执行${NC}"
    # 等待一段时间，确保应用有足够的时间启动
    sleep 2
fi

echo -e "${GREEN}应用已成功构建、安装并启动${NC}"

# 返回到原始目录
cd - > /dev/null

exit 0
