#!/bin/bash

# 设置环境变量
export ANDROID_HOME=$HOME/Library/Android/sdk
export PATH=$PATH:$ANDROID_HOME/tools:$ANDROID_HOME/platform-tools
# publish_dex_sdk.sh
# 将 lib_webview_ad_plugin 编译成 dex 文件，并放到 lib_webview_ad_sdk 的 assets 目录

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# 设置工作目录为项目根目录
SCRIPT_DIR="$(dirname "$0")"
cd "$SCRIPT_DIR/.."

# 显示当前工作目录
echo -e "${GREEN}Current working directory: $(pwd)${NC}"

# 检查 Android SDK 环境变量
if [ -z "$ANDROID_HOME" ]; then
    echo -e "${RED}Error: ANDROID_HOME environment variable is not set.${NC}"
    echo -e "${YELLOW}Please set ANDROID_HOME to your Android SDK path.${NC}"
    exit 1
fi

# 查找最新的 build-tools 版本
BUILD_TOOLS_DIR="$ANDROID_HOME/build-tools"
if [ ! -d "$BUILD_TOOLS_DIR" ]; then
    echo -e "${RED}Error: Build tools directory not found at $BUILD_TOOLS_DIR${NC}"
    exit 1
fi

# 查找合适的 build-tools 版本
# 首先尝试找到 28.0.3 版本（最后一个包含 dx 工具的稳定版本）
if [ -d "$BUILD_TOOLS_DIR/28.0.3" ]; then
    BUILD_TOOLS_PATH="$BUILD_TOOLS_DIR/28.0.3"
    echo -e "${GREEN}Using build tools version: 28.0.3 (contains dx tool)${NC}"
    USE_DX=true
else
    # 如果没有 28.0.3，则使用最新版本
    LATEST_BUILD_TOOLS=$(ls -1 "$BUILD_TOOLS_DIR" | sort -V | tail -1)
    if [ -z "$LATEST_BUILD_TOOLS" ]; then
        echo -e "${RED}Error: No build tools version found.${NC}"
        exit 1
    fi

    BUILD_TOOLS_PATH="$BUILD_TOOLS_DIR/$LATEST_BUILD_TOOLS"
    echo -e "${GREEN}Using build tools version: $LATEST_BUILD_TOOLS${NC}"

    # 检查是否有 dx 工具
    if [ -f "$BUILD_TOOLS_PATH/dx" ]; then
        USE_DX=true
    else
        # 如果没有 dx，检查是否有 d8
        if [ -f "$BUILD_TOOLS_PATH/d8" ]; then
            USE_DX=false
            echo -e "${YELLOW}dx tool not found, will use d8 instead${NC}"
        else
            echo -e "${RED}Error: Neither dx nor d8 tool found in $BUILD_TOOLS_PATH${NC}"
            exit 1
        fi
    fi
fi

# 创建临时目录
TEMP_DIR="./build/temp_dex"
mkdir -p "$TEMP_DIR"
echo -e "${GREEN}Created temporary directory: $TEMP_DIR${NC}"

# 清理之前的构建
echo -e "${YELLOW}Cleaning previous build...${NC}"
./gradlew clean
if [ $? -ne 0 ]; then
    echo -e "${RED}Error: Failed to clean project.${NC}"
    exit 1
fi

# 编译 lib_webview_ad_plugin 模块
echo -e "${YELLOW}Building lib_webview_ad_plugin module...${NC}"
./gradlew :lib_webview_ad_plugin:assembleRelease
if [ $? -ne 0 ]; then
    echo -e "${RED}Error: Failed to build lib_webview_ad_plugin.${NC}"
    exit 1
fi

# 查找编译后的 classes.jar 文件
# 尝试多个可能的路径
POSSIBLE_PATHS=(
    "./lib_webview_ad_plugin/build/intermediates/runtime_library_classes/release/classes.jar"
    "./lib_webview_ad_plugin/build/intermediates/aar_main_jar/release/classes.jar"
    "./lib_webview_ad_plugin/build/intermediates/compile_library_classes_jar/release/classes.jar"
    "./lib_webview_ad_plugin/build/intermediates/compile_r_class_jar/release/R.jar"
    "./lib_webview_ad_plugin/build/intermediates/packaged_classes/release/classes.jar"
    "./lib_webview_ad_plugin/build/outputs/aar/lib_webview_ad_plugin-release.aar"
)

CLASSES_JAR=""
for path in "${POSSIBLE_PATHS[@]}"; do
    if [ -f "$path" ]; then
        echo -e "${GREEN}Found classes at: $path${NC}"
        CLASSES_JAR="$path"
        break
    fi
done

if [ -z "$CLASSES_JAR" ]; then
    echo -e "${RED}Error: Could not find classes.jar or equivalent in any of the expected locations${NC}"
    echo -e "${YELLOW}Searching for any jar files in the build directory...${NC}"
    find ./lib_webview_ad_plugin/build -name "*.jar" | grep -v "javadoc" | grep -v "sources"
    echo -e "${YELLOW}Searching for any aar files in the build directory...${NC}"
    find ./lib_webview_ad_plugin/build -name "*.aar"
    exit 1
fi

# 如果找到的是 AAR 文件，需要解压缩
if [[ "$CLASSES_JAR" == *".aar" ]]; then
    echo -e "${YELLOW}Found AAR file instead of JAR. Extracting...${NC}"
    mkdir -p "$TEMP_DIR/aar_extract"
    unzip -q "$CLASSES_JAR" -d "$TEMP_DIR/aar_extract"
    if [ -f "$TEMP_DIR/aar_extract/classes.jar" ]; then
        CLASSES_JAR="$TEMP_DIR/aar_extract/classes.jar"
        echo -e "${GREEN}Extracted classes.jar from AAR${NC}"
    else
        echo -e "${RED}Error: Could not find classes.jar in the AAR file${NC}"
        exit 1
    fi
fi

# 复制 classes.jar 到临时目录
cp "$CLASSES_JAR" "$TEMP_DIR/"
echo -e "${GREEN}Copied classes.jar to $TEMP_DIR${NC}"

# 使用 dx 或 d8 工具将 classes.jar 转换为 dex 文件
echo -e "${YELLOW}Converting classes.jar to dex...${NC}"
if [ "$USE_DX" = true ]; then
    # 使用 dx 工具
    "$BUILD_TOOLS_PATH/dx" --dex --output="$TEMP_DIR/mmkv_core.so" "$TEMP_DIR/classes.jar"
else
    # 使用 d8 工具
    # 创建临时目录用于 d8 输出
    D8_OUTPUT_DIR="$TEMP_DIR/d8_output"
    mkdir -p "$D8_OUTPUT_DIR"

    # 使用 d8 转换
    "$BUILD_TOOLS_PATH/d8" "$TEMP_DIR/classes.jar" --output "$D8_OUTPUT_DIR"

    # d8 输出的是 classes.dex，需要重命名
    if [ -f "$D8_OUTPUT_DIR/classes.dex" ]; then
        cp "$D8_OUTPUT_DIR/classes.dex" "$TEMP_DIR/mmkv_core.so"
    else
        echo -e "${RED}Error: d8 did not generate classes.dex file${NC}"
        exit 1
    fi
fi

if [ $? -ne 0 ]; then
    echo -e "${RED}Error: Failed to convert classes.jar to dex.${NC}"
    exit 1
fi

# 确保 assets 目录存在
ASSETS_DIR="./lib_webview_ad_sdk/src/main/assets"
mkdir -p "$ASSETS_DIR"

# 复制 dex 文件到 assets 目录
cp "$TEMP_DIR/mmkv_core.so" "$ASSETS_DIR/"
if [ $? -ne 0 ]; then
    echo -e "${RED}Error: Failed to copy dex file to assets directory.${NC}"
    exit 1
fi

echo -e "${GREEN}Successfully created dex file at $ASSETS_DIR/mmkv_core.so${NC}"

# 清理临时文件
rm -rf "$TEMP_DIR"
echo -e "${GREEN}Cleaned up temporary files.${NC}"

echo -e "${GREEN}Done! The dex file has been placed in the assets directory.${NC}"

# 更新SDK Quickstart Guide.md的依赖
echo -e "${YELLOW}Updating SDK Quickstart Guide.md dependencies...${NC}"

# 获取当前日期和时间，格式为YYYYMMDD_HHMM
CURRENT_DATETIME=$(date +"%Y%m%d_%H%M")

# 构建新的版本号
SDK_VERSION="1.1.0_$CURRENT_DATETIME"

# 更新SDK文档中的依赖版本 - 使用相对路径
SDK_DOC_PATH="./publish_sdk/SDK Quickstart Guide.md"

if [ -f "$SDK_DOC_PATH" ]; then
    echo -e "${GREEN}Found SDK documentation file at $SDK_DOC_PATH${NC}"

    # 显示文件内容中的当前版本号，用于调试
    echo -e "${YELLOW}Current version in documentation:${NC}"
    grep -E "implementation files" "$SDK_DOC_PATH" || echo "No matching version found"

    # 直接使用文本替换方法
    # 创建新文件内容
    NEW_CONTENT=$(cat "$SDK_DOC_PATH" | perl -pe "s/implementation files\\('libs\\/wsk_[0-9.]+_[0-9]+_[0-9]+\\.aar'\\)/implementation files('libs\\/wsk_$SDK_VERSION.aar')/g")

    # 将新内容写入文件
    echo "$NEW_CONTENT" > "$SDK_DOC_PATH"

    # 验证更新是否成功
    echo -e "${YELLOW}Updated version in documentation:${NC}"
    grep -E "implementation files" "$SDK_DOC_PATH" || echo "No matching version found after update"

    echo -e "${GREEN}Updated SDK dependencies in documentation to version $SDK_VERSION${NC}"
else
    echo -e "${RED}Error: SDK documentation file not found at $SDK_DOC_PATH${NC}"
    # 尝试查找文件
    echo -e "${YELLOW}Searching for SDK Quickstart Guide.md...${NC}"
    find . -name "SDK Quickstart Guide.md"
fi
