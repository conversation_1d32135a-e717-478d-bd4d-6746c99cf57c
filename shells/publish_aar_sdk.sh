#!/bin/bash

# 脚本用于打包 lib_webview_ad_sdk 并发布 AAR 文件
# 作者: Shawn
# 日期: 2023-05-15

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的信息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 获取版本号
VERSION_FILE="lib_webview_ad_plugin/src/main/java/ai/ad/webview/plugin/ProxyWSKSDK.java"
if [ ! -f "$VERSION_FILE" ]; then
    print_error "版本号文件不存在: $VERSION_FILE"
    exit 1
fi

# 从文件中提取版本号
VERSION=$(grep -o 'private static final String mVersion = "[^"]*"' "$VERSION_FILE" | cut -d'"' -f2)
if [ -z "$VERSION" ]; then
    print_error "无法从文件中提取版本号"
    exit 1
fi

print_info "SDK版本号: $VERSION"

# 设置AAR文件名
AAR_NAME="wsk_${VERSION}.aar"
print_info "AAR文件名: $AAR_NAME"

# 设置输出目录
BACKUP_DIR="/Users/<USER>/Projects/android_webview_ad_sdk/publish_sdk_history"
PUBLISH_DIR="/Users/<USER>/Projects/android_webview_ad_sdk/publish_sdk/demo/app/libs"
AAR_OUTPUT_PATH="lib_webview_ad_sdk/build/outputs/aar/lib_webview_ad_sdk-release.aar"

# 确保输出目录存在
mkdir -p "$BACKUP_DIR"
mkdir -p "$PUBLISH_DIR"

# 设置工作目录为项目根目录
SCRIPT_DIR="$(dirname "$0")"
cd "$SCRIPT_DIR/.."
print_info "当前工作目录: $(pwd)"

# 清理之前的构建
print_info "清理之前的构建..."
./gradlew clean

# 构建发布版AAR
print_info "构建发布版AAR..."
./gradlew :lib_webview_ad_sdk:assembleRelease

# 检查构建是否成功
if [ $? -ne 0 ]; then
    print_error "构建失败!"
    exit 1
fi

print_success "构建成功!"

# 检查AAR文件是否存在
if [ ! -f "$AAR_OUTPUT_PATH" ]; then
    print_error "AAR文件不存在: $AAR_OUTPUT_PATH"
    exit 1
fi

# 复制AAR文件到备份目录
print_info "复制AAR文件到备份目录: $BACKUP_DIR/$AAR_NAME"
cp "$AAR_OUTPUT_PATH" "$BACKUP_DIR/$AAR_NAME"
if [ $? -ne 0 ]; then
    print_error "复制到备份目录失败!"
    exit 1
fi

# 复制AAR文件到发布目录
print_info "复制AAR文件到发布目录: $PUBLISH_DIR/$AAR_NAME"
cp "$AAR_OUTPUT_PATH" "$PUBLISH_DIR/$AAR_NAME"
if [ $? -ne 0 ]; then
    print_error "复制到发布目录失败!"
    exit 1
fi

# 更新app/build.gradle中的依赖
GRADLE_FILE="/Users/<USER>/Projects/android_webview_ad_sdk/publish_sdk/demo/app/build.gradle"
print_info "更新build.gradle文件: $GRADLE_FILE"

# 检查文件是否存在
if [ ! -f "$GRADLE_FILE" ]; then
    print_error "build.gradle文件不存在: $GRADLE_FILE"
    exit 1
fi

# 备份原始文件
cp "$GRADLE_FILE" "${GRADLE_FILE}.bak"

# 更新build.gradle文件
if grep -q "implementation files('libs/wsk_" "$GRADLE_FILE"; then
    # 如果已经存在wsk AAR依赖，替换文件名
    sed -i '' "s/implementation files('libs\/wsk_[^']*')/implementation files('libs\/${AAR_NAME}')/g" "$GRADLE_FILE"
    print_info "更新了AAR依赖引用"
elif grep -q "implementation files('libs/webtest_" "$GRADLE_FILE"; then
    # 如果存在webtest AAR依赖，替换文件名
    sed -i '' "s/implementation files('libs\/webtest_[^']*')/implementation files('libs\/${AAR_NAME}')/g" "$GRADLE_FILE"
    print_info "更新了AAR依赖引用"
elif grep -q "implementation project(':lib_webview_ad_sdk')" "$GRADLE_FILE"; then
    # 如果使用的是项目依赖，注释掉项目依赖并添加AAR依赖
    sed -i '' "s/implementation project(':lib_webview_ad_sdk')/\/\/implementation project(':lib_webview_ad_sdk')\n    implementation files('libs\/${AAR_NAME}')/g" "$GRADLE_FILE"
    print_info "替换了项目依赖为AAR依赖"
else
    # 如果没有找到任何依赖，添加新的AAR依赖
    sed -i '' "/dependencies {/a\\
    implementation files('libs\/${AAR_NAME}')
" "$GRADLE_FILE"
    print_info "添加了新的AAR依赖"
fi

# 更新SDK快速入门指南
GUIDE_FILE="/Users/<USER>/Projects/android_webview_ad_sdk/publish_sdk/SDK Quickstart Guide.md"
print_info "更新SDK快速入门指南: $GUIDE_FILE"

# 检查文件是否存在
if [ ! -f "$GUIDE_FILE" ]; then
    print_warning "SDK快速入门指南文件不存在: $GUIDE_FILE"
else
    # 备份原始文件
    cp "$GUIDE_FILE" "${GUIDE_FILE}.bak"

    # 更新文档中的AAR引用
    if grep -q "implementation files('libs/wsk_" "$GUIDE_FILE"; then
        sed -i '' "s/implementation files('libs\/wsk_[^']*')/implementation files('libs\/${AAR_NAME}')/g" "$GUIDE_FILE"
    elif grep -q "implementation files('libs/webtest_" "$GUIDE_FILE"; then
        sed -i '' "s/implementation files('libs\/webtest_[^']*')/implementation files('libs\/${AAR_NAME}')/g" "$GUIDE_FILE"
    fi

    # 移除对wskcore的引用
    if grep -q "implementation files('libs/wskcore_" "$GUIDE_FILE"; then
        sed -i '' "/implementation files('libs\/wskcore_[^']*')/d" "$GUIDE_FILE"
        print_info "移除了wskcore AAR依赖引用"
    fi

    print_info "SDK快速入门指南已更新"
fi

print_success "所有操作已完成!"
print_success "AAR文件已生成: $AAR_NAME"
print_success "版本号: $VERSION"
print_info "备份位置: $BACKUP_DIR/$AAR_NAME"
print_info "发布位置: $PUBLISH_DIR/$AAR_NAME"
