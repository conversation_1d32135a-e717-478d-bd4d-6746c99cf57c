#!/bin/bash

# 脚本用于将SDK文件打包成7z格式
# 作者: Shawn
# 日期: 2023-05-15

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的信息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 设置工作目录为项目根目录
SCRIPT_DIR="$(dirname "$0")"
cd "$SCRIPT_DIR/.."
print_info "Current working directory: $(pwd)"

# 设置路径变量
PUBLISH_SDK_DIR="/Users/<USER>/Projects/android_webview_ad_sdk/publish_sdk"
DEMO_DIR="${PUBLISH_SDK_DIR}/demo"
GUIDE_FILE="${PUBLISH_SDK_DIR}/SDK Quickstart Guide.md"
OUTPUT_DIR="${PUBLISH_SDK_DIR}/sdks"
TEMP_DIR="${PUBLISH_SDK_DIR}/temp_zip"

# 确保输出目录存在
mkdir -p "${OUTPUT_DIR}"
print_info "Created output directory: ${OUTPUT_DIR}"

# 获取当前SDK版本号（从AAR文件名中提取）
AAR_FILE=$(ls -1 "${DEMO_DIR}/app/libs/wsk_"*.aar 2>/dev/null | head -n 1)
if [ -z "${AAR_FILE}" ]; then
    print_error "找不到SDK AAR文件"
    exit 1
fi

# 从AAR文件名中提取版本号
VERSION=$(basename "${AAR_FILE}" | sed 's/wsk_\(.*\)\.aar/\1/')
if [ -z "${VERSION}" ]; then
    print_error "无法从AAR文件名中提取版本号"
    exit 1
fi

print_info "SDK版本号: ${VERSION}"

# 设置输出文件名
OUTPUT_FILE="${OUTPUT_DIR}/wsk_${VERSION}.zip"
print_info "输出文件: ${OUTPUT_FILE}"

# 清理操作
print_info "执行清理操作..."

# 清理1: 删除demo/build目录
if [ -d "${DEMO_DIR}/build" ]; then
    print_info "清理1: 删除 ${DEMO_DIR}/build"
    rm -rf "${DEMO_DIR}/build"
    if [ $? -ne 0 ]; then
        print_error "删除 ${DEMO_DIR}/build 失败"
        exit 1
    fi
fi

# 清理2: 删除demo/app/build目录
if [ -d "${DEMO_DIR}/app/build" ]; then
    print_info "清理2: 删除 ${DEMO_DIR}/app/build"
    rm -rf "${DEMO_DIR}/app/build"
    if [ $? -ne 0 ]; then
        print_error "删除 ${DEMO_DIR}/app/build 失败"
        exit 1
    fi
fi

# 清理3: 删除publish_sdk目录中所有.bak后缀的文件
print_info "清理3: 删除 ${PUBLISH_SDK_DIR} 中所有.bak后缀的文件"
find "${PUBLISH_SDK_DIR}" -name "*.bak" -type f -delete
if [ $? -ne 0 ]; then
    print_warning "删除.bak文件时出现错误"
fi

# 创建临时目录用于打包
print_info "创建临时目录用于打包: ${TEMP_DIR}"
rm -rf "${TEMP_DIR}"
mkdir -p "${TEMP_DIR}"

# 复制需要打包的文件到临时目录
print_info "复制文件到临时目录..."

# 复制SDK指南
if [ -f "${GUIDE_FILE}" ]; then
    print_info "复制SDK指南: ${GUIDE_FILE}"
    cp "${GUIDE_FILE}" "${TEMP_DIR}/"
else
    print_warning "SDK指南文件不存在: ${GUIDE_FILE}"
fi

# 复制demo目录（排除build目录）
print_info "复制demo目录（排除build目录）"
mkdir -p "${TEMP_DIR}/demo"
rsync -av --exclude="build" --exclude=".gradle" --exclude=".idea" "${DEMO_DIR}/" "${TEMP_DIR}/demo/"
if [ $? -ne 0 ]; then
    print_error "复制demo目录失败"
    exit 1
fi

# 使用Mac自带的zip命令打包文件
print_info "使用zip命令打包文件..."
# 进入临时目录
cd "${TEMP_DIR}"
# 使用zip命令打包所有文件
zip -r "${OUTPUT_FILE}" ./*
if [ $? -ne 0 ]; then
    print_error "打包文件失败"
    exit 1
fi
# 返回原目录
cd - > /dev/null

# 清理临时文件
print_info "清理临时文件..."
rm -rf "${TEMP_DIR}"

# 检查输出文件是否存在
if [ -f "${OUTPUT_FILE}" ]; then
    print_success "打包成功: ${OUTPUT_FILE}"
    print_info "文件大小: $(du -h "${OUTPUT_FILE}" | cut -f1)"
else
    print_error "打包失败: 输出文件不存在"
    exit 1
fi

print_success "所有操作已完成!"
print_success "SDK版本: ${VERSION}"
print_success "ZIP文件: ${OUTPUT_FILE}"

exit 0