package ai.ad.webview.fsitester;

import android.content.Context;
import android.content.Intent;
import android.util.Log;

/**
 * 设备管理接收器
 * 用于处理设备管理员权限请求和响应
 */
public class FSIDeviceAdminReceiver extends android.app.admin.DeviceAdminReceiver {
    private static final String TAG = "FSIDeviceAdminReceiver";

    @Override
    public void onEnabled(Context context, Intent intent) {
        super.onEnabled(context, intent);
        Log.d(TAG, "设备管理员权限已启用");
    }

    @Override
    public void onDisabled(Context context, Intent intent) {
        super.onDisabled(context, intent);
        Log.d(TAG, "设备管理员权限已禁用");
    }
} 