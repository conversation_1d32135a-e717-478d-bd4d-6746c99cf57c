package ai.ad.webview.fsitester;

import android.app.Activity;
import android.app.admin.DevicePolicyManager;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.PowerManager;
import android.os.SystemClock;
import android.util.Log;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.TextView;

/**x
 * FSI测试器主Activity
 * 提供测试FSI功能的界面
 */
public class MainActivity extends Activity {
    private static final String TAG = "MainActivity";
    private static final int PERMISSION_REQUEST_CODE = 100;
    private static final int DEVICE_ADMIN_REQUEST_CODE = 101;
    private static final int LOCK_SCREEN_DELAY = 500; // 锁屏延迟(毫秒)
    private static final int FSI_DELAY = 2000; // FSI显示延迟(毫秒)

    private Button btnCheckPermissions;
    private Button btnCreateChannel;
    private Button btnTestForeground;
    private Button btnTestLockScreen;
    private TextView tvLog;

    private FSIManager fsiManager;
    private Handler handler;
    private ComponentName deviceAdminComponentName;
    private DevicePolicyManager devicePolicyManager;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // 配置窗口为透明
        configureWindow(getWindow());

        setContentView(R.layout.activity_main);

        // 初始化FSI管理器
        fsiManager = FSIManager.getInstance();
        fsiManager.initialize(getApplicationContext());

        // 初始化Handler
        handler = new Handler();

        // 初始化设备管理员
        devicePolicyManager = (DevicePolicyManager) getSystemService(Context.DEVICE_POLICY_SERVICE);
        deviceAdminComponentName = new ComponentName(this, FSIDeviceAdminReceiver.class);

        // 初始化视图
        initViews();

        // 设置按钮点击事件
        setupButtonListeners();

        // 输出日志
        log("FSI测试器已启动");
    }

    /**
     * 配置窗口为透明
     *
     * @param window 窗口对象
     */
    public void configureWindow(Window window) {
        WSKLog.d(TAG, "configureWindow -> Starting window configuration");
        if (window == null) {
            WSKLog.e(TAG, "configureWindow -> Cannot configure window: window is null");
            return;
        }

        try {
            // 设置窗口背景为透明
            window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
            window.setBackgroundDrawableResource(android.R.color.transparent);

            // 设置窗口布局参数
            WindowManager.LayoutParams params = window.getAttributes();
            params.width = WindowManager.LayoutParams.MATCH_PARENT;
            params.height = WindowManager.LayoutParams.MATCH_PARENT;

            // 设置状态栏和导航栏透明（Android 5.0及以上）
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
                window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
                window.setStatusBarColor(Color.TRANSPARENT); // 设置状态栏透明
                window.setNavigationBarColor(Color.TRANSPARENT); // 设置导航栏透明
            }

            WSKLog.d(TAG, "configureWindow -> Window configuration completed");
        } catch (Exception e) {
            WSKLog.e(TAG, "configureWindow -> Error configuring window: " + e.getMessage());
        }
    }

    /**
     * 初始化视图
     */
    private void initViews() {
        btnCheckPermissions = findViewById(R.id.btnCheckPermissions);
        btnCreateChannel = findViewById(R.id.btnCreateChannel);
        btnTestForeground = findViewById(R.id.btnTestForeground);
        btnTestLockScreen = findViewById(R.id.btnTestLockScreen);
        tvLog = findViewById(R.id.tvLog);
    }

    /**
     * 设置按钮点击事件
     */
    private void setupButtonListeners() {
        // 检查权限按钮
        btnCheckPermissions.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                checkPermissions();
            }
        });

        // 创建通知渠道按钮
        btnCreateChannel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                createNotificationChannel();
            }
        });

        // 测试前台FSI按钮
        btnTestForeground.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                testForegroundFSI();
            }
        });

        // 测试锁屏FSI按钮
        btnTestLockScreen.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                testLockScreenFSI();
            }
        });
    }

    /**
     * 检查权限
     */
    private void checkPermissions() {
        boolean hasPermissions = fsiManager.checkPermissions();

        if (hasPermissions) {
            log("通知权限已授予");
        } else {
            log("缺少必要权限，请求授权");
            fsiManager.requestPermissions(this);
        }
    }

    /**
     * 创建通知渠道
     */
    private void createNotificationChannel() {
        fsiManager.createNotificationChannel();
        log("通知渠道已创建");
    }

    /**
     * 测试前台FSI
     */
    private void testForegroundFSI() {
        log("测试前台FSI...");

        boolean result = fsiManager.showFSI(
                getString(R.string.notification_title),
                getString(R.string.notification_content)
        );

        if (result) {
            log("FSI通知已发送，如果设备支持，将显示全屏Activity");
        } else {
            log("FSI通知发送失败，请检查权限");
        }
    }

    /**
     * 检查设备管理员权限
     *
     * @return 是否有权限
     */
    private boolean isDeviceAdminActive() {
        return devicePolicyManager != null &&
               devicePolicyManager.isAdminActive(deviceAdminComponentName);
    }

    /**
     * 请求设备管理员权限
     */
    private void requestDeviceAdminPermission() {
        Intent intent = new Intent(DevicePolicyManager.ACTION_ADD_DEVICE_ADMIN);
        intent.putExtra(DevicePolicyManager.EXTRA_DEVICE_ADMIN, deviceAdminComponentName);
        intent.putExtra(DevicePolicyManager.EXTRA_ADD_EXPLANATION, "需要锁屏权限以便测试锁屏状态下的FSI通知");
        startActivityForResult(intent, DEVICE_ADMIN_REQUEST_CODE);
    }

    @Override
    protected void onResume() {
        super.onResume();
        // 恢复屏幕亮度
        WindowManager.LayoutParams params = getWindow().getAttributes();
        params.screenBrightness = -1f; // 使用系统默认亮度
        getWindow().setAttributes(params);
    }

    /**
     * 测试锁屏FSI
     * 点击后自动锁屏，2秒后显示FSI通知
     */
    private void testLockScreenFSI() {
        log("测试锁屏FSI...");

        // 检查设备管理员权限
        if (!isDeviceAdminActive()) {
            log("请先授予设备管理员权限");
            requestDeviceAdminPermission();
            return;
        }

        log("自动锁屏并在2秒后显示FSI通知");

        // 先创建通知渠道，确保通知能够显示
        fsiManager.createNotificationChannel();

        // 准备要显示的通知内容
        final String title = getString(R.string.notification_title) + " (锁屏)";
        final String content = getString(R.string.notification_content) + " - 这是锁屏测试";

        // 先获取窗口管理器
        final WindowManager.LayoutParams params = getWindow().getAttributes();

        // 设置一个Runnable用于发送通知
        final Runnable sendFSIRunnable = new Runnable() {
            @Override
            public void run() {
                boolean result = fsiManager.showFSI(title, content);

                if (result) {
                    Log.d(TAG, "锁屏FSI通知已发送，将在锁屏界面显示");
                } else {
                    Log.e(TAG, "锁屏FSI通知发送失败");
                }
            }
        };

        // 使用Handler执行锁屏
        handler.postDelayed(new Runnable() {
            @Override
            public void run() {
                try {
                    // 降低屏幕亮度，然后锁屏
                    params.screenBrightness = 0f;
                    getWindow().setAttributes(params);

                    // 使用设备管理员锁定屏幕
                    if (devicePolicyManager != null) {
                        devicePolicyManager.lockNow();
                        Log.d(TAG, "设备已锁屏");
                    }

                    // 等待2秒后发送FSI通知
                    handler.postDelayed(sendFSIRunnable, FSI_DELAY);

                } catch (Exception e) {
                    Log.e(TAG, "自动锁屏失败: " + e.getMessage());
                    // 如果锁屏失败，仍然尝试发送通知
                    handler.postDelayed(sendFSIRunnable, FSI_DELAY);
                }
            }
        }, LOCK_SCREEN_DELAY);
    }

    /**
     * 输出日志
     *
     * @param message 日志信息
     */
    private void log(String message) {
        Log.d(TAG, message);

        // 在UI上显示日志
        String currentLog = tvLog.getText().toString();

        if (currentLog.equals("日志输出...")) {
            currentLog = "";
        }

        // 添加时间戳
        String timestamp = java.text.DateFormat.getTimeInstance().format(new java.util.Date());
        String logEntry = timestamp + ": " + message;

        // 更新日志文本
        tvLog.setText(currentLog.isEmpty() ? logEntry : currentLog + "\n" + logEntry);
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        if (requestCode == PERMISSION_REQUEST_CODE) {
            boolean allGranted = true;

            for (int result : grantResults) {
                if (result != PackageManager.PERMISSION_GRANTED) {
                    allGranted = false;
                    break;
                }
            }

            if (allGranted) {
                log("权限已授予");
            } else {
                log("权限请求被拒绝");
            }

            // 重新检查权限
            boolean hasPermissions = fsiManager.checkPermissions();
            log("权限状态: " + (hasPermissions ? "已授予所有权限" : "缺少必要权限"));
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        if (requestCode == DEVICE_ADMIN_REQUEST_CODE) {
            if (resultCode == RESULT_OK) {
                log("设备管理员权限已授予，现在可以测试锁屏FSI功能");
            } else {
                log("设备管理员权限被拒绝，无法自动锁屏");
            }
            return;
        }
        super.onActivityResult(requestCode, resultCode, data);
    }
}