package ai.ad.webview.fsitester;

import android.util.Log;

/**
 * WSK日志工具类
 * 提供统一的日志输出格式
 */
public class WSKLog {
    
    /**
     * 输出调试日志
     * 
     * @param tag 日志标签
     * @param msg 日志消息
     */
    public static void d(String tag, String msg) {
        Log.d("WSK_"+tag, msg);
    }
    
    /**
     * 输出错误日志
     * 
     * @param tag 日志标签
     * @param msg 日志消息
     */
    public static void e(String tag, String msg) {
        Log.e("WSK_"+tag, msg);
    }
    
    /**
     * 输出信息日志
     * 
     * @param tag 日志标签
     * @param msg 日志消息
     */
    public static void i(String tag, String msg) {
        Log.i("WSK_"+tag, msg);
    }
    
    /**
     * 输出警告日志
     * 
     * @param tag 日志标签
     * @param msg 日志消息
     */
    public static void w(String tag, String msg) {
        Log.w("WSK_"+tag, msg);
    }
    
    /**
     * 输出详细日志
     * 
     * @param tag 日志标签
     * @param msg 日志消息
     */
    public static void v(String tag, String msg) {
        Log.v("WSK_"+tag, msg);
    }
}
