<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent">

    <!-- 标题栏 -->
    <LinearLayout
        android:id="@+id/layoutTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp"
        android:background="#3F51B5">

        <TextView
            android:id="@+id/tvTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="FSI测试通知"
            android:textColor="@android:color/white"
            android:textSize="20sp"
            android:textStyle="bold"
            android:gravity="center"
            android:layout_marginBottom="8dp" />

        <TextView
            android:id="@+id/tvContent"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="这是一条来自FSI Tester的全屏通知"
            android:textColor="@android:color/white"
            android:textSize="14sp"
            android:gravity="center" />
    </LinearLayout>

    <!-- WebView区域 -->
    <WebView
        android:id="@+id/webView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/layoutTitle"
        android:layout_above="@+id/layoutButtons" />

    <!-- 底部按钮区域 -->
    <LinearLayout
        android:id="@+id/layoutButtons"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:orientation="horizontal"
        android:padding="16dp"
        android:gravity="center"
        android:background="#3F51B5">

        <Button
            android:id="@+id/btnClose"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/close"
            android:textColor="@android:color/white"
            android:background="#E91E63" />
    </LinearLayout>

    <!-- 关闭按钮 -->
    <ImageButton
        android:id="@+id/ibClose"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_alignParentTop="true"
        android:layout_alignParentEnd="true"
        android:layout_margin="16dp"
        android:background="@android:color/transparent"
        android:src="@android:drawable/ic_menu_close_clear_cancel"
        android:tint="@android:color/white"
        android:contentDescription="关闭" />

</RelativeLayout>