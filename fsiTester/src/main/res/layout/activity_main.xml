<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp"
    tools:context=".MainActivity">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="FSI 测试工具"
        android:textSize="24sp"
        android:textStyle="bold"
        android:gravity="center"
        android:layout_marginBottom="24dp" />

    <Button
        android:id="@+id/btnCheckPermissions"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/check_permissions"
        android:layout_marginBottom="16dp" />

    <Button
        android:id="@+id/btnCreateChannel"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/create_channel"
        android:layout_marginBottom="16dp" />

    <Button
        android:id="@+id/btnTestForeground"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/test_foreground"
        android:layout_marginBottom="16dp" />

    <Button
        android:id="@+id/btnTestLockScreen"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/test_lock_screen"
        android:layout_marginBottom="16dp" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <TextView
            android:id="@+id/tvLog"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="8dp"
            android:background="#F5F5F5"
            android:text="日志输出..." />
    </ScrollView>

</LinearLayout> 