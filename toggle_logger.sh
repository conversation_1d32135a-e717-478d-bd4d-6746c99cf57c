#!/bin/bash

# 定义颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# 定义文件路径
SDK_FILE="lib_webview_ad_sdk/src/main/java/ai/ad/webview/sdk/WSKSDK.java"
BACKUP_FILE="${SDK_FILE}.bak"

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO] $1${NC}"
}

log_warn() {
    echo -e "${YELLOW}[WARNING] $1${NC}"
}

log_error() {
    echo -e "${RED}[ERROR] $1${NC}"
}

# 检查文件是否存在
if [ ! -f "$SDK_FILE" ]; then
    log_error "WSKSDK.java 文件不存在: $SDK_FILE"
    exit 1
fi

# 创建备份
cp "$SDK_FILE" "$BACKUP_FILE"
if [ $? -ne 0 ]; then
    log_error "无法创建备份文件"
    exit 1
fi
log_info "已创建备份文件: $BACKUP_FILE"

# 注释掉日志代码
log_info "正在注释掉日志注入代码..."
sed -i '' 's/WSKLog.enable();/\/\/WSKLog.enable();/g' "$SDK_FILE"
sed -i '' 's/_instance.injectLogger(logger);/\/\/_instance.injectLogger(logger);/g' "$SDK_FILE"

# 检查是否成功修改
if grep -q "//WSKLog.enable();" "$SDK_FILE" && grep -q "//_instance.injectLogger(logger);" "$SDK_FILE"; then
    log_info "日志注入代码已成功注释"
else
    log_error "注释日志注入代码失败"
    # 恢复备份
    cp "$BACKUP_FILE" "$SDK_FILE"
    rm "$BACKUP_FILE"
    exit 1
fi

# 执行脚本
log_info "日志已禁用，现在可以执行您的脚本..."
echo "请输入要执行的脚本命令，或直接按回车继续:"
read script_command

if [ ! -z "$script_command" ]; then
    log_info "执行命令: $script_command"
    eval "$script_command"
    SCRIPT_RESULT=$?
    if [ $SCRIPT_RESULT -eq 0 ]; then
        log_info "脚本执行成功"
    else
        log_warn "脚本执行返回代码: $SCRIPT_RESULT"
    fi
else
    log_info "未提供脚本命令，继续恢复日志..."
fi

# 恢复原始代码
log_info "正在恢复日志注入代码..."
sed -i '' 's/\/\/WSKLog.enable();/WSKLog.enable();/g' "$SDK_FILE"
sed -i '' 's/\/\/_instance.injectLogger(logger);/_instance.injectLogger(logger);/g' "$SDK_FILE"

# 检查是否成功恢复
if grep -q "WSKLog.enable();" "$SDK_FILE" && grep -q "_instance.injectLogger(logger);" "$SDK_FILE"; then
    log_info "日志注入代码已成功恢复"
    # 删除备份
    rm "$BACKUP_FILE"
    log_info "备份文件已删除"
else
    log_error "恢复日志注入代码失败，使用备份文件恢复"
    # 恢复备份
    cp "$BACKUP_FILE" "$SDK_FILE"
    rm "$BACKUP_FILE"
fi

log_info "操作完成"
